/*
 * @Author: <PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON>@tengen.com.cn
 * @Date: 2023-06-20 00:57:28
 * @LastEditors: ji<PERSON><PERSON>jie <EMAIL>
 * @LastEditTime: 2023-08-17 16:20:32
 * @FilePath: \ml307_dcln - 副本 (2)\custom\custom_main\inc\topic_im20.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef TOPIC_IM20_H
#define TOPIC_IM20_H
#include "aiot_mqtt_api.h"
/* --------------------topic定义-------------------------*/
//发布
#define TOPIC_PUBLISH_IM20_RTDATA "thing/%s/attribute/im20"

/* --------------------字段定义-------------------------*/

// 在线状态
#define IM20_ONLINE "Online"

// # 静态属性
#define PROPERTY_IM20_ADDR   "im20Address"
#define PROPERTY_IM20_ID     "Im20id"
// # 动态属性
// ##频率
#define PROPERTY_IM20_HZ "Hz"
// ##电压
#define PROPERTY_IM20_U_V1 "PhV_phsV1"
#define PROPERTY_IM20_U_V2 "PhV_phsV2"
#define PROPERTY_IM20_U_V3 "PhV_phsV3"
#define PROPERTY_IM20_U_VLNavg "VLNavg"
#define PROPERTY_IM20_U_V12 "LinV_phsV12"
#define PROPERTY_IM20_U_V23 "LinV_phsV23"
#define PROPERTY_IM20_U_V31 "LinV_phsV31"
#define PROPERTY_IM20_U_VLin_avg "VLin_avg"
// ##状态
#define PROPERTY_IM20_POWER_OUTAGE "PowerOutage"
#define PROPERTY_IM20_GENERATOR_RUN "GeneratorRunning"

void Im20DataPublish(const int index, int connectYunFlag);
void Im20ChangedDataPublish(const int index, int connectYunFlag);
void CircuitBreakerPowerDmpSubscribe(void *handle, const aiot_mqtt_recv_t *packet, void *userdata);

#endif