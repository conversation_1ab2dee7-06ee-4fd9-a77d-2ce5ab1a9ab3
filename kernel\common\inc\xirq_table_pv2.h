/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*
 * XIRQ table for TAVOR Harbell (coupled with the interrupt list, see intc_list.h).
 * THIS FILE CAN BE #include'd ONLY ONCE
 * The table below is for: PV2/ MG/ Eshel . Using the PV2/MG definitions.
 */
	{SW_INT_SRC_0  ,  15  },
	{SW_INT_SRC_1  ,  15  },
	{SW_INT_SRC_2  ,  15  },
	{SW_INT_SRC_3  ,  15  },
	{SW_INT_SRC_4  ,  15  },
	{SW_INT_SRC_5  ,  15  },
	{SW_INT_SRC_6  ,  15  },
	{SW_INT_SRC_7  ,  15  },
	{SW_INT_SRC_8  ,  15  },
	{SW_INT_SRC_9  ,  15  },
	{SW_INT_SRC_10 ,  15  },
	{SW_INT_SRC_11 ,  15  },
	{SW_INT_SRC_12 ,  15  },
	{SW_INT_SRC_13 ,  15  },
	{SW_INT_SRC_14 ,  15  },
	{SW_INT_SRC_15 ,  15  },
	/* Internal Interrupts (or register interrupts ) [16:23] , 8 possible sources*/
	{RI_INT_SRC_0  ,  0  },
	{RI_INT_SRC_1  ,  7  },
	{RI_INT_SRC_2  ,  5  },
	{RI_INT_SRC_3  ,  4  },
	{RI_INT_SRC_4  ,  5  },
	{RI_INT_SRC_5  ,  10 },
	{RI_INT_SRC_6  ,  15 },
	{RI_INT_SRC_7  ,  2  },
	{XSWI_INT_SRC_0,  0  },
	/* iNTERNAL EXC  7  },EPTION 3 INTERRUPTS*/
	{EIRQ_INT_0    ,  10 },
	{EIRQ_INT_1    ,  15 },
	{EIRQ_INT_2    ,  2  },
	/* HW interrupts - sourced by GB peripherals (including the Modem) 48 possible interrupts*/
	{INTC_SRC_DMA ,  15  },       /* 0x00 */
	{INTC_HW_RIPC1_COMBIND , 15  },
	{INTC_HW_DDR_ACCESS_TIMEOUT ,  15  },
	{INTC_SRC_RES3 ,  15  },
	{INTC_SRC_RES4 ,  15  },
	{INTC_SRC_RES5 ,  15 },
	{INTC_SRC_RES6 ,  15 },
	{INTC_SRC_RES7 ,  15  },
	{INTC_HW_DMA_C2_0 ,  0  },
	{INTC_HW_DMA_C2_1   ,  4  },
	{INTC_HW_DMA_C2_2 ,  7  },
	{INTC_HW_DMA_C2_3 ,  5  },
	{INTC_HW_DMA_C2_4 ,  4  },
	{INTC_HW_DMA_C2_5 ,  5  },
	{INTC_HW_DMA_C2_6 ,  10 },
	{INTC_HW_DMA_C2_7 ,  15 },

	{INTC_HW_SCK_SNST ,  15  },  /* 0x10 */
	{INTC_HW_SCK_WKUP ,15},
	{INTC_HW_USIM2   ,  10  },
	{INTC_HW_APB_GSSP   ,  15  },
	{INTC_HW_PMIC_WKUP ,  15  },
    {INTC_HW_MSL      ,  5  },
	{INTC_HW_EXTPAD_1 ,  4  },
	{INTC_HW_EXTPAD_2 ,  5  },
	{INTC_SRC_USIM     ,  10 },
	{INTC_SRC_I2C_CP    ,  4 },
    {INTC_SRC_IPC_DATA_ACK ,  7  },
	{INTC_SRC_IPC_RD ,  5  },
	{INTC_SRC_IPC_ACK , 4  },
	{INTC_SRC_IPC_GP ,  5  },
	{INTC_SRC_RES30        ,  15  },

	{INTC_HW_CPMU     ,  4  },       /* 0x20 */
    {INTC_HW_EIC ,  7  },
	{INTC_HW_RTU_0    ,  5  },
	{INTC_HW_RTU_1    ,  4  },
	{INTC_HW_RTU_2    ,  5  },
	{INTC_HW_Tmr1     ,  10 },
    {INTC_HW_Tmr2     ,  15 },
    {INTC_SRC_RES38   ,  15 },
    {INTC_HW_Tmr3     ,  0  },
    {INTC_SRC_WDT     ,  3  },
	{INTC_HW_ECIPHER  ,  15  },
	{INTC_HW_DTC_CH0  ,  15  },
	{INTC_HW_ADMA_DTC_2 ,  15  },
	{INTC_HW_DTC_CH1  ,  15  },
    {INTC_HW_ECIPHER1     , 15 },
    {INTC_HW_ARM_ACCS_ERR , 15 },
								    /* 0x30 */

    {INTC_HW_DTC_ERR  ,15    },
	{INTC_HW_RES48    ,15    },
	{INTC_GSM_WAKE_UP ,1     },
	{INTC_WBCDMA_WAKE_UP ,2  },
	{INTC_CRXD_EVENT,15},
	{INTC_CGPIO_EVENT ,4 },
	{INTC_HW_DSSP1,	  0			},
	{INTC_HW_RES54,	  15			},
	{INTC_HW_DSSP3,	  2			},
	{INTC_HW_I2C, 3		},
	{INTC_COMM_UART,		  4			},
	{INTC_HW_MAC_E,		  15			},
	{INTC_AC_IPC_0,			  1		    },
	{INTC_D2_MODEM_OK,		  2		    },
	{INTC_AC_IPC_1,			  3		    },
	{INTC_AC_IPC_2,			  4		    },
	{INTC_AC_IPC_3,			  0		    },
									    /* 0x40 */
	{EDH_MACD_INTERRUPT,             15 },
	{EDH_RHD_INTERRUPT,              15 },
	{EDH_MACD_RHD_ERROR_INTERRUPT,   15 },
	{EDH_PHD_INTERRUPT,              15 },
	{EDH_PHD_PRVC_ERROR_INTERRUPT,   15 },
	{EDH_PDIC_INTERRUPT,             15 },
	{EDH_PCIP_INTERRUPT,             15 },
	{EDH_PDIC_PCIP_ERROR_INTERRUPT,  15 },
	{EDH_PCIP_PHB_ERROR_INTERRUPT,   15 },
	{EDH_MHB_INTERRUPT,              15 },
	{EDH_MMA_MHB_ERROR_INTERRUPT,    15 },
	{LDI_ERROR,                      15 },
	{LDI_SI_P,                       15 },
	{LDI_HD_DATA_DEBUG,              15 },
	{LTU_GPSIG,                      15 },
	{LTU_COMP,                       15 },
									    /* 0x50 */
	{INTC_RESERVED_79,               15 },
	{MC_WRITE_READ_ERROR,            15 },
	{LTE_X_ASSERT_MSA2SEAGULL,       15 },
	{EDH_PDIC_ERROR_INTERRUPT,       15 },
	{INTC_RESERVED_83,               15 },
	{INTC_RESERVED_84,               15 },
	{INTC_RESERVED_85,               15 },
	{INTC_RESERVED_86,               15 },
	{INTC_RESERVED_87,               15 },
	{INTC_RESERVED_88,               15 },
	{INTC_RESERVED_89,               15 },
	{INTC_RESERVED_90,               15 },
	{INTC_RESERVED_91,               15 },
	{INTC_RESERVED_92,               15 },
	{INTC_RESERVED_93,               15 },
	{INTC_RESERVED_94,               15 },
	{INTC_RESERVED_95,               15 }
	// no setting for: INTC_CI2C_SCL_SDA_WAKE_UP_DUMMY_I2C_2_TEST - NOT A REAL HW INTERRUPT									
	

