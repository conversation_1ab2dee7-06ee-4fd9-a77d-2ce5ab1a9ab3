/******************************************************************************
 *
 * Name:	SSP.h
 * Project:	Hermon-2
 * Purpose:	Testing
 *
 ******************************************************************************/

/******************************************************************************
 *
 *  (C)Copyright 2005 - 2011 Marvell. All Rights Reserved.
 *
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL.
 *  The copyright notice above does not evidence any actual or intended
 *  publication of such source code.
 *  This Module contains Proprietary Information of Marvell and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the
 *  licensees of Marvell.
 *  Such users have the right to use, modify, and incorporate this code into
 *  products for purposes authorized by the license agreement provided they
 *  include this notice and the associated copyright notice with any such
 *  product.
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

/******************************************************************************
 *
 * This file was automatically generated by reg.pl using  *	SSP.csv
 *
 ******************************************************************************/

/******************************************************************************
 *
 * History:
 *
 ********* PLEASE INSERT THE CVS HISTORY OF THE PREVIOUS VERSION HERE. *********
 *******************************************************************************/

#ifndef	__INC_SSP_H
#define	__INC_SSP_H

/*
 *
 *	THE REGISTER DEFINES
 *
 */
#define	SSP_SSCR0		(0x0000)	/* 32 bit	SSP Control Register 0 */
#define	SSP_SSCR1		(0x0004)	/* 32 bit	SSP Control Register 1  */
#define	SSP_SSSR		(0x0008)	/* 32 bit	SSP Status Register */
#define	SSP_SSITR		(0x000C)	/* 32 bit	SSP Interrupt Test
											 *			Register
											 */
#define	SSP_SSDR		(0x0010)	/* 32 bit	SSP Data Register */
#define	SSP_SSTO		(0x0028)	/* 32 bit	SSP Time Out Register */
#define	SSP_SSPSP		(0x002C)	/* 32 bit	SSP Programmable Serial
											 *			Protocol Register
											 */
#define	SSP_SSTSA		(0x0030)	/* 32 bit	SSP TX Time Slot Active
											 *			Register
											 */
#define	SSP_SSRSA		(0x0034)	/* 32 bit	SSP RX Time Slot Active
											 *			Register
											 */
#define	SSP_SSTSS		(0x0038)	/* 32 bit	SSP Time Slot Status
											 *			Register
											 */
#define	SSP_SSACD		(0x003C)	/* 32 bit	SSP Audio Clock Divider
											 *			Register
											 */
#define	SSP_SSACDD		(0x0040)	/* 32 bit	SSP Audio Clock Dither
											 *			Divider Register
											 */
#define	DUMMY_REGISTER	(0x0000)	/* 32 bit	This is a Dummy Register */

/*
 *
 *	THE BIT DEFINES
 *
 */
/*	SSP_SSCR0		0x0000	SSP Control Register 0 */
#define	SSP_SSCR0_MOD			BIT_31			/* Mode */
#define	SSP_SSCR0_ACS			BIT_30			/* Audio Clock Select */
#define	SSP_SSCR0_FPCKE			BIT_29			/* FIFO Packing Enable */
/*		Bit(s) SSP_SSCR0_RSRV_28 reserved */
#define	SSP_SSCR0_52MM			BIT_27			/* 52M[bps] M[ode */
#define	SSP_SSCR0_FRDC_MSK		SHIFT24(0x7)	/* Frame Rate Divider Control */
#define	SSP_SSCR0_FRDC_BASE		24
/* Transmit FIFO Underrun Interrupt Mask */
#define	SSP_SSCR0_TIM			BIT_23
/* Receive FIFO Overrun Interrupt Mask */
#define	SSP_SSCR0_RIM			BIT_22
#define	SSP_SSCR0_NCS			BIT_21			/* Network Clock Select */
#define	SSP_SSCR0_EDSS			BIT_20			/* Extended Data Size Select */
#define	SSP_SSCR0_SCR_MSK		SHIFT8(0xfff)	/* Serial Clock Rate */
#define	SSP_SSCR0_SCR_BASE		8
#define	SSP_SSCR0_SSE			BIT_7			/* Synchronous Serial Port Enable */
#define	SSP_SSCR0_ECS			BIT_6			/* External Clock Select */
#define	SSP_SSCR0_FRF_MSK		SHIFT4(0x3)		/* Frame Format */
#define	SSP_SSCR0_FRF_BASE		4
#define	SSP_SSCR0_DSS_MSK		SHIFT0(0xf)		/* Data Size Select */
#define	SSP_SSCR0_DSS_BASE		0

/*	SSP_SSCR1		0x0004	SSP Control Register 1 */
/* TXD Three-state Enable On Last Phase */
#define	SSP_SSCR1_TTELP			BIT_31
#define	SSP_SSCR1_TTE			BIT_30			/* TXD Three-State Enable */
#define	SSP_SSCR1_EBCEI			BIT_29			/* Enable Bit Count Error Interrupt */
#define	SSP_SSCR1_SCFR			BIT_28			/* Slave Clock Free Running */
#define	SSP_SSCR1_ECRA			BIT_27			/* Enable Clock Request A */
#define	SSP_SSCR1_ECRB			BIT_26			/* Enable Clock Request B */
/* SSP Serial Bit Rate Clock (SSPSCLKx) Direction */
#define	SSP_SSCR1_SCLKDIR		BIT_25
#define	SSP_SSCR1_SFRMDIR		BIT_24			/* SSP Frame (SSPSFRMx) Direction */
#define	SSP_SSCR1_RWOT			BIT_23			/* Receive Without Transmit */
#define	SSP_SSCR1_TRAIL			BIT_22			/* Trailing Byte */
#define	SSP_SSCR1_TSRE			BIT_21			/* Transmit Service Request Enable */
#define	SSP_SSCR1_RSRE			BIT_20			/* Receive Service Request Enable */
/* Receiver Time-out Interrupt Enable */
#define	SSP_SSCR1_TINTE			BIT_19
/* Peripheral Trailing Byte Interrupt Enable */
#define	SSP_SSCR1_PINTE			BIT_18
/*		Bit(s) SSP_SSCR1_RSRV_17 reserved */
#define	SSP_SSCR1_IFS			BIT_16			/* Invert Frame Signal */
#define	SSP_SSCR1_STRF			BIT_15			/* Select FIFO For Efwr */
#define	SSP_SSCR1_EFWR			BIT_14			/* Enable FIFO Write/read */
#define	SSP_SSCR1_RFT_MSK		SHIFT10(0xf)	/* RXFIFO Trigger Threshold */
#define	SSP_SSCR1_RFT_BASE		10
#define	SSP_SSCR1_TFT_MSK		SHIFT6(0xf)		/* TXFIFO Trigger Threshold */
#define	SSP_SSCR1_TFT_BASE		6
/* Motorola SPI SSPSCLK phase setting */
#define	SSP_SSCR1_SPH			BIT_4
/* Motorola SPI SSPSCLK Polarity Setting */
#define	SSP_SSCR1_SPO			BIT_3
#define	SSP_SSCR1_LBM			BIT_2			/* Loopback Mode */
#define	SSP_SSCR1_TIE			BIT_1			/* Transmit FIFO Interrupt Enable */
#define	SSP_SSCR1_RIE			BIT_0			/* Receive FIFO Interrupt Enable */

/*	SSP_SSSR		0x0008	SSP Status Register */
#define	SSP_SSSR_OSS					BIT_31			/* Odd Sample Status */
/* TX FIFO Odd Sample Status */
#define	SSP_SSSR_TX_OSS					BIT_30
/*		Bit(s) SSP_SSSR_RSRV_29_24 reserved */
#define	SSP_SSSR_BCE					BIT_23			/* Bit Count Error */
/* Clock Synchronization Status */
#define	SSP_SSSR_CSS					BIT_22
#define	SSP_SSSR_TUR					BIT_21			/* Transmit FIFO Underrun */
#define	SSP_SSSR_EOC					BIT_20			/* End Of Chain */
/* Receiver Time-out Interrupt */
#define	SSP_SSSR_TINT					BIT_19
/* Peripheral Trailing Byte Interrupt */
#define	SSP_SSSR_PINT					BIT_18
/*		Bit(s) SSP_SSSR_RSRV_17_16 reserved */
#define	SSP_SSSR_RFL_MSK				SHIFT12(0xf)	/* Receive FIFO Level */
#define	SSP_SSSR_RFL_BASE				12
#define	SSP_SSSR_TFL_MSK				SHIFT8(0xf)		/* Transmit FIFO Level */
#define	SSP_SSSR_TFL_BASE				8
#define	SSP_SSSR_ROR					BIT_7			/* Receive FIFO Overrun */
/* Receive FIFO Service Request */
#define	SSP_SSSR_RFS					BIT_6
/* Transmit FIFO Service Request */
#define	SSP_SSSR_TFS					BIT_5
#define	SSP_SSSR_BSY					BIT_4			/* SSP Busy */
#define	SSP_SSSR_RNE					BIT_3			/* Receive FIFO Not Empty */
#define	SSP_SSSR_TNF					BIT_2			/* Transmit FIFO Not Full */
/*		Bit(s) SSP_SSSR_RSRV_1_0 reserved */

/*	SSP_SSITR		0x000C	SSP Interrupt Test Register */
/*		Bit(s) SSP_SSITR_RSRV_31_8 reserved */
#define	SSP_SSITR_TROR					BIT_7				/* Test RXFIFO Overrun */
/* Test RXFIFO Service Request */
#define	SSP_SSITR_TRFS					BIT_6
/* Test TXFIFO Service Request */
#define	SSP_SSITR_TTFS					BIT_5
/*		Bit(s) SSP_SSITR_RSRV_4_0 reserved */

/*	SSP_SSDR		0x0010	SSP Data Register */
#define	SSP_SSDR_DATA_MSK		SHIFT0(0xffffffff)	/* DATA */
#define	SSP_SSDR_DATA_BASE		0

/*						 */
/*		Bit(s) SSP_SSTO_RSRV_31_24 reserved */
#define	SSP_SSTO_TIMEOUT_MSK			SHIFT0(0xffffff)	/* Timeout Value */
#define	SSP_SSTO_TIMEOUT_BASE			0

/*	SSP_SSPSP		0x002C	SSP Programmable Serial Protocol Register */
/*		Bit(s) SSP_SSPSP_RSRV_31 reserved */
#define	SSP_SSPSP_EDMYSTOP_MSK		SHIFT28(0x7)	/* Extended Dummy Stop */
#define	SSP_SSPSP_EDMYSTOP_BASE		28
#define	SSP_SSPSP_EDMYSTRT_MSK		SHIFT26(0x3)	/* Extended Dummy Start */
#define	SSP_SSPSP_EDMYSTRT_BASE		26
/* Frame Sync Relative Timing Bit */
#define	SSP_SSPSP_FSRT				BIT_25
#define	SSP_SSPSP_DMYSTOP_MSK		SHIFT23(0x3)	/* Dummy Stop */
#define	SSP_SSPSP_DMYSTOP_BASE		23
/*		Bit(s) SSP_SSPSP_RSRV_22 reserved */
#define	SSP_SSPSP_SFRMWDTH_MSK		SHIFT16(0x3f)	/* Serial Frame Width */
#define	SSP_SSPSP_SFRMWDTH_BASE		16
#define	SSP_SSPSP_SFRMDLY_MSK		SHIFT9(0x7f)	/* Serial Frame Delay */
#define	SSP_SSPSP_SFRMDLY_BASE		9
#define	SSP_SSPSP_DMYSTRT_MSK		SHIFT7(0x3)		/* Dummy Start */
#define	SSP_SSPSP_DMYSTRT_BASE		7
#define	SSP_SSPSP_STRTDLY_MSK		SHIFT4(0x7)		/* Start Delay */
#define	SSP_SSPSP_STRTDLY_BASE		4
#define	SSP_SSPSP_ETDS				BIT_3			/* End Of Transfer Data State */
#define	SSP_SSPSP_SFRMP				BIT_2			/* Serial Frame Polarity */
#define	SSP_SSPSP_SCMODE_MSK		SHIFT0(0x3)		/* Serial Bit-rate Clock Mode */
#define	SSP_SSPSP_SCMODE_BASE		0

/*	SSP_SSTSA		0x0030	SSP TX Time Slot Active Register */
/*		Bit(s) SSP_SSTSA_RSRV_31_8 reserved */
#define	SSP_SSTSA_TTSA_MSK				SHIFT0(0xff)		/* TX Time Slot Active */
#define	SSP_SSTSA_TTSA_BASE				0

/*	SSP_SSRSA		0x0034	SSP RX Time Slot Active Register */
/*		Bit(s) SSP_SSRSA_RSRV_31_8 reserved */
#define	SSP_SSRSA_RTSA_MSK				SHIFT0(0xff)		/* RX Time Slot Active */
#define	SSP_SSRSA_RTSA_BASE				0

/*	SSP_SSTSS		0x0038	SSP Time Slot Status Register */
#define	SSP_SSTSS_NMBSY					BIT_31				/* Network Mode Busy */
/*		Bit(s) SSP_SSTSS_RSRV_30_3 reserved */
#define	SSP_SSTSS_TSS_MSK				SHIFT0(0x7)			/* Time Slot Status */
#define	SSP_SSTSS_TSS_BASE				0

/*	SSP_SSACD		0x003C	SSP Audio Clock Divider Register */
/*		Bit(s) SSP_SSACD_RSRV_31_8 reserved */
#define	SSP_SSACD_SCDX8					BIT_7				/* SYSCLK Divided By 8 */
/* Audio Clock PLL Select */
#define	SSP_SSACD_ACPS_MSK				SHIFT4(0x7)
#define	SSP_SSACD_ACPS_BASE				4
/* SYSCLK Divider Bypass */
#define	SSP_SSACD_SCDB					BIT_3
/* Audio Clock Divider Select */
#define	SSP_SSACD_ACDS_MSK				SHIFT0(0x7)
#define	SSP_SSACD_ACDS_BASE				0

/*	SSP_SSACDD		0x0040	SSP Audio Clock Dither Divider Register */
/*		Bit(s) SSP_SSACDD_RSRV_31 reserved */
#define	SSP_SSACDD_NUM_MSK				SHIFT16(0x7fff)	/* Numerator */
#define	SSP_SSACDD_NUM_BASE				16
/*		Bit(s) SSP_SSACDD_RSRV_15_12 reserved */
#define	SSP_SSACDD_DEN_MSK				SHIFT0(0xfff)	/* Denominator */
#define	SSP_SSACDD_DEN_BASE				0

/*	DUMMY_REGISTER	0x0000	This is a Dummy Register */
#define	DUMMY_REGISTER_DUMMY_MSK		SHIFT0(0xffffffff)	/* Ignore this register */
#define	DUMMY_REGISTER_DUMMY_BASE		0


/*
 *
 *	THE REGISTER DEFINES
 *
 */
#define	SSP_TOP_CTRL		(0x0000)
#define	SSP_FIFO_CTRL		(0x0004)
#define	SSP_INT_EN			(0x0008)
#define	SSP_TIMEOUT		    (0x000C)
#define	SSP_DATAR			(0x0010)	/* 32 bit	SSP Data Register */
#define	SSP_STATUS			(0x0014)

/*
 *
 *  SSP Top Control Register
 *
 */

/* TXD Three-state Enable On Last Phase */
#define SSP_TCR_TTELP			BIT_18

/* TXD Three-State Enable */
#define SSP_TCR_TTE				BIT_17

/* Slave Clock Free Running */
#define SSP_TCR_SCFR			BIT_16

/* Invert Frame Signal */
#define SSP_TCR_IFS				BIT_15

/* Hold Frame Low Control */
#define SSP_TCR_HFL				BIT_14

/* Trailing Byte */
#define SSP_TCR_TRIAL			BIT_13

/* Loopback Mode */
#define SSP_TCR_LBM				BIT_12

/* Motorola SPI SSPSCLK phase setting */
#define SSP_TCR_SPH				BIT_11

/* Motorola SPI SSPSCLK Polarity Setting */
#define SSP_TCR_SPO				BIT_10

/* Data Size Select */
#define SSP_TCR_DSS_MASK		SHIFT5(0x1f)
#define SSP_TCR_DSS32			SHIFT5(0x1f)
#define SSP_TCR_DSS24			SHIFT5(0x17)
#define SSP_TCR_DSS16			SHIFT5(0xf)
#define SSP_TCR_DDS8			SHIFT5(0x7)
#define SSP_TCR_DSS8			SHIFT5(0x7)

/* SSP Frame (SSPSFRMx) Direction */
#define SSP_TCR_SFRMDIR			BIT_4

/* SSP Serial Bit Rate Clock (SSPSCLKx) Direction */
#define SSP_TCR_SCLKDIR			BIT_3
#define SSP_TCR_FRF_MASK		SHIFT1(0x3)

/* Synchronous Serial Port Enable */
#define SSP_TCR_SSE				BIT_0

/*
 *
 *  SSP FIFO Control Register
 *
 */

/* Rx FIFO Auto Full Control */
#define SSP_FCR_RAFC            BIT_17

/* apb_pwdata Write to Tx FIFO Endian */
#define SSP_FCR_TWE_MASK		SHIFT14(0x3)
#define SSP_FCR_TWE(x)          (x << 14)

/* apb_prdata Read from Rx FIFO Endian */
#define SSP_FCR_RRE_MASK		SHIFT12(0x3)
#define SSP_FCR_RRE(x)          (x << 12)

/* Receive Service Request Enable */
#define SSP_FCR_RSRE			BIT_11

/* Transmit Service Request Enable */
#define SSP_FCR_TSRE			BIT_10

/* RXFIFO Trigger Threshold */
/* MaxBurstSize(DMA)/Data Size Select(SSP) - 1 */
#define SSP_FCR_RFT_MASK		SHIFT5(0x1f)
#define SSP_FCR_RFT(x)			(x << 5)

/* TXFIFO Trigger Threshold */
/* MaxBurstSize(DMA)/Data Size Select(SSP) - 1 */
#define SSP_FCR_TFT_MASK		SHIFT0(0x1f)
#define SSP_FCR_TFT(x)			(x << 0)

/*
 *
 *  SSP Interrupt Enable Register
 *
 */

/* Transmit FIFO Underrun Interrupt Mask */
#define SSP_IER_TIM				BIT_5

/* Receive FIFO Overrun Interrupt Mask */
#define SSP_IER_RIM				BIT_4

/* Transmit FIFO Interrupt Enable */
#define SSP_IER_TIE				BIT_3

/* Receive FIFO Interrupt Enable */
#define SSP_IER_RIE				BIT_2

/* Receiver Time-out Interrupt Enable */
#define SSP_IER_RTOIE	        BIT_1

/* -------------------- */


#endif	/* __INC_SSP_H */
