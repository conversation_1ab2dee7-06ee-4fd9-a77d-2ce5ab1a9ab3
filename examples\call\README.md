# 语音通话(VoLTE)  

## **语音通话(VoLTE)功能介绍**

VoLTE技术带给4G用户最直接的感受就是接通等待时间更短，以及更高质量、更自然的语音视频通话效果。VoLTE与2G、3G语音通话有着本质的不同。VoLTE是架构在4G网络上全IP条件下的端到端语音方案。VoLTE相较2G、3G语音通话，语音质量能提高40%左右，因为它采用高分辨率编解码技术。VoLTE为用户带来更低的接入时延（拨号后的等待时间），比3G降50%，大概在2秒左右，而2G时代在6-7秒。此外，2G、3G下，掉线时有发生，但VoLTE的掉线率接近于零。

## **实现功能**
本APP实现语音通话的的拨打电话、来电提示、挂断电话等功能；

1. 实现语音拨打电话后，超时1分钟后挂断电话；
2. 实现语音通话状态通知：来电提示、来电号码解析、电话挂断、语音通话建立成功。


## **APP执行流程**

1. 进入语音通话测试线程，创建语音通话超时挂断的定时和创建语音通话消息队列，然后等待pdp激活；
2. 注册urc回调，通过urc上报的内容筛选关键字判断语音通话的状态；
3. 使用虚拟AT指令ATD打电话；
4. 启动1分钟定时器就挂断电话：对方可能接听，也可能不接听；
5. 线程中阻塞等待语音通话状态消息，消息主要从两个地方来：一是定时器超时后，发送挂断电话的消息到线程中，线程获取到消息后，执行虚拟AT指令“ATH”挂断电话；二是通过urc回调函数检查关键字，“RING”是来电提示，线程接收到该消息后直接执行虚拟AT指令“ATA”接听电话；“+CLCC:”是通话号码显示，拨打电话、语音来电都会上报该URC，可通过内容获取对方的通话号码；“NO CARRIER”是语音通话结束：如对方挂断将会上报该URC；“CONNECT”是语音通话连接建立成功：对方接听或者模组主动接听都会上报该URC。


## **使用说明**
- 支持的模组（子）型号：ML307A-DSLN/GSLN
- 支持的SDK版本：ML307A OpenCPU SDK 1.5.0版本及其后续版本
- 是否需要外设支撑：无
- 使用注意事项：  
    1. 拨打电话的指令为"ATD"，注意号码后需要加分号，如“ATD10086;”；
    2. 测试本示例请使用支持VoLTE的SIM卡，建议物联卡测试异常的时候，可直接使用手机卡测试；
    3. 模组主动拨打电话，对方不接听直接挂断，这种情况模组无法感知对方已经挂断了，需要等语音通话超时（超时也会上报“NO CARRIER”）或者模组自动调用指令挂断。

## **FAQ（非必要，视客户/FAE咨询情况增列）**

- 无

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/18 19:20
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------