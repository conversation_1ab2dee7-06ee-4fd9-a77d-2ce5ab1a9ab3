/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*********************************************************************
* Filename:     AUX_TABLE_RxAgcRipple_CalBody.h
* Created By:   <PERSON><PERSON> meltser on 6/18/2008
* Programmers:  Elad Goldman
* Module:       Generic RF Module - Data Base
* RF Type:      SKY_Hel3
* DB Ver.:      0.9
* Doc Ver.:     2.94
* GUI Ver.:     7.26
*
* Description:  Contains the "AUX_TABLE_RxAgcRipple" CalBody
*
* Notes:        This file is automatically generated by WIZARD.
*********************************************************************/

/*
 * each line in the file contain the data of 2*Int16:
*/
//|-RippleCorrect_Slope----|-RippleCorrect_Offset---|-fixedIndex0------------|-fixedIndex1------------|-fixedIndex2------------|-fixedIndex3------------|-fixedIndex4------------|-fixedIndex5------------|-fixedIndex6------------|
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               0,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               1,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               2,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               3,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               4,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               5,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               6,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               7,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               8,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               9,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               10,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               11,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               12,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               13,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               14,
   0,                       0,                       // EGSM_BAND,                LNA_ACTIVE,               15,

   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             0,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             1,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             2,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             3,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             4,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             5,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             6,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             7,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             8,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             9,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             10,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             11,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             12,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             13,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             14,
   0,                       0,                       // EGSM_BAND,                LNA_BYPASSED,             15,

   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               0,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               1,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               2,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               3,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               4,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               5,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               6,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               7,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               8,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               9,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               10,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               11,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               12,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               13,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               14,
   0,                       0,                       // DCS_BAND,                 LNA_ACTIVE,               15,

   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             0,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             1,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             2,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             3,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             4,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             5,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             6,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             7,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             8,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             9,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             10,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             11,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             12,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             13,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             14,
   0,                       0,                       // DCS_BAND,                 LNA_BYPASSED,             15,

   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               0,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               1,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               2,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               3,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               4,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               5,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               6,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               7,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               8,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               9,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               10,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               11,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               12,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               13,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               14,
   0,                       0,                       // PCS_BAND,                 LNA_ACTIVE,               15,

   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             0,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             1,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             2,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             3,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             4,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             5,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             6,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             7,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             8,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             9,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             10,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             11,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             12,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             13,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             14,
   0,                       0,                       // PCS_BAND,                 LNA_BYPASSED,             15,

   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               0,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               1,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               2,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               3,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               4,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               5,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               6,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               7,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               8,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               9,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               10,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               11,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               12,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               13,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               14,
   0,                       0,                       // GSM_850_BAND,             LNA_ACTIVE,               15,

   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             0,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             1,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             2,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             3,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             4,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             5,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             6,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             7,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             8,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             9,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             10,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             11,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             12,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             13,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             14,
   0,                       0,                       // GSM_850_BAND,             LNA_BYPASSED,             15,

