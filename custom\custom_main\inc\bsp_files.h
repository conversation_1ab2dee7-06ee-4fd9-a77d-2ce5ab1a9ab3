﻿/*********************************************************
*  @file    bsp_files.h
*  @brief   包含各种针对文件系统的操作，比如保存参数，保存OTA固件
*  Copyright (c) 2021 TENGEN.
*  All rights reserved.
*  created by Delei 2021/03/26
********************************************************/
#ifndef __BSP_FILES_H__
#define __BSP_FILES_H__

#include "includes.h"
#include "memory_gw.h"
#include "periodctrl.h"

#define HISTORY_LIST_MAX    512 //定义历史数据最多记录512条

//用于存放四元组的文件
#define THREE_META_FILE		"three_meta.txt"
//用于存放测试结果的文件
#define TEST_RES_FILE       "testRes.txt"
//用于存放子设备固件的文件
#define SUBDEV_FILE         "subdev.txt"
//用于存放定时、时段控制相关的数据
#define PERIOD_FILE         "period.txt"
//用于存放出场敏感参数：变化阈值、485级联地址、微断相关参数
#define DEV_INFO_FILE		"devInfo.txt"

//用于存放周期定时数据   //SIFANG
#define PERIOD_CTRL_FILE    "periCtrl.txt"
//用于存放历史数据
#define HISTORY_FILE        "history.txt"

//用于存放三元组信息
typedef struct {
    char pProductKey[21];
    char pDeviceName[33];
    char pDeviceSecr[33];
    char pProducSecr[20];
} deviceInfo_t;

//rtata memory
typedef struct {
    uint8_t ucTime_ctrl_en[MAX_CB_NUM + 1];//bit0:onEnable,bit1:offEnable      keep breaker data from address 1-MAX_CB_NUM
    str_TIME onBeginTime;
    str_TIME onEndTime;
    str_TIME offBeginTime;
    str_TIME offEndTime;
    uint8_t  onCtrlEnable;
    uint8_t  offCtrlEnable;    
    GW_TIME_TASK GwTimeTask;    //本地定时任务
} period_ctrl_t;

typedef struct {
    str_deadband r_para;        //变化上报的阈值参数，数据变化大于这里面的设定值，将被认定为发生了变化
    uint8_t AddrOfBreakFirst;   //网关下第一个断路器映射地址
    // uint8_t pruductName[4];     //生产厂家
    // uint32_t productDate;       //生产日期
    // uint32_t BreakId;           //微断的BreakId，由微断产测时，上位机导入并记忆
    int       ShutdownReason;   //这里记录关机原因
    uint8_t baudId; // 波特率序号 0:9600 1:19200
    // uint8_t productModel[16];   //产品型号
    
} devInfo_t;

typedef struct {
    uint32_t  GWID;//网关唯一识别码
    uint16_t  TestStatusID;//测试通过的情况
} testRes_t;


//用于存放数据到Flash的缓存
typedef struct {
    uint32_t breakId;//breakID
    uint32_t actAndReson;//动作、原因
    uint32_t actTime;//动作时间
}historyFlash_t;

//用于存放历史数据的链表节点
typedef struct {
    uint32_t breakId;//breakID
    uint32_t actAndReson;//动作、原因
    uint32_t actTime;//动作时间
    struct core_list_head historyListNode;//链表节点
    struct core_list_head pickNode;//被挑选的，符合筛选条件的链表节点(临时有效)
}historyNode_t;

// int info_prestore(void);
int devInfo_read(void);
int devInfo_write(void);
int periodCtrl_read(void);
int periodCtrl_write(void);
int three_meta_get(void);
int three_meta_set(void);
int test_res_get(void);
int test_res_set(void);
osStatus_t subdev_firmware_write(uint8_t *buffer, uint32_t length);
osStatus_t subdev_firmware_read(uint8_t *buffer, uint32_t length,uint32_t offset);
osStatus_t subdev_firmware_stop(void);
osStatus_t subdev_firmware_Del(void);
void printDevInfo(char *log);
void printPeriodCtrl(char *log);


int checkPeiodFile(void);
int readPeriodFile(int seekAddr, uint8_t *buffer, int length);
int writePeriodFile(int seekAddr, uint8_t *buffer, int length);
int delPeriodFile(void);

int checkHistoryFile(void);
int writeHistoryFile(uint8_t *pBuf, int blockNum);

#endif