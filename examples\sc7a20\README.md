# SC7A20 I2C传感器测试 简介 

## SC7A20 I2C传感器介绍**
I2C（Inter-Integrated Circuit），中文称为集成电路总线，是一种应用广泛的芯片间串行扩展总线。I2C总线属于一主多从的总线结构，即一个主设备（Master）可以与多个从设备（Slave）进行通信。每个设备在I2C总线上都有一个特定的设备地址，用于区分同一I2C总线上的其他设备。此外，I2C还支持多主机模式，即允许两个或多个主设备在一个I2C总线上竞争，此时将通过仲裁决定哪个主设备获取使用权。
I2C总线总共只有两条信号线：
1、串行时钟线（SCL）：用于同步数据传输的时钟信号。
2、串行数据线（SDA）：用于数据的双向传输。
I2C总线因其简单性和灵活性而被广泛应用于各种电子设备中，如连接传感器、存储器、显示屏等外设到微控制器或微处理器上。它特别适用于需要多个设备共享通信线并由一个（或多个）主设备管理的场景。I2C的优势在于其简单的二线制设计、灵活的通信方式以及支持多主机和多从机的能力。

SC7A20是一款高精度、低功耗的数字三轴加速度传感器芯片，广泛应用于智能手机、可穿戴设备、工业自动化和汽车电子等领域。SC7A20通过I2C/SPI接口与MCU通信，增强了芯片的兼容性和灵活性。本示例采用I2C进行通信。

## **实现功能**
1. 实现SC7A20 I2C通信，获取设备WHO_AM_I
2. 实现SC7A20 XYZ三轴的加速度值，5S获取一次

## **APP执行流程**
1. 创建SC7A20测试主线程
2. 创建用于定时获取加速度数据的信号量、定时器
3. 初始化SC7A20，初始化I2C，并通过I2C读取SC7A20的WHO_AM_I寄存器，读取成功，说明模组与传感器通信基本无误。
4. 等待等定时器到达，释放信号量，主线程获取到信号量后，读取SC7A20的寄存器，并打印XYZ三轴的加速度值。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：需要SC7A20
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 问题：
  答复：


## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/11 18:08
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------