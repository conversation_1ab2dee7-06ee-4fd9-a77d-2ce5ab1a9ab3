;#<FEEDBACK># ARM Linker, 5050041: Last Updated: Mon Apr 15 19:11:24 2024
;VERSION 0.2
;FILE 3DES.o
Base64Decode <= USED 0
Base64Encode <= USED 0
CovertKey <= USED 0
GetByte <= USED 0
MyDesInit <= USED 0
Run1Des <= USED 0
RunPad <= USED 0
RunRsm <= USED 0
;FILE AAtomizer.o
_ZN14streamingmedia9AAtomizer4HashEPKc <= USED 0
;FILE ABuffer.o
_ZN14streamingmedia7ABuffer18setFarewellMessageEN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia7ABuffer4metaEv <= USED 0
_ZN14streamingmedia7ABufferC1EPvj <= USED 0
;FILE ALooper.o
_ZN14streamingmedia7ALooper8GetNowMsEv <= USED 0
;FILE ALooperRoster.o
_ZN14streamingmedia13ALooperRoster10findLooperEi <= USED 0
_ZN14streamingmedia13ALooperRoster20postAndAwaitResponseERKN7android2spINS_8AMessageEEEPS4_ <= USED 0
_ZN14streamingmedia13ALooperRoster9postReplyEjRKN7android2spINS_8AMessageEEE <= USED 0
;FILE AMRRTPAssembler.o
;FILE AMessage.o
_ZN14streamingmedia8AMessage10FromParcelERKNS_6ParcelE <= USED 0
_ZN14streamingmedia8AMessage10setMessageEPKcRKN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage17setObjectInternalEPKcRKN7android2spINS3_7RefBaseEEENS0_4TypeE <= USED 0
_ZN14streamingmedia8AMessage20postAndAwaitResponseEPN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage5clearEv <= USED 0
_ZN14streamingmedia8AMessage7setRectEPKciiii <= USED 0
_ZN14streamingmedia8AMessage7setSizeEPKcj <= USED 0
_ZN14streamingmedia8AMessage8freeItemEPNS0_4ItemE <= USED 0
_ZN14streamingmedia8AMessage8setFloatEPKcf <= USED 0
_ZN14streamingmedia8AMessage9postReplyEj <= USED 0
_ZN14streamingmedia8AMessage9setBufferEPKcRKN7android2spINS_7ABufferEEE <= USED 0
_ZN14streamingmedia8AMessage9setDoubleEPKcd <= USED 0
_ZN14streamingmedia8AMessage9setTargetEi <= USED 0
_ZNK14streamingmedia8AMessage10findBufferEPKcPN7android2spINS_7ABufferEEE <= USED 0
_ZNK14streamingmedia8AMessage10findDoubleEPKcPd <= USED 0
_ZNK14streamingmedia8AMessage10findStringEPKcPNS_7AStringE <= USED 0
_ZNK14streamingmedia8AMessage11debugStringEi <= USED 0
_ZNK14streamingmedia8AMessage11findMessageEPKcPN7android2spIS0_EE <= USED 0
_ZNK14streamingmedia8AMessage12countEntriesEv <= USED 0
_ZNK14streamingmedia8AMessage13writeToParcelEPNS_6ParcelE <= USED 0
_ZNK14streamingmedia8AMessage14getEntryNameAtEjPNS0_4TypeE <= USED 0
_ZNK14streamingmedia8AMessage20senderAwaitsResponseEPj <= USED 0
_ZNK14streamingmedia8AMessage8findRectEPKcPiS3_S3_S3_ <= USED 0
_ZNK14streamingmedia8AMessage8findSizeEPKcPj <= USED 0
_ZNK14streamingmedia8AMessage9findFloatEPKcPf <= USED 0
;FILE AString.o
_ZN14streamingmedia12StringPrintfEPKcz <= USED 0
_ZN14streamingmedia7AString4trimEv <= USED 0
_ZN14streamingmedia7AString5clearEv <= USED 0
_ZN14streamingmedia7AString5eraseEjj <= USED 0
_ZN14streamingmedia7AString5setToEPKc <= USED 0
_ZN14streamingmedia7AString6appendEPv <= USED 0
_ZN14streamingmedia7AString6appendERKS0_jj <= USED 0
_ZN14streamingmedia7AString6appendEd <= USED 0
_ZN14streamingmedia7AString6appendEf <= USED 0
_ZN14streamingmedia7AString6appendEl <= USED 0
_ZN14streamingmedia7AString6appendEm <= USED 0
_ZN14streamingmedia7AString6appendEx <= USED 0
_ZN14streamingmedia7AString6appendEy <= USED 0
_ZN14streamingmedia7AString6insertEPKcjj <= USED 0
_ZN14streamingmedia7AString6insertERKS0_j <= USED 0
_ZN14streamingmedia7AString7astrdupEPKc <= USED 0
_ZN14streamingmedia7AString7tolowerEv <= USED 0
_ZN14streamingmedia7AStringC1ERKS0_jj <= USED 0
_ZNK14streamingmedia7AString10startsWithEPKc <= USED 0
_ZNK14streamingmedia7AString4findEPKcj <= USED 0
_ZNK14streamingmedia7AString4hashEv <= USED 0
_ZNK14streamingmedia7AString4sizeEv <= USED 0
_ZNK14streamingmedia7AString7compareERKS0_ <= USED 0
_ZNK14streamingmedia7AString8endsWithEPKc <= USED 0
_ZNK14streamingmedia7AStringgtERKS0_ <= USED 0
_ZNK14streamingmedia7AStringltERKS0_ <= USED 0
;FILE ATimers.o
_ZN7android13DurationTimer13addTotimevalTEP8timevalTl <= USED 0
_ZN7android13DurationTimer17subtracttimevalTsEPK8timevalTS3_ <= USED 0
_ZN7android13DurationTimer4stopEv <= USED 0
_ZN7android13DurationTimer5startEv <= USED 0
_ZNK7android13DurationTimer13durationUsecsEv <= USED 0
toMillisecondTimeoutDelay <= USED 0
;FILE AmrBEPayloadEncap.o
_ZN14streamingmedia17AmrBEPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrOAPayloadEncap.o
_ZN14streamingmedia17AmrOAPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrPayloadEncap.o
_ZN14streamingmedia15AmrPayloadEncap9setNewCMREh <= USED 0
;FILE Andratomic.o
android_atomic_acquire_cas <= USED 0
android_atomic_acquire_load <= USED 0
android_atomic_acquire_store <= USED 0
android_atomic_and <= USED 0
android_atomic_cas <= USED 0
android_atomic_cmpxchg_thumb <= USED 0
android_atomic_or <= USED 0
android_atomic_release_load <= USED 0
android_atomic_release_store <= USED 0
android_compiler_barrier <= USED 0
android_memory_barrier <= USED 0
android_memory_store_barrier <= USED 0
;FILE Andrlog.o
__android_log_btwrite <= USED 0
__android_log_buf_print <= USED 0
__android_log_bwrite <= USED 0
__android_log_vprint <= USED 0
__non_inline_android_log_write <= USED 0
getLogLevel <= USED 0
setLogLevel <= USED 0
;FILE AntTunerConfig.o
;FILE AntTunerConfig_NVM.o
;FILE AucCodecStub.o
_ZN14streamingmedia13amrTxCallBackEP15amrFrameInfo_ts <= USED 0
_ZN14streamingmedia19amrRxCallBack_neteqEPv <= USED 0
;FILE AucEncoder.o
_ZN14streamingmedia10AucEncoder12packEVSFrameEP15IMSFrameInfo_tsx <= USED 0
_ZN14streamingmedia10AucEncoder7getMuteEv <= USED 0
;FILE AudioAMRFormatParameter.o
_ZN23AudioAMRFormatParameter17set_amr_parameterERK13AMRParameter_ <= USED 0
_ZN23AudioAMRFormatParameter19set_amr_support_redEb <= USED 0
_ZN23AudioAMRFormatParameter30set_amr_mode_change_capabilityEj <= USED 0
;FILE AudioEVSFormatParameter.o
;FILE AudioExtern.o
CraneCodecADCOnOff <= USED 0
LteVoiceSuspend <= USED 0
audioGsmResumeCB <= USED 0
audioGsmSuspendCB <= USED 0
;FILE AudioHAL_Control.o
AudioHAL_AifBindCodec_CB <= USED 0
AudioHAL_AifBindHeadsetDetectionCB <= USED 0
AudioHAL_AifBind_BTEvent_CB <= USED 0
AudioHAL_AifConfigure <= USED 0
AudioHAL_AifGetFMCodecGain <= USED 0
AudioHAL_AifGetHeadsetInfo <= USED 0
AudioHAL_AifGetTxCodecGainStage1 <= USED 0
AudioHAL_AifGetTxCodecGainStage2 <= USED 0
AudioHAL_AifGet_VAD <= USED 0
AudioHAL_AifHeadsetDetection <= USED 0
AudioHAL_AifSetDspRxMute <= USED 0
AudioHAL_AifSetPhoneStatus <= USED 0
AudioHAL_AifSetSideTone <= USED 0
AudioHAL_AifSetTxCodecGain <= USED 0
AudioHAL_AifSet_VAD <= USED 0
AudioHAL_AifTonePause <= USED 0
AudioHAL_sspaSetDelay <= USED 0
;FILE AudioHAL_Stream.o
AUDIOHAL_GetBufferSize <= USED 0
AudioHAL_FadingControl <= USED 0
AudioHAL_IsMediaStreamOn <= USED 0
AudioHAL_SetFadingStep <= USED 0
TestPlay_HalfHandler_mediaLoop <= USED 0
TestRecord_HalfHandler_mediaLoop <= USED 0
audiohal_adaptor_unbind <= USED 0
audiohal_bind_rx_handler <= USED 0
;FILE AudioInit.o
ACMAudioFormatSupported <= USED 0
audioInited <= USED 0
;FILE AudioTriState.o
;FILE Biquad_IIR.o
;FILE COMCfg.o
COMCfgFindFirst <= USED 0
COMCfgFindNext <= USED 0
COMCfgGetParameter <= USED 0
COMCfgPhase1Init <= USED 0
COMCfgReadName <= USED 0
IsCOMCfgValid <= USED 0
;FILE CPMediaManagerClient.o
_ZN14streamingmedia20CPMediaManagerClient11AddBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient11EnableVoLTEEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14GetMediaConfigE15SDPMeida_Config <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14RemoveBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15SetVoLTEAddressEPKc <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15getMediaSessionEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient24SetConfigCommonInterfaceEjRKNS_6ParcelE <= USED 0
_ZN14streamingmedia20CPMediaManagerClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaManagerClient8UnitTestERKN7android7String8Ei <= USED 0
_ZN14streamingmedia20CPMediaManagerClientD2Ev <= USED 0
;FILE CPMediaSessionClient.o
_ZN14streamingmedia20CPMediaSessionClient10getRtpPortEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7getMuteEb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7setMuteEbb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient8SendDTMFEPK16Media_DTMFData_t <= USED 0
;FILE CPMediaStreamClient.o
_ZN14streamingmedia19CPMediaStreamClient12GetLocalPortEPi <= USED 0
_ZN14streamingmedia19CPMediaStreamClient20GetStreamCodecConfigE26Media_SessionCodecConfig_ti15Media_DirectionPjPv <= USED 0
;FILE CapNeg.o
;FILE CapNegMediaStreamToSDP.o
;FILE CapNegSDPToMediaStream.o
;FILE CapNegUtils.o
CNGetAttributeFromList <= USED 0
CNGetProfileLevelIdValue <= USED 0
CN_Destroy_MediaStreamList <= USED 0
CN_Destroy_SdpSessionInfo <= USED 0
dump_sdp_session_info <= USED 0
;FILE CellularAL_AT.o
CellularAL_Reject <= USED 0
CellularAL_SetConfig <= USED 0
;FILE CpVolume.o
;FILE DSPDebugConfig.o
;FILE DSPDebugConfig_NVM.o
;FILE DTMFFormatParameter.o
;FILE DTMF_Para_Gen.o
;FILE DiagSig_PS.o
GetCurrentHISRPointer <= USED 0
L1TraceSignal <= USED 0
;FILE Drc_Loudspeaker.o
;FILE ECM.o
Ecm_indicate_status_msg <= USED 0
Ecm_remove_hdr <= USED 0
;FILE EEHandler.o
eeHandlerSaveDescFileLineParamWarning <= USED 0
eeHandlerSaveDescFileLineWarning <= USED 0
eeHandlerSaveDescWarning <= USED 0
errHandlerSaveToFDIPeriodic <= USED 0
errorHandlerPhase1Init <= USED 0
;FILE EEHandler_Serial.o
DBG_Heartbeat <= USED 0
DBG_HeartbeatMsgSet <= USED 0
;FILE EEHandler_config.o
;FILE EEHandler_fatal.o
EELOG_WaitForAck <= USED 0
EELOG_WaitForEQ <= USED 0
FatalErrorHandler <= USED 0
SilentReset_set <= USED 0
Trigger_SilentReset_For_SDcard <= USED 0
eeDeferredFinalActionRunEx <= USED 0
eeExtExceptionHandlerBind <= USED 0
eeForceNonDeferred <= USED 0
eeGetPcStep <= USED 0
eeHandlerSaveDescFileLineAssertRegs <= USED 0
eeParkThreadEx <= USED 0
eeh_is_data_abort <= USED 0
eeh_set_data_abort <= USED 0
errHandlerFinalAction <= USED 0
lcdDisplayExceptionInfo <= USED 0
;FILE EEHandler_hal.o
AllfilesDone <= USED 0
EELOG_finish_ <= USED 0
SspEeSendEehHalRcvEvent <= USED 0
WDTSRDYActive <= USED 0
WDTSRDYInactive <= USED 0
assertSRDYActive <= USED 0
assertSRDYInactive <= USED 0
ee_delay <= USED 0
ee_delay_us <= USED 0
;FILE EEHandler_handlers.o
;FILE EE_Postmortem.o
;FILE EE_silentReset.o
Is_silentReset <= USED 0
check_data <= USED 0
reset_flag_read <= USED 0
silentReset_Check <= USED 0
;FILE EE_wdtManager.o
WDTMoniterTrigger <= USED 0
eeWdtEehTimeStamp <= USED 0
;FILE EVSRTPAssembler.o
;FILE FDI_Transport.o
ACATwrapper_print_nameList <= USED 0
;FILE FatSysWrapper.o
FDI5_fileinfo_date2str <= USED 0
FDI5_fileinfo_time2str <= USED 0
FDI_MakeDir <= USED 0
FDI_Stat <= USED 0
FDI_feof <= USED 0
FDI_feof_fatsys <= USED 0
FDI_timercount <= USED 0
fdi_sem_lock_check <= USED 0
;FILE FormatParameter.o
;FILE FreqChange.o
Crane_Get_Svc_Level_Values <= USED 0
Delay_32k <= USED 0
Delta_32k_Tick <= USED 0
GetCurrentCpCoreFreq <= USED 0
GetCurrentPsramPhyFreq <= USED 0
GetLastCpuIdleRate <= USED 0
cpcore_fc_fix_bywifi <= USED 0
cpu_dvc_init <= USED 0
efuse_dro <= USED 0
get_prf_num_by_dro_crn <= USED 0
phy_pll2_fc <= USED 0
read_dro <= USED 0
;FILE GBMemoryDump.o
GBMemDumpGetAssertCauseFlag <= USED 0
;FILE GKITick.o
GKIExtTickFunc <= USED 0
GKIExtTickFuncCallBack <= USED 0
GKIMaximumSuspend <= USED 0
;FILE GenericNACKFeedBackPacket.o
_ZN14streamingmedia25GenericNACKFeedBackPacket19ConstructPacketHeadEv <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetBLPEt <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetPIDEt <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetBLPEv <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetPIDEv <= USED 0
;FILE I2C_ttc.o
CheckLevanteID <= USED 0
I2CBlockingMasterReceive <= USED 0
I2CBusStateGetDirect <= USED 0
I2CConfigureDi <= USED 0
I2CConfigureDi_temp <= USED 0
I2CControl <= USED 0
I2CD2Prepare <= USED 0
I2CD2Recover <= USED 0
I2CEnableclockandPin <= USED 0
I2CIntLISR <= USED 0
I2CMasterReceiveData <= USED 0
I2CMasterReceiveDataDirect_CB <= USED 0
I2CMasterSendByReferance <= USED 0
I2CPhase1Init <= USED 0
I2CPhase2Init <= USED 0
I2CUnRegister <= USED 0
I2Ctask <= USED 0
PM8609Buck2_set_1_8 <= USED 0
PM8609LDO_4_set <= USED 0
PM8609LDO_4_set_1_8 <= USED 0
PM8609LDO_4_set_2_9 <= USED 0
PMC_GetLevanteRealType <= USED 0
PMC_GetLevanteType <= USED 0
i2cReceiveDataEnded <= USED 0
;FILE IMEI.o
IMEI2ReadOnly <= USED 0
IMEI2ReadStr <= USED 0
IMEIReadOnly <= USED 0
;FILE InputStream.o
_ZN14streamingmedia11InputStream12prepareAsyncEv <= USED 0
_ZN14streamingmedia11InputStream14initRenderer_lEv <= USED 0
_ZN14streamingmedia11InputStream16postVideoEvent_lEx <= USED 0
_ZN14streamingmedia11InputStream17notifyVideoSize_lEv <= USED 0
_ZN14streamingmedia11InputStream18cancelPlayerEventsEb <= USED 0
_ZN14streamingmedia11InputStream18startAudioPlayer_lEb <= USED 0
_ZN14streamingmedia11InputStream19onPrepareAsyncEventEv <= USED 0
_ZN14streamingmedia11InputStream19postVideoLagEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream19setVideoScalingModeEi <= USED 0
_ZN14streamingmedia11InputStream20postBufferingEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream21postStreamDoneEvent_lEi <= USED 0
_ZN14streamingmedia11InputStream21setVideoScalingMode_lEi <= USED 0
_ZN14streamingmedia11InputStream22shutdownVideoDecoder_lEv <= USED 0
_ZN14streamingmedia11InputStream25postCheckAudioStatusEventEx <= USED 0
_ZN14streamingmedia11InputStream4initEv <= USED 0
_ZN14streamingmedia11InputStream5resetEv <= USED 0
_ZN14streamingmedia11InputStream7pause_lEb <= USED 0
_ZN14streamingmedia19InputStreamRendererC2Ev <= USED 0
_ZNK14streamingmedia11InputStream4dumpEiRKN7android6VectorINS1_8String16EEE <= USED 0
_ZNK14streamingmedia11InputStream9isPlayingEv <= USED 0
;FILE LzmaDec.o
LzmaDec_Allocate <= USED 0
LzmaDec_DecodeToBuf <= USED 0
LzmaDec_Free <= USED 0
;FILE LzmaLib.o
;FILE MQTTConnectClient.o
;FILE MQTTDeserializePublish.o
;FILE MQTTPacket.o
MQTTPacket_equals <= USED 0
MQTTPacket_read <= USED 0
MQTTPacket_readnb <= USED 0
bufchar <= USED 0
getLenStringLen <= USED 0
;FILE MQTTSerializePublish.o
MQTTSerialize_puback <= USED 0
;FILE MQTTSubscribeClient.o
;FILE MQTTUnsubscribeClient.o
;FILE MRD.o
BtIDRead <= USED 0
GetMRDBakFlashAddress <= USED 0
MRDFileDirFirst <= USED 0
MRDFileDirNext <= USED 0
MRDGetBufferAddr <= USED 0
MRDInit <= USED 0
MRDItemGet <= USED 0
imei_data_to_rdisk <= USED 0
isMRDOperationAllowed <= USED 0
mrd_read_item_to_rdisk <= USED 0
myhex2str <= USED 0
nvm_data_to_rdisk <= USED 0
rdisk_to_imei_data <= USED 0
version_compare <= USED 0
;FILE MediaAudioStream.o
;FILE MediaBuffer.o
_ZN14streamingmedia11MediaBuffer5claimEv <= USED 0
_ZN14streamingmedia11MediaBuffer5cloneEv <= USED 0
_ZN14streamingmedia11MediaBufferC1EPvj <= USED 0
_ZN14streamingmedia11MediaBufferC1ERKN7android2spINS_7ABufferEEE <= USED 0
;FILE MediaBufferGroupT.o
;FILE MediaBufferPool.o
;FILE MediaDescription.o
;FILE MediaManager.o
_ZN14streamingmedia12MediaManager12getDTMFEventEPjPc <= USED 0
_ZN14streamingmedia12MediaManager14GetMediaConfigEv <= USED 0
_ZN14streamingmedia12MediaManager14logMediaconfigE17SDPMedia_Config_t <= USED 0
_ZN14streamingmedia12MediaManager15SetCameraConfigEi14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager18getCodecCapabilityEPcP16Media_CodecCap_t <= USED 0
_ZN14streamingmedia12MediaManager19GetCameraCapabilityEiPiR14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager24GetCameraCapabilityCountEv <= USED 0
_ZN14streamingmedia12MediaManager9getCameraEv <= USED 0
_ZN14streamingmedia12MediaManager9setCameraEP14Media_CamCap_t <= USED 0
;FILE MediaSession.o
_ZN14streamingmedia12MediaSession10getRtpPortEv <= USED 0
;FILE MediaSource.o
_ZN14streamingmedia11MediaSource11ReadOptions11clearSeekToEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions5resetEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setLateByEx <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setSeekToExNS1_8SeekModeE <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptionsC1Ev <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getLateByEv <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getSeekToEPxPNS1_8SeekModeE <= USED 0
;FILE MediaSourceT.o
_ZN14streamingmedia12MediaSourceT12readVecBlockERN7android6VectorIPNS_18MediaBufferWrapperEEEx <= USED 0
_ZN14streamingmedia12MediaSourceT9readBlockEPPNS_11MediaBufferEx <= USED 0
_ZN14streamingmedia12MediaSourceT9setConfigEPNS_8MetaDataE <= USED 0
;FILE MediaStream.o
_ZN14streamingmedia11MediaStream11stopReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12pauseReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12startReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream13resumeReceiveEv <= USED 0
;FILE MetaData.o
_ZN14streamingmedia8MetaData10setCStringEjPKc <= USED 0
_ZN14streamingmedia8MetaData10setPointerEjPv <= USED 0
_ZN14streamingmedia8MetaData10typed_data11freeStorageEv <= USED 0
_ZN14streamingmedia8MetaData10typed_data15allocateStorageEj <= USED 0
_ZN14streamingmedia8MetaData10typed_data5clearEv <= USED 0
_ZN14streamingmedia8MetaData10typed_dataC1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataD1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataaSERKS1_ <= USED 0
_ZN14streamingmedia8MetaData11findCStringEjPPKc <= USED 0
_ZN14streamingmedia8MetaData6removeEj <= USED 0
_ZN14streamingmedia8MetaData7setRectEjiiii <= USED 0
_ZN14streamingmedia8MetaData8findRectEjPiS1_S1_S1_ <= USED 0
_ZN14streamingmedia8MetaData8setFloatEjf <= USED 0
_ZN14streamingmedia8MetaData8setInt64Ejx <= USED 0
_ZN14streamingmedia8MetaData9findFloatEjPf <= USED 0
_ZN14streamingmedia8MetaData9findInt64EjPx <= USED 0
_ZN14streamingmedia8MetaDataC1ERKS0_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data7getDataEPjPPKvS2_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data8asStringEv <= USED 0
_ZNK14streamingmedia8MetaData9dumpToLogEv <= USED 0
;FILE MrvXML.o
AddAttrToXML <= USED 0
AddElementToXML <= USED 0
AddToList <= USED 0
DeleteAttrFromXML <= USED 0
DeleteElementFromXML <= USED 0
DeleteFromList <= USED 0
DeleteFromListTial <= USED 0
FreeList <= USED 0
InitList <= USED 0
MrvAddCData <= USED 0
MrvAttributeAttach <= USED 0
MrvCountElement <= USED 0
MrvCreateElements <= USED 0
MrvCreateXMLString <= USED 0
MrvCreateXMLStringR <= USED 0
MrvEnumPrintf <= USED 0
MrvGetClearTags <= USED 0
MrvGetError <= USED 0
;FILE MrvXMLUtil.o
;FILE NTPTime.o
_ZN14streamingmedia7NTPTime7addmsecEj <= USED 0
_ZN14streamingmedia7NTPTimeltERKS0_ <= USED 0
;FILE NUtick.o
NUMaximumSuspend <= USED 0
NUTickLisr <= USED 0
NUTickUpdate <= USED 0
NUTickUpdateAfterSuspend <= USED 0
dumpNuTickDelayInfo <= USED 0
;FILE NetEQWrapper.o
RTPInjection2IMS <= USED 0
ReceiveRTP <= USED 0
_ZN14streamingmedia12NetEQWrapper12getAudioInfoERiS1_S1_ <= USED 0
_ZN14streamingmedia12NetEQWrapper13extractPacketEPs <= USED 0
;FILE NetworkSelectionAL_AT.o
networkSelectionAL_HdlRegisterNotify <= USED 0
networkSelectionAL_HdlUnregisterNotify <= USED 0
;FILE OutputStream.o
_ZN14streamingmedia12OutputStream17createAudioSourceEv <= USED 0
_ZN14streamingmedia12OutputStream17setupVideoEncoderEN7android2spINS_11MediaSourceEEEiPS4_ <= USED 0
_ZN14streamingmedia12OutputStream19decoderProfileLevelEhhh <= USED 0
_ZN14streamingmedia12OutputStream5resetEv <= USED 0
_ZNK14streamingmedia12OutputStream5mutedEv <= USED 0
;FILE PCA_api.o
AucSwitchAudioOn <= USED 0
AudioPatch_DisablePcmStream <= USED 0
LteVoiceSuspend_internal <= USED 0
MuteVoicepath <= USED 0
RegisterCallback <= USED 0
SetAudioVoLTE <= USED 0
SetCTMControl <= USED 0
SetDebugCmd <= USED 0
SetIPCControl <= USED 0
SetStreamPriority <= USED 0
SetVoiceEnhanceModuleControl <= USED 0
StartVoIPAudio <= USED 0
StopVoIPAudio <= USED 0
sacNotifyAudioConnected <= USED 0
;FILE Parcel.o
_ZN14streamingmedia14acquire_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS0_2wpINS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS1_INS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14release_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS0_2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS1_INS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel10appendFromEPKS0_jj <= USED 0
_ZN14streamingmedia6Parcel10writeFloatEf <= USED 0
_ZN14streamingmedia6Parcel10writeInt32Ei <= USED 0
_ZN14streamingmedia6Parcel10writeInt64Ex <= USED 0
_ZN14streamingmedia6Parcel11finishWriteEj <= USED 0
_ZN14streamingmedia6Parcel11setDataSizeEj <= USED 0
_ZN14streamingmedia6Parcel11writeDoubleEd <= USED 0
_ZN14streamingmedia6Parcel11writeIntPtrEi <= USED 0
_ZN14streamingmedia6Parcel11writeObjectERKNS_18flat_binder_objectEb <= USED 0
_ZN14streamingmedia6Parcel12pushAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel12restartWriteEj <= USED 0
_ZN14streamingmedia6Parcel12writeCStringEPKc <= USED 0
_ZN14streamingmedia6Parcel12writeInplaceEj <= USED 0
_ZN14streamingmedia6Parcel12writeString8ERKN7android7String8E <= USED 0
_ZN14streamingmedia6Parcel13continueWriteEj <= USED 0
_ZN14streamingmedia6Parcel13writeString16EPKtj <= USED 0
_ZN14streamingmedia6Parcel13writeString16ERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel13writeUnpaddedEPKvj <= USED 0
_ZN14streamingmedia6Parcel14acquireObjectsEv <= USED 0
_ZN14streamingmedia6Parcel14releaseObjectsEv <= USED 0
_ZN14streamingmedia6Parcel15restoreAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel15setDataCapacityEj <= USED 0
_ZN14streamingmedia6Parcel15writeWeakBinderERKN7android2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel16writeNoExceptionEv <= USED 0
_ZN14streamingmedia6Parcel17writeStrongBinderERKN7android2spINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel19ipcSetDataReferenceEPKhjPKjjPFvPS0_S2_jS4_jPvES6_ <= USED 0
_ZN14streamingmedia6Parcel19writeFileDescriptorEib <= USED 0
_ZN14streamingmedia6Parcel19writeInterfaceTokenERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel20closeFileDescriptorsEv <= USED 0
_ZN14streamingmedia6Parcel22writeDupFileDescriptorEi <= USED 0
_ZN14streamingmedia6Parcel5writeEPKvj <= USED 0
_ZN14streamingmedia6Parcel5writeERKN7android11FlattenableE <= USED 0
_ZN14streamingmedia6Parcel6removeEjj <= USED 0
_ZN14streamingmedia6Parcel7setDataEPKhj <= USED 0
_ZN14streamingmedia6Parcel8freeDataEv <= USED 0
_ZN14streamingmedia6Parcel8growDataEj <= USED 0
_ZN14streamingmedia6Parcel8setErrorEi <= USED 0
_ZNK14streamingmedia6Parcel10errorCheckEv <= USED 0
_ZNK14streamingmedia6Parcel10ipcObjectsEv <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEPd <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEv <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEPi <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEv <= USED 0
_ZNK14streamingmedia6Parcel10readObjectEb <= USED 0
_ZNK14streamingmedia6Parcel10scanForFdsEv <= USED 0
_ZNK14streamingmedia6Parcel11ipcDataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel11readCStringEv <= USED 0
_ZNK14streamingmedia6Parcel11readInplaceEj <= USED 0
_ZNK14streamingmedia6Parcel11readString8Ev <= USED 0
_ZNK14streamingmedia6Parcel12dataCapacityEv <= USED 0
_ZNK14streamingmedia6Parcel12dataPositionEv <= USED 0
_ZNK14streamingmedia6Parcel12objectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel12readString16Ev <= USED 0
_ZNK14streamingmedia6Parcel14checkInterfaceEPNS_7IBinderE <= USED 0
_ZNK14streamingmedia6Parcel14readWeakBinderEv <= USED 0
_ZNK14streamingmedia6Parcel15ipcObjectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel15setDataPositionEj <= USED 0
_ZNK14streamingmedia6Parcel16enforceInterfaceERKN7android8String16EPNS_14IPCThreadStateE <= USED 0
_ZNK14streamingmedia6Parcel16readStrongBinderEv <= USED 0
_ZNK14streamingmedia6Parcel17readExceptionCodeEv <= USED 0
_ZNK14streamingmedia6Parcel18hasFileDescriptorsEv <= USED 0
_ZNK14streamingmedia6Parcel18readFileDescriptorEv <= USED 0
_ZNK14streamingmedia6Parcel19readString16InplaceEPj <= USED 0
_ZNK14streamingmedia6Parcel4dataEv <= USED 0
_ZNK14streamingmedia6Parcel4readERN7android11FlattenableE <= USED 0
_ZNK14streamingmedia6Parcel7ipcDataEv <= USED 0
_ZNK14streamingmedia6Parcel7objectsEv <= USED 0
_ZNK14streamingmedia6Parcel8dataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel9dataAvailEv <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEPf <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEv <= USED 0
_ZNK14streamingmedia6Parcel9readInt32EPi <= USED 0
_ZNK14streamingmedia6Parcel9readInt64EPx <= USED 0
_ZNK14streamingmedia6Parcel9readInt64Ev <= USED 0
;FILE PayloadEncap.o
;FILE PicoIms3gppImsXmlInterpreter.o
;FILE PicoImsInit.o
IMS_ClearOptionalHeader <= USED 0
IMS_ClearPreCondition <= USED 0
IMS_ClearProxy <= USED 0
IMS_ClearServer <= USED 0
IMS_DeleteUser <= USED 0
IMS_DisableSendingRegister <= USED 0
IMS_GetDefaultUser <= USED 0
IMS_GetNumPublicUser <= USED 0
IMS_GetRegisteredContact <= USED 0
IMS_GetSecAgreeConfig <= USED 0
IMS_GetSecAgreeStatus <= USED 0
IMS_GetStackProperty <= USED 0
IMS_GetTimer <= USED 0
IMS_GetTransport <= USED 0
IMS_GetUAHd <= USED 0
IMS_IsStackInit <= USED 0
IMS_ReSubscribe <= USED 0
IMS_SMIPSendAbortSMMA <= USED 0
IMS_SMIPSetMaxSmRetry <= USED 0
IMS_SMIPSetMaxSmmaRetry <= USED 0
IMS_SMIPSetRecvSupport <= USED 0
IMS_SMIPSetSendSupport <= USED 0
IMS_SMIPSetTr2m <= USED 0
IMS_SMIPSetTram <= USED 0
IMS_SetDNSServer <= USED 0
IMS_SetInstanceId <= USED 0
IMS_SetLocalIdentity <= USED 0
IMS_SetNetworkDetail <= USED 0
IMS_SetOptionalHeader <= USED 0
IMS_SetPUAHd <= USED 0
IMS_SetRtpBasePort <= USED 0
IMS_SetSecAgreeConfig <= USED 0
IMS_SetTransport <= USED 0
IMS_StartMWI <= USED 0
IMS_StopMWI <= USED 0
PICO_IMS_BuildDateGet <= USED 0
PICO_IMS_BuildMachineGet <= USED 0
PICO_IMS_BuildTimeGet <= USED 0
PICO_IMS_BuildUserGet <= USED 0
PICO_IMS_BuildVersionGet <= USED 0
;FILE PicoImsMwi.o
;FILE PicoImsReg.o
;FILE RDisk.o
Rdisk_addto_filelist <= USED 0
;FILE RTC_stub.o
RTCAlarmSet <= USED 0
RTCIsAlarmSet <= USED 0
RTCNotifyOnTimeSetBind <= USED 0
RTCNotifyOnTimeSetEvent <= USED 0
RTCNotifyOnTimeSetUnBind <= USED 0
RTCNotifyUsers <= USED 0
RTCPhase1Init <= USED 0
RTCPhase2Init <= USED 0
RTCPowerUpAlarmCheck <= USED 0
RTCToANSIConvert <= USED 0
SCS_IsAlarmEnabled <= USED 0
;FILE RTPAssembler.o
;FILE RdGenData.o
RDGenGetProdMode <= USED 0
RDGenGetUniqueId <= USED 0
;FILE RefBase.o
_ZN7android7RefBase12weakref_type14attemptIncWeakEPKv <= USED 0
_ZN7android7RefBase12weakref_type7trackMeEbb <= USED 0
_ZN7android7RefBase20extendObjectLifetimeEi <= USED 0
_ZNK7android7RefBase11getWeakRefsEv <= USED 0
_ZNK7android7RefBase12weakref_type12getWeakCountEv <= USED 0
_ZNK7android7RefBase12weakref_type7refBaseEv <= USED 0
_ZNK7android7RefBase12weakref_type9printRefsEv <= USED 0
_ZNK7android7RefBase14forceIncStrongEPKv <= USED 0
_ZNK7android7RefBase14getStrongCountEv <= USED 0
;FILE ReliableData.o
ReliableDataBakCheck <= USED 0
ReliableDataCheck <= USED 0
ReliableDataPhase1 <= USED 0
ReliableDataVerify <= USED 0
getSerialNumber <= USED 0
mrd_nvm_unpack <= USED 0
rd_linkstatus_ind <= USED 0
rf_calibration_status <= USED 0
strStartsWith <= USED 0
;FILE RmiFunctions.o
;FILE RtcpByePacket.o
;FILE RtcpCompoundPacket.o
;FILE RtcpRRPacket.o
_ZNK14streamingmedia12RtcpRRPacket10GotoReportEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpRRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket32GetExtendedHighestSequenceNumberEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket7GetSSRCEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket9GetJitterEj <= USED 0
;FILE RtcpSDESPacket.o
_ZN14streamingmedia14RtcpSDESPacket11GetItemDataEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket12GotoNextItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoFirstItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoNextChunkEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket14GotoFirstChunkEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket11GetItemTypeEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket12GetChunkSSRCEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetChunkCountEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetItemLengthEv <= USED 0
;FILE RtcpSRPacket.o
_ZNK14streamingmedia12RtcpSRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket19GetSenderOctetCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket20GetSenderPacketCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket7GetSSRCEj <= USED 0
;FILE RtcpSession.o
_ZN14streamingmedia11RtcpSession10processBYEEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession11processSDESEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia11RtcpSession16onByeRequestSendERKN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia11RtcpSession7sendByeEv <= USED 0
_ZN14streamingmedia11RtcpSession8sendAVPFERKN7android2spINS_8AMessageEEE <= USED 0
;FILE RtpSession.o
_ZN14streamingmedia10RtpSession10getRtpPortEv <= USED 0
_ZN14streamingmedia13StreamAdapter12getDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter14onSetDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia13StreamAdapter21setNATKeepAliveSchemeEb26MEDIA_RTPKeepaliveScheme_t <= USED 0
_ZN14streamingmedia13StreamAdapter9parseRTCPEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia18RecRtpSocketLooperC1Ev <= USED 0
;FILE RtpSource.o
_ZN14streamingmedia9RtpSource4stopEv <= USED 0
_ZN14streamingmedia9RtpSource5pauseEv <= USED 0
;FILE RtpWriter.o
_ZN14streamingmedia9RtpWriter12addRTPHeaderEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia9RtpWriter13resetSourceIdEj <= USED 0
;FILE SMIP.o
IMS_RLCb <= USED 0
IMS_RLToLLCb <= USED 0
;FILE SMIP2.o
ims_HdlSMIP2SendAck <= USED 0
;FILE SMRL.o
SMRL_GetConfig <= USED 0
;FILE SPI_interface.o
SPI_PIO_Read <= USED 0
SPI_PIO_Write <= USED 0
SPI_PIO_Write_Read <= USED 0
spidrv_dma_work <= USED 0
;FILE SessionDescription.o
_ZN14streamingmedia18SessionDescription3logEPKc <= USED 0
;FILE SharedBuffer.o
_ZNK7android12SharedBuffer4editEv <= USED 0
_ZNK7android12SharedBuffer5resetEj <= USED 0
;FILE SimCard.o
;FILE SimCardAL.o
SimCardAL_Apps_Get <= USED 0
SimCardAL_IMS_HomeDN_Get <= USED 0
SimCardAL_SoftSim_Reset <= USED 0
SimCardAL_SoftSim_Set <= USED 0
SimCardAL_StatusCbRegister <= USED 0
SimCardAL_StatusCbUnRegister <= USED 0
SimCardAL_WLAN_PLMN_Get <= USED 0
SimCardAL_WLAN_SID_Get <= USED 0
;FILE SimCardUtil.o
ascii_dump <= USED 0
;FILE SimFiles.o
;FILE SimSmsStorage.o
SimCardAL_SMIP_GetMWIStatus <= USED 0
SimCardAL_SMIP_GetPreferredMemoryForStorage <= USED 0
SimCardAL_SMIP_SetMWIStatus <= USED 0
;FILE StreamingMediaCodecFactory.o
;FILE String16.o
_ZN7android18terminate_string16Ev <= USED 0
_ZN7android8String1610replaceAllEtt <= USED 0
_ZN7android8String165setToEPKt <= USED 0
_ZN7android8String165setToEPKtj <= USED 0
_ZN7android8String165setToERKS0_ <= USED 0
_ZN7android8String165setToERKS0_jj <= USED 0
_ZN7android8String166appendEPKtj <= USED 0
_ZN7android8String166appendERKS0_ <= USED 0
_ZN7android8String166insertEjPKt <= USED 0
_ZN7android8String166insertEjPKtj <= USED 0
_ZN7android8String166removeEjj <= USED 0
_ZN7android8String169makeLowerEv <= USED 0
_ZN7android8String16C1EPKc <= USED 0
_ZN7android8String16C1EPKcj <= USED 0
_ZN7android8String16C1EPKt <= USED 0
_ZN7android8String16C1EPKtj <= USED 0
_ZN7android8String16C1ERKNS_7String8E <= USED 0
_ZN7android8String16C1ERKS0_ <= USED 0
_ZN7android8String16C1ERKS0_jj <= USED 0
_ZN7android8String16C1Ev <= USED 0
_ZN7android8String16D1Ev <= USED 0
_ZNK7android8String1610startsWithEPKt <= USED 0
_ZNK7android8String1610startsWithERKS0_ <= USED 0
_ZNK7android8String168findLastEt <= USED 0
_ZNK7android8String169findFirstEt <= USED 0
;FILE String8.o
_ZN7android17terminate_string8Ev <= USED 0
_ZN7android7String810appendPathEPKc <= USED 0
_ZN7android7String810lockBufferEj <= USED 0
_ZN7android7String811setPathNameEPKc <= USED 0
_ZN7android7String811setPathNameEPKcj <= USED 0
_ZN7android7String812appendFormatEPKcz <= USED 0
_ZN7android7String812unlockBufferEj <= USED 0
_ZN7android7String812unlockBufferEv <= USED 0
_ZN7android7String813appendFormatVEPKcSt9__va_list <= USED 0
_ZN7android7String816convertToResPathEv <= USED 0
_ZN7android7String85setToEPKcj <= USED 0
_ZN7android7String85setToEPKjj <= USED 0
_ZN7android7String85setToEPKtj <= USED 0
_ZN7android7String86appendERKS0_ <= USED 0
_ZN7android7String86formatEPKcz <= USED 0
_ZN7android7String87formatVEPKcSt9__va_list <= USED 0
_ZN7android7String87toLowerEjj <= USED 0
_ZN7android7String87toLowerEv <= USED 0
_ZN7android7String87toUpperEjj <= USED 0
_ZN7android7String87toUpperEv <= USED 0
_ZN7android7String8C1EPKcj <= USED 0
_ZN7android7String8C1EPKj <= USED 0
_ZN7android7String8C1EPKjj <= USED 0
_ZN7android7String8C1EPKt <= USED 0
_ZN7android7String8C1EPKtj <= USED 0
_ZN7android7String8C1ERKNS_8String16E <= USED 0
_ZNK7android7String810getPathDirEv <= USED 0
_ZNK7android7String810getUtf32AtEjPj <= USED 0
_ZNK7android7String811getBasePathEv <= USED 0
_ZNK7android7String811getPathLeafEv <= USED 0
_ZNK7android7String814find_extensionEv <= USED 0
_ZNK7android7String814getUtf32LengthEv <= USED 0
_ZNK7android7String816getPathExtensionEv <= USED 0
_ZNK7android7String88getUtf32EPj <= USED 0
_ZNK7android7String88walkPathEPS0_ <= USED 0
;FILE SysDynSilicon.o
SysUSIMWakeupIntResetEna <= USED 0
Sys_ACIPCIntEna <= USED 0
Sys_AplpRFD_GetRxTxDelay <= USED 0
Sys_CSSR_WDT_KICK_MODE_BIT_SetIsEnable <= USED 0
Sys_CommpmMemConfRequired <= USED 0
Sys_CommpmUsimNmosEngageInUse <= USED 0
Sys_CrdGBdump <= USED 0
Sys_CrdGBdump_L1DataABuffSize <= USED 0
Sys_IsDataOMSLOverACIPC <= USED 0
Sys_IsProductIDTavorLlike <= USED 0
Sys_IsWorkingWith3dBOffset <= USED 0
Sys_TavorSiliconIs_PV_B0orUpper <= USED 0
Sys_getDSSPMode <= USED 0
Sys_intcIsThirdRegisterExist <= USED 0
Sys_isCpuEshel <= USED 0
Sys_mslGetCpuID <= USED 0
Sys_usbVersion <= USED 0
Sys_usimDetectGpioPinNo <= USED 0
Sys_usimUseDma <= USED 0
Sys_wdtKickRegAddress <= USED 0
;FILE Threads.o
_Z24androidCreateThreadGetIDPFvPvES_PS_ <= USED 0
_ZN7android6Thread11_threadLoopEPv <= USED 0
_ZN7android6ThreadC1Eb <= USED 0
_ZNK7android6Thread11exitPendingEv <= USED 0
androidCreateThread <= USED 0
androidCreateThreadEtc <= USED 0
androidGetTid <= USED 0
androidSetCreateThreadFunc <= USED 0
;FILE TimedEventQueue.o
_ZN14streamingmedia15TimedEventQueue11cancelEventEi <= USED 0
_ZN14streamingmedia15TimedEventQueue11threadEntryEv <= USED 0
_ZN14streamingmedia15TimedEventQueue12cancelEventsEPFbPvRKN7android2spINS0_5EventEEEES1_b <= USED 0
_ZN14streamingmedia15TimedEventQueue13ThreadWrapperEPv <= USED 0
_ZN14streamingmedia15TimedEventQueue15postEventToBackERKN7android2spINS0_5EventEEE <= USED 0
_ZN14streamingmedia15TimedEventQueue18postEventWithDelayERKN7android2spINS0_5EventEEEx <= USED 0
_ZN14streamingmedia15TimedEventQueue22removeEventFromQueue_lEi <= USED 0
_ZN14streamingmedia15TimedEventQueue5startEv <= USED 0
_ZN14streamingmedia15TimedEventQueue9postEventERKN7android2spINS0_5EventEEE <= USED 0
;FILE UART_atcmd.o
uart_atcmd_init <= USED 0
;FILE UART_drv.o
uartdrv_change_to_115200 <= USED 0
uartdrv_change_to_3000000 <= USED 0
uartdrv_change_to_921600 <= USED 0
uartdrv_clear_receive_fifo <= USED 0
uartdrv_enable_int <= USED 0
uartdrv_flush <= USED 0
uartdrv_get_IntStatus <= USED 0
uartdrv_get_base_addr <= USED 0
uartdrv_get_baudrate <= USED 0
uartdrv_get_dma_rx_channel <= USED 0
uartdrv_get_private_data <= USED 0
uartdrv_getc <= USED 0
uartdrv_getchar_noblock <= USED 0
uartdrv_is_RxInt <= USED 0
uartdrv_mask_RxInt <= USED 0
uartdrv_rts_ctl <= USED 0
uartdrv_rts_is_enable <= USED 0
uartdrv_set_baudrate <= USED 0
;FILE UART_utils.o
PMIC_Set_UART_3_3V <= USED 0
UARTCharacterWrite <= USED 0
UARTFIFORead <= USED 0
UARTRxWakeLISR <= USED 0
diag_send_data_2uart <= USED 0
send_data_2uart <= USED 0
;FILE USSI.o
USSI_MsgHandler <= USED 0
ims_HdlCancelUSSI <= USED 0
ims_HdlInitUSSI <= USED 0
ims_HdlSendUSSI <= USED 0
ims_HdlShutUSSI <= USED 0
;FILE Unicode.o
strcmp16 <= USED 0
strcpy16 <= USED 0
strlen16 <= USED 0
strlen32 <= USED 0
strncmp16 <= USED 0
strncpy16 <= USED 0
strnlen16 <= USED 0
strnlen32 <= USED 0
strzcmp16 <= USED 0
strzcmp16_h_n <= USED 0
utf16_to_utf8 <= USED 0
utf16_to_utf8_length <= USED 0
utf32_from_utf8_at <= USED 0
utf32_to_utf8 <= USED 0
utf32_to_utf8_length <= USED 0
utf8_length <= USED 0
utf8_to_utf16 <= USED 0
utf8_to_utf16_length <= USED 0
utf8_to_utf16_no_null_terminator <= USED 0
utf8_to_utf32 <= USED 0
utf8_to_utf32_length <= USED 0
;FILE UsbMgr.o
M_KiOsGetSignalInt <= USED 0
USBMgrEndpointStall <= USED 0
USBMgrFlushRxBuffer <= USED 0
USBMgrFlushTxQueue <= USED 0
USBMgrGetHWCfgEPMaxPacketSize <= USED 0
USBMgrRegister <= USED 0
USBMgrTempFuncUpdateHardCodedUSBDescriptor <= USED 0
;FILE Utils.o
;FILE VectorImpl.o
_ZN7android10VectorImpl11appendArrayEPKvj <= USED 0
_ZN7android10VectorImpl11setCapacityEj <= USED 0
_ZN7android10VectorImpl12appendVectorERKS0_ <= USED 0
_ZN7android10VectorImpl13insertArrayAtEPKvjj <= USED 0
_ZN7android10VectorImpl14insertVectorAtERKS0_j <= USED 0
_ZN7android10VectorImpl3addEv <= USED 0
_ZN7android10VectorImpl3popEv <= USED 0
_ZN7android10VectorImpl4pushEv <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_E <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_PvES3_ <= USED 0
_ZN7android10VectorImpl8insertAtEjj <= USED 0
_ZN7android10VectorImpl9replaceAtEj <= USED 0
_ZN7android16SortedVectorImpl5mergeERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImpl5mergeERKS0_ <= USED 0
_ZN7android16SortedVectorImplC2ERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImplaSERKS0_ <= USED 0
_ZNK7android10VectorImpl8itemSizeEv <= USED 0
_ZNK7android16SortedVectorImpl7orderOfEPKv <= USED 0
;FILE Vmbuf.o
vdup <= USED 0
vrealloc <= USED 0
vtrim <= USED 0
;FILE WS_CmdMsg.o
IPCCommMessageRead <= USED 0
IPCCommReplyRequiredCommandSend <= USED 0
IPCCommReplyRequiredNewOpcodeCommandSend <= USED 0
cmdMsgReturnOSObjects <= USED 0
handleReplyRequiredRdIntToDSP <= USED 0
;FILE WS_CmnSrv.o
APLP_AAAPDataBufferFreeNotification <= USED 0
APLP_AAAPDataChannelFreeNotification <= USED 0
APLP_AAAPDataReceivedNotification <= USED 0
APLP_AAAPGetDataPointer <= USED 0
APLP_AAAPModeRegistration <= USED 0
BsramMemoryDumpRoutine <= USED 0
IPCCommClose <= USED 0
IPCCommConfig <= USED 0
IPCCommGetConfigure <= USED 0
IPCCommGetVersion <= USED 0
IPCCommMemoryDumpRoutine <= USED 0
IPCCommPhase1Init <= USED 0
IPCCommSpyCmd <= USED 0
IPCCommStatus <= USED 0
IPCCommUnSpyCmd <= USED 0
cmnSrvCheckAppIDValidity <= USED 0
cmnSrvMsgBroadcastingRoutine <= USED 0
cmnSrvReturnOSObjects <= USED 0
cmnSrvSpyCmdBroadcastingRoutine <= USED 0
plDratApplicationIdReturn <= USED 0
;FILE WS_Data.o
IPCCommFreeDataChannel <= USED 0
IPCCommSendSelfEvent <= USED 0
IPCErrorIndicationCallBackBind <= USED 0
dataReturnOSObjects <= USED 0
;FILE WS_HwAcs.o
hwAcsConfigureInterrupts <= USED 0
hwAcsDisableInterrupts <= USED 0
reset_MSA_fix_silent_reset <= USED 0
;FILE WS_IPCICATFunc.o
AAAPDataBufferFreeNotification <= USED 0
AAAPDataChannelFreeNotification <= USED 0
AAAPDataReceivedNotification <= USED 0
AAAPGetDataPointer <= USED 0
getFilterArrayPointer <= USED 0
;FILE WS_TcmSrv.o
IPCCreateEvent <= USED 0
;FILE Xmlussdformatter.o
;FILE a_refl.o
;FILE aam.o
AAMAcquireHandle <= USED 0
AAMAcquireHandleForLptBeforeIntDis <= USED 0
AAMBeforeIntDisRegister <= USED 0
AAMPhase1Init <= USED 0
AAMPhase2Init <= USED 0
AAM_GET_BIT_MASK_BY_HANDLE <= USED 0
mmiAAMInit <= USED 0
mmiAAMRegister <= USED 0
mmiAAMRequest <= USED 0
mmiAAMUnregister <= USED 0
;FILE abcfg.o
abcfGetHashTableArraySize <= USED 0
ablmSetTeClose <= USED 0
ablmSetTeOpen <= USED 0
;FILE abem_fnc.o
;FILE abgl_fnc.o
printContextInfo <= USED 0
;FILE abgp_fnc.o
abgpDoApexDataSwtichReq <= USED 0
abgpStringCmpI <= USED 0
abmmRmUserModeIncludeLteMode <= USED 0
;FILE ablm_fn2.o
ablmInitialiseFdnList <= USED 0
;FILE ablm_pb.o
;FILE ablm_ufn.o
ablmAlphaComparePartial <= USED 0
;FILE ablm_util.o
ablmDnCopyToSim <= USED 0
;FILE abmm_blm.o
abmmBlGetLteArfcnListsFromCuList <= USED 0
abmmBlUpdateLteArfcnListsFromCuList <= USED 0
abmmBlmGetArfcnFromNvm <= USED 0
abmmBlmInsertArfcnNvm <= USED 0
abmmRmGetModemInfo <= USED 0
;FILE abmm_bmm.o
abmmBmNewFddLteBandMode <= USED 0
abmmBmNewTdLteBandMode <= USED 0
abmmBmSetFrequencyCapability <= USED 0
abmmBmSetGPRSServiceType <= USED 0
;FILE abmm_cdm.o
abmmBmSetFddBandFromMedataInit <= USED 0
abmmCdGetMsCapability <= USED 0
abmmDoMmrUpdateNvmInd <= USED 0
;FILE abmm_plm.o
abmmPlAddToTempFplmnList <= USED 0
abmmPlCopyAvailableLtePlmns <= USED 0
abmmPlDeleteFromPlmnListWithAct <= USED 0
abmmPlDestroyTempForbiddenList <= USED 0
abmmPlGprsServicesPresent <= USED 0
abmmPlIsLastPlmn <= USED 0
abmmPlIsOPlmnsForRrc <= USED 0
abmmPlIsPowerOn <= USED 0
abmmPlNumPrefPlmns <= USED 0
;FILE abmm_pmm.o
abmmPmResuming <= USED 0
abmmPmUpdateAllNvmData <= USED 0
;FILE abmm_rmm.o
abmmDoMmrAbortRegCnf <= USED 0
abmmPlIsRoamingForbidenPlmn <= USED 0
abmmRmAreAllLteOnlyHplmnsInList <= USED 0
abmmRmCheckInvalidImei <= USED 0
abmmRmContinue <= USED 0
abmmRmGetCurrentPlmn <= USED 0
abmmRmGetManualPlmn <= USED 0
abmmRmHplmnIsBoltSmartfrenOrnSim <= USED 0
abmmRmHplmnIsCMHKSim <= USED 0
abmmRmHplmnIsChinaPlmn <= USED 0
abmmRmHplmnIsPlay <= USED 0
abmmRmInformSACRegCmccRoaming <= USED 0
abmmRmInterruptReg <= USED 0
abmmRmIsHplmnForOtherTask <= USED 0
abmmRmIsNarrowHplmnSearchNeededAndCalcNetworkMode <= USED 0
abmmRmIsPlmnAwaitingMmRetry <= USED 0
abmmRmIsPowerUp <= USED 0
abmmRmPartiallyRegistered <= USED 0
abmmRmPeerTaskRecoverStatus <= USED 0
abmmRmReceivedForbiddenNatLaInDualRatManualMode <= USED 0
abmmRmReceivedForbiddenPlmnInDualRatManualMode <= USED 0
abmmRmSetOtaRefreshBlockImsiDetach <= USED 0
abmmRmSetSearchCtrlBits <= USED 0
abmmRmStateEcallExplicitReg <= USED 0
abmmRmStopTimersWithoutHplmnTimer <= USED 0
abmmRmT3245EventHandle <= USED 0
abmmRmT3245Expiry <= USED 0
abmmRmT3245Restart <= USED 0
abmmRmT3245Stop <= USED 0
abmmRmWhetherEPlmnlistNeedUpdate <= USED 0
get_commonReserved_3_value <= USED 0
get_commonReserved_4_value <= USED 0
;FILE abmm_sig.o
abmmDoAnrm2DeleteDataCnf <= USED 0
abmmSendAnrm2DeleteDataReq <= USED 0
abmmSendAnrm2DeleteFileRecordReq <= USED 0
abmmSendAnrm2ListFileRecordsReq <= USED 0
abmmSendAnrm2ReadFileRecordReq <= USED 0
abmmSendAnrm2WriteFileRecordReq <= USED 0
abmmSendMmrExplicitEcallInactivityReq <= USED 0
abmmSgFillLteBands <= USED 0
abmmSgFillLteBandsForResume <= USED 0
abmmSgIsOpPrefPlmn <= USED 0
abmmSigGetNumOfBand <= USED 0
abmmsendMmrSetMedata <= USED 0
;FILE abmm_utils.o
abccCheckCurrentIsEccCall <= USED 0
abccIsInCall <= USED 0
abmmBlGetBaListForGrr <= USED 0
abmmDelNvmFPlmnsList <= USED 0
abmmIfContinueSearch <= USED 0
abmmInsertFplmnToNvmFPlmnsList <= USED 0
strnlen_util <= USED 0
;FILE abmmdebug.o
;FILE abmmmain.o
abmmDoResume <= USED 0
;FILE abnv_init.o
SetCipheringA5Algo <= USED 0
SetGprsAlgo <= USED 0
abmmCdHandleMeCustomConfig <= USED 0
;FILE absh_fnc.o
abshGetControllingTask <= USED 0
abshRestoreAbActiveSim <= USED 0
abshSendIndExcludeTask <= USED 0
abshSetAbActiveSim <= USED 0
;FILE absh_lcl.o
abshDoMmiKeyPressInd <= USED 0
abshDoMmiPowerKeyInd <= USED 0
abshOnQueue <= USED 0
abshSendApexGlErrorInd <= USED 0
;FILE abshmain.o
abshTask1 <= USED 0
abshTask21 <= USED 0
abshTask2ExitRoutine <= USED 0
abshTaskExitRoutine <= USED 0
;FILE absi_fnc.o
absiAclStatus <= USED 0
absiGetBaList <= USED 0
absiGetCbmidList <= USED 0
absiGetCurrentHplmn <= USED 0
absiGetImsi <= USED 0
absiGetMsAdminAdditionalInfo <= USED 0
absiGetPlmnFromImsi <= USED 0
absiGetRplmn <= USED 0
absiGetRplmnFromEFPsloci <= USED 0
absiGetSimPhase <= USED 0
absiGetSimProfile <= USED 0
absiGsmAccessAvailable <= USED 0
absiIsNetworkCodeInUse <= USED 0
absiLpStatus <= USED 0
absiResetMepCodes <= USED 0
absiResetNetworkCodes <= USED 0
absiSetAclStatus <= USED 0
absiSetPlmnModeBit <= USED 0
absiSetRatBalancingMode <= USED 0
absiSimChanged <= USED 0
absiUpdateRplmn <= USED 0
;FILE absisigs.o
absiDoAlsiReadNasConfigCnf <= USED 0
absiDoAlsiReadNasConfigReq <= USED 0
absiDoApexSimIdleReq <= USED 0
absiFetchSimDataIfAny <= USED 0
;FILE absm_apx.o
;FILE absm_ems.o
;FILE absm_ini.o
;FILE absm_sim.o
;FILE absm_trn.o
absmGetPendingTransactions <= USED 0
;FILE absm_ts.o
;FILE absm_uti.o
absmDeleteSmspParameters <= USED 0
absmGetFreeStoredMsg <= USED 0
absmGetMsgByReference <= USED 0
;FILE abst_fn3.o
;FILE abst_fn4.o
;FILE abst_fn5.o
;FILE abst_fn6.o
;FILE access_intf.o
AccessIntf_GetSSConfig <= USED 0
AccessIntf_NW_GetCaps <= USED 0
AccessIntf_NW_GetConfig <= USED 0
AccessIntf_NW_GetProperty <= USED 0
AccessIntf_NW_SetConfig <= USED 0
AccessIntf_NW_SetProperty <= USED 0
AccessIntf_PS_GetConfig <= USED 0
AccessIntf_PS_GetProperty <= USED 0
AccessIntf_QueryFirstAvailableNetwork <= USED 0
AccessIntf_QueryNetwork <= USED 0
AccessIntf_QueryNextAvailableNetwork <= USED 0
AccessIntf_SelectNetwork <= USED 0
AccessIntf_SetSSConfig <= USED 0
;FILE acmDspIf.o
ACMDspIfAudioCTMCtrl <= USED 0
ACMDspIfAudioDownLinkModeGet <= USED 0
ACMDspIfAudioIPCCtrl <= USED 0
ACMDspIfAudioSetSspConfiguration <= USED 0
ACMDspIfAudioUpLinkModeGet <= USED 0
;FILE acm_COMM.o
;FILE acm_Codec.o
GSSP_SEL_BY_CP <= USED 0
GSSP_SEL_REG_ADDR <= USED 0
IsCPOnlyBoard <= USED 0
;FILE acm_atc.o
ATC_Process_PCM_data <= USED 0
;FILE acm_audio_effect.o
audio_effect_init <= USED 0
;FILE acm_audio_record.o
audio_record_open <= USED 0
audio_record_read <= USED 0
destroy_record_task_env <= USED 0
;FILE acm_audio_tone.o
;FILE acm_audio_track.o
acm_audio_track_get_available <= USED 0
acm_audio_track_get_softgain <= USED 0
audio_track_close <= USED 0
destroy_track_task_env <= USED 0
;FILE acm_audio_vocoder.o
;FILE acm_calibration.o
;FILE acm_control.o
ACMAudioBindECallGetHandler <= USED 0
ACMAudioOutGainSet <= USED 0
ACMAudioSetDTMFMode <= USED 0
ACMAudio_GetDSPSettings <= USED 0
ACMVoiceInGainSet <= USED 0
ACMVoiceMuteIn <= USED 0
ACMVoiceMuteOut <= USED 0
ACMVoiceOutGainSet <= USED 0
ACMVoiceSideToneGainSet <= USED 0
set_phone_state <= USED 0
;FILE acm_stream.o
ACMStreamReset <= USED 0
;FILE add.o
;FILE aes.o
mbedtls_aes_decrypt <= USED 0
mbedtls_aes_encrypt <= USED 0
;FILE afbc_drv.o
cam_afbc_enc_auto_stop <= USED 0
cam_afbc_enc_deinit_module <= USED 0
cam_afbc_enc_disable_interupt <= USED 0
cam_afbc_enc_disable_surface <= USED 0
cam_afbc_enc_enable_interupt <= USED 0
cam_afbc_enc_enable_surface <= USED 0
cam_afbc_enc_init_module <= USED 0
cam_afbc_enc_set_bodybuf_adr <= USED 0
cam_afbc_enc_set_headbuf_adr <= USED 0
cam_afbc_enc_set_input_chroma_buf_adr <= USED 0
cam_afbc_enc_set_input_chroma_buf_stride <= USED 0
cam_afbc_enc_set_input_wr_order <= USED 0
cam_afbc_enc_set_inputbuf_adr <= USED 0
cam_afbc_enc_set_inputbuf_stride <= USED 0
cam_afbc_enc_setbufsize <= USED 0
cam_afbc_enc_setfmtspec <= USED 0
cam_afbc_enc_setup_bbox <= USED 0
cam_afbc_enc_start <= USED 0
cam_afbc_mdelay <= USED 0
cam_afbc_write32 <= USED 0
camafbc_irq_disable <= USED 0
camafbc_irq_enable <= USED 0
camafbc_test_enc_auto_stop <= USED 0
camafbc_test_enc_callback_irq_opt <= USED 0
camafbc_test_enc_start <= USED 0
camafbc_test_enc_stop <= USED 0
;FILE agc.o
_Z18energy_new_WrapperPssPi <= USED 0
_Z18energy_old_WrapperPssPi <= USED 0
;FILE amidala_md5.o
sd_md5 <= USED 0
;FILE amr_payload.o
unpackAmrFrame <= USED 0
;FILE amr_vocoder_api.o
amrGetPCMInHandle <= USED 0
amrStart <= USED 0
;FILE amrdecode.o
;FILE amrencode.o
AMREncodeReset <= USED 0
;FILE amrnb_dec_api.o
amrnb_decode_get_amr <= USED 0
amrnb_decode_get_rate <= USED 0
amrnb_decode_loop <= USED 0
amrnb_decode_write <= USED 0
;FILE amrnb_enc_api.o
amrnb_encode_loop <= USED 0
amrnb_encode_read <= USED 0
amrnb_encode_set_rate <= USED 0
;FILE api_lib.o
netconn_close <= USED 0
netconn_delete_tcp <= USED 0
netconn_gethostbyname <= USED 0
netconn_recv_udp_raw_netbuf <= USED 0
netconn_sendto <= USED 0
netconn_write <= USED 0
;FILE api_msg.o
;FILE aplp_stub_ds3.o
BcchGsmToUtranReselection <= USED 0
GSMStopPeriodicUpdate <= USED 0
GsmStartPeriodicUpdateTemp <= USED 0
IsWbSleep <= USED 0
ModifySIMID <= USED 0
PlMsrCheckIfCellSearchActiveAndAbort <= USED 0
PopSIMID <= USED 0
RspGet3GCalibrationStatus <= USED 0
crcLengthConvert <= USED 0
dlmControllerSend <= USED 0
dlmPrintf <= USED 0
getBchFromOtherRatFlag <= USED 0
getCycleBchCounter <= USED 0
getSchdBitmap <= USED 0
getWbGsmSuspendFlag <= USED 0
gwiAbortBch <= USED 0
gwiAckEndOfWbRfAllocation <= USED 0
gwiClearWbMeasIndicator <= USED 0
gwiDsdsGsm2WbSleepReq <= USED 0
gwiDsdsGsmActivateInd <= USED 0
gwiDsdsGsmActivateRsp <= USED 0
gwiDsdsGsmDeactivateInd <= USED 0
gwiDsdsGsmEarlyWakupReq <= USED 0
gwiDsdsGsmGapCanCelInd <= USED 0
gwiDsdsRcvWbPCHInGsmPsReq <= USED 0
gwiDsdsResumeGsmCnf <= USED 0
gwiDsdsResumeWbReq <= USED 0
gwiDsdsSchdGsmGapFinishedInd <= USED 0
gwiDsdsSchdGsmGapReq <= USED 0
gwiDsdsSchdSetWbSuspendState <= USED 0
gwiDsdsSchdWbGapInd <= USED 0
gwiDsdsSuspendGsmCnf <= USED 0
gwiDsdsSuspendWbReq <= USED 0
gwiDsdsWbActivateRsp <= USED 0
gwiDsdsWbDeactivateRsp <= USED 0
gwiDsdsWbInactivateRsp <= USED 0
gwiGetGsmCellInfoFromAplpDb <= USED 0
gwiGetWbMeasIndicator <= USED 0
gwiGsmBcchDecodeErrInd <= USED 0
gwiGsmL1Ready <= USED 0
gwiIsUarfcnValid <= USED 0
gwiRegisterDeactivate <= USED 0
gwiSendAfcCtrlDefreezeMode <= USED 0
gwiSendPlwGsmBcchDecodeInd <= USED 0
gwiStartBch <= USED 0
gwiUtranCellMeasAtGsmReq <= USED 0
gwiUtranRssiMeasReq <= USED 0
gwiWbDetectedMeasReqInGsm <= USED 0
gwiWbMeasAtGsmTimerExpiration <= USED 0
plAMPlpTransReq <= USED 0
plAMSetCurrentPlpSm <= USED 0
plAMgetWbResumeFlag <= USED 0
plAmAplpWarningHandler <= USED 0
plAmCheckValidityOfBchDecodeParameters <= USED 0
plAmSpyWriteRrcCnfEvent <= USED 0
plAmUpdatePlpSm <= USED 0
plAplpSendEmptyMsg2Schd <= USED 0
plAplpSendMsg2SchdApi <= USED 0
plAplpSetGsmTaskReq2Schd <= USED 0
plAtlGetSfn <= USED 0
plCalibEnterWCDMAMode <= USED 0
plCalibExitWCDMAMode <= USED 0
plCalibGetAcqRssiValue <= USED 0
plCalibSetAcquisition <= USED 0
plCalibStartRxTest <= USED 0
plCalibStartTxTest <= USED 0
plCalibStopTxTest <= USED 0
plDataHandleFrameInterrupt <= USED 0
plMCLClearChain <= USED 0
plMSPccpchSetNumOfShifts <= USED 0
plMSRRakeUpdatePathInfoEvent <= USED 0
plMsDratSet3GRestore <= USED 0
plMsSmSendMsaPowerManagementParameters <= USED 0
plMsrAfcDacTableFileUpdate <= USED 0
plMsrBcchTimerExpired <= USED 0
plMsrClearMcl <= USED 0
plMsrClearMeasureTypesState <= USED 0
plMsrDbClearAllPathsMeasuremnts <= USED 0
plMsrGsWbReset <= USED 0
plMsrGsWbStop <= USED 0
plMsrGsmPlmnPrintDb <= USED 0
plMsrGsmSrvGSMPowerup <= USED 0
plMsrGsmSrvGSMReset <= USED 0
plMsrGsmSrvGSMStartup <= USED 0
plMsrGsmSrvGSMTerminate <= USED 0
plMsrInGsmRestoreMsrModeAndStateIfExist <= USED 0
plMsrInLteRestoreMsrModeAndStateIfExist <= USED 0
plMsrPlpGsmRefTimeSet <= USED 0
plMsrSendAPLPApiEvent <= USED 0
plMsrSendDefaultMeasParams <= USED 0
plMsrSendMultiBcchReportToRrc <= USED 0
plMsrStopSkipPreventor <= USED 0
plMsrWbMeasAtGsmAbortReq <= USED 0
plMsr_AM_SearchEngInit <= USED 0
plRFDCAPTParasOptFile_ReadNVMFile <= USED 0
plRFDGetBandByUarfcn <= USED 0
plRFDGetPDDebugInfoCnf <= USED 0
plRFDGetSupportedRfBands <= USED 0
plRFDIsDlUarfcnValid <= USED 0
plRFDIsMaxPowerActive <= USED 0
plRFDMaxPowerInitApi <= USED 0
plRFDOpen <= USED 0
plRfDivTestDivAnt <= USED 0
plRfDivTestInit <= USED 0
plRfDivTestPriAntOnly <= USED 0
plRfDivTestSecAntOnly <= USED 0
plSchdSendGsmCopyCodeCmd <= USED 0
plTCCDefaultBindFunction <= USED 0
plTccSetStopShift <= USED 0
plTccVirtualMaceIntHisr <= USED 0
pldGsmTerminate <= USED 0
pldGsmTerminate2 <= USED 0
pldInit <= USED 0
pldRDASetGsm <= USED 0
pldSetGsm <= USED 0
pldSetWcdma <= USED 0
pldWcdmaTerminate <= USED 0
plgRFDStartTempUpdateOnce <= USED 0
plgSetRfResetReq <= USED 0
plwBindCphyDetectedCellMeasInd <= USED 0
plwBindCphyFreqScanInd <= USED 0
plwBindCphyIntraFreqCellMeasurementInd <= USED 0
plwBindCphyMeasuredIntraFreqCellsInd <= USED 0
plwBindCphyOutOfSyncInd <= USED 0
plwBindCphyPsOutOfSyncInd <= USED 0
plwBindCphyPsSyncInd <= USED 0
plwBindCphyRlReleaseCnf <= USED 0
plwBindCphyRlSetupCnf <= USED 0
plwBindCphySyncInd <= USED 0
plwBindPhyDataInd <= USED 0
plwBindPhyDownlinkDataTransferEnd <= USED 0
plwBindPhyHsDataInd <= USED 0
plwBindPhyUplinkDataSync <= USED 0
plwBindPhyUplinkDataTransferEnd <= USED 0
plwCphyDeactivateReq_internal <= USED 0
plwCphyDetectedCellMeasReq <= USED 0
plwCphyDpchSetupReq <= USED 0
plwCphyFreqScanReq <= USED 0
plwCphyIntraFreqCellMeasReq <= USED 0
plwCphyPccpchSetupReq <= USED 0
plwCphyRlReleaseReq <= USED 0
plwCphySetCompressedModeParams <= USED 0
plwDpchDlEstablished <= USED 0
plwLoopBackMode2 <= USED 0
plwNotifyNextTtiTfc <= USED 0
plwPhyDataReq <= USED 0
plwPhyHsPointerAssignReq <= USED 0
resetBchFromOtherRatFlag <= USED 0
schdAbortIntrabchWhenResumeGsmorWb <= USED 0
setBcchDecodeState <= USED 0
;FILE arc4random.o
arc4random <= USED 0
;FILE arm946e_sCP15.o
Arm946eCP15DrainWriteBuffer <= USED 0
Arm946eCP15FlushDataCache <= USED 0
Arm946eCP15FlushInstCache <= USED 0
Arm946eCP15GetAlternateVectorSelect <= USED 0
Arm946eCP15GetBigEndian <= USED 0
Arm946eCP15GetCacheTypeReg <= USED 0
Arm946eCP15GetDataCacheEnable <= USED 0
Arm946eCP15GetDataCacheSize <= USED 0
Arm946eCP15GetDataTCMEnable <= USED 0
Arm946eCP15GetDataTCMLoadMode <= USED 0
Arm946eCP15GetDataTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetDataTCMSize <= USED 0
Arm946eCP15GetInstCacheEnable <= USED 0
Arm946eCP15GetInstTCMEnable <= USED 0
Arm946eCP15GetInstTCMLoadMode <= USED 0
Arm946eCP15GetInstTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetInstTCMSize <= USED 0
Arm946eCP15GetInstructionCacheSize <= USED 0
Arm946eCP15GetRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetTCMSizeReg <= USED 0
Arm946eCP15Read <= USED 0
Arm946eCP15RestoreDataTCMEnable <= USED 0
Arm946eCP15RestoreInstTCMEnable <= USED 0
Arm946eCP15SetAlternateVectorSelect <= USED 0
Arm946eCP15SetBigEndian <= USED 0
Arm946eCP15SetControlReg <= USED 0
Arm946eCP15SetDataCacheDisable <= USED 0
Arm946eCP15SetDataTCMDisable <= USED 0
Arm946eCP15SetDataTCMLoadMode <= USED 0
Arm946eCP15SetInstCacheDisable <= USED 0
Arm946eCP15SetInstTCMDisable <= USED 0
Arm946eCP15SetInstTCMLoadMode <= USED 0
Arm946eCP15SetLowPowerState <= USED 0
Arm946eCP15Write <= USED 0
;FILE arm946e_sCP15_memRetain.o
Arm946eCP15GetControlReg_DS <= USED 0
Arm946eCP15GetMPUEnalbe_DS <= USED 0
Arm946eCP15RestoreMPUEnableDisable_DS <= USED 0
Arm946eCP15SetDataCacheEnable_DS <= USED 0
Arm946eCP15SetDataTCMEnable_DS <= USED 0
Arm946eCP15SetDataTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetInstCacheEnable_DS <= USED 0
Arm946eCP15SetInstTCMEnable_DS <= USED 0
Arm946eCP15SetInstTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetMPUDisable_DS <= USED 0
Arm946eCP15SetMPUEnalbe_DS <= USED 0
Arm946eCP15SetRegionAccessPermission_DS <= USED 0
Arm946eCP15SetRegionBaseAndSizeReg_DS <= USED 0
;FILE ascc_fnc.o
;FILE ascs_fnc.o
ascsGetFirstNwStateInd <= USED 0
;FILE asgki.o
;FILE asgl_cfg.o
afstDtmfTime <= USED 0
simatGetDefaultLangSetting <= USED 0
;FILE asgl_fnc.o
simatGlConvBcdToText <= USED 0
simatGlConvTextToBcd <= USED 0
simatGlGetBcd <= USED 0
simatGlPutBcd <= USED 0
;FILE asmn_fnc.o
simatDontDestroySignal <= USED 0
simatTask1 <= USED 0
simatTask21 <= USED 0
;FILE asmn_proc2.o
;FILE asms_fnc.o
;FILE asn1parse.o
mbedtls_asn1_find_named_data <= USED 0
mbedtls_asn1_free_named_data <= USED 0
mbedtls_asn1_free_named_data_list <= USED 0
mbedtls_asn1_get_alg_null <= USED 0
;FILE asn1write.o
mbedtls_asn1_store_named_data <= USED 0
mbedtls_asn1_write_algorithm_identifier <= USED 0
mbedtls_asn1_write_bitstring <= USED 0
mbedtls_asn1_write_bool <= USED 0
mbedtls_asn1_write_ia5_string <= USED 0
mbedtls_asn1_write_int <= USED 0
mbedtls_asn1_write_named_bitstring <= USED 0
mbedtls_asn1_write_null <= USED 0
mbedtls_asn1_write_octet_string <= USED 0
mbedtls_asn1_write_oid <= USED 0
mbedtls_asn1_write_printable_string <= USED 0
mbedtls_asn1_write_raw_buffer <= USED 0
mbedtls_asn1_write_tagged_string <= USED 0
mbedtls_asn1_write_utf8_string <= USED 0
;FILE aspl_fnc.o
;FILE aspp_fnc.o
;FILE asr_property.o
asr_property_area_init <= USED 0
asr_property_dump <= USED 0
asr_property_get <= USED 0
asr_property_get_buffer <= USED 0
asr_property_set <= USED 0
;FILE asrd_fnc.o
simatRefreshInit <= USED 0
;FILE asros_mbedtls_platform.o
mbedtls_timing_get_delay <= USED 0
mbedtls_timing_set_delay <= USED 0
;FILE astm_fnc.o
;FILE atParser.o
ATReParse <= USED 0
utlAtParserInitCommandParameters <= USED 0
utlCloseAtParser <= USED 0
utlGetDCDmode <= USED 0
utlGetDTRmode <= USED 0
utlSetEcho <= USED 0
;FILE at_common.o
;FILE at_iointf_requestor.o
;FILE at_parser.o
;FILE at_prop.o
ATPROP_GetCMGF <= USED 0
ATPROP_GetCPBS <= USED 0
ATPROP_GetNewSMSIndicationSettings <= USED 0
ATPROP_GetPreferredMemoryForStorage <= USED 0
ATPROP_GetS0 <= USED 0
ATPROP_GetSimMemoryStatus <= USED 0
ATPROP_GetVTD <= USED 0
ATPROP_GetZDON <= USED 0
ATPROP_Hdl_Get_SMS_Capability <= USED 0
ATPROP_Hdl_Get_Sim_Memory_Status <= USED 0
ATPROP_SetCMGF <= USED 0
ATPROP_SetCMRSS <= USED 0
ATPROP_SetCPBR <= USED 0
ATPROP_SetCPBS <= USED 0
ATPROP_SetProxyATReqBackByTransparentTransmission <= USED 0
;FILE at_provider.o
AT_SendTestResultCode <= USED 0
at_provider_get_proxy_req_group_name <= USED 0
at_provider_update_crnt_proxy_req <= USED 0
;FILE at_proxy.o
AT_Proxy_SetIO <= USED 0
;FILE at_proxy_provider.o
at_Proxy_SetInitOwner <= USED 0
;FILE at_requestor.o
AT_CancelRequest <= USED 0
AT_CancelSyncRequest <= USED 0
;FILE atcmd_hdlr.o
;FILE atcmd_list.o
;FILE audio_bind.o
AcmGetPCMRate <= USED 0
OEMTest_AMRRxHandler <= USED 0
OEMTest_AMRTxHandler <= USED 0
OEMTest_DTMFDetectionHandler <= USED 0
OEMTest_LoopbackRxHandler <= USED 0
OEMTest_LoopbackRxHandler_2 <= USED 0
OEMTest_LoopbackTxHandler <= USED 0
POCVoice_MuteTTSPlay <= USED 0
POCVoice_StartTTSPlay <= USED 0
POCVoice_StopTTSPlay <= USED 0
Test_GetVoiceDataCallback <= USED 0
audioBindDoDataSwapBytes <= USED 0
audioBindGetRAT <= USED 0
audioBindGsmGetDtxSupport <= USED 0
audioBindGsmGetTxBuffer <= USED 0
audioBindGsmGetVocoderTypeRate <= USED 0
audioBindGsmSetAmrSidTypeInd <= USED 0
audioBindGsmSetAmrToc <= USED 0
audioBindGsmSetDecoderSidInd <= USED 0
audioBindGsmSetEncoderFlags <= USED 0
audioBindGsmStartVoicePath <= USED 0
audioBindGsmStopVoicePath <= USED 0
;FILE audio_file.o
;FILE audio_pcm.o
tg_pcm_close <= USED 0
tg_pcm_open <= USED 0
tg_pcm_read <= USED 0
tg_pcm_set_state_listener <= USED 0
tg_pcm_start <= USED 0
tg_pcm_stop <= USED 0
tg_pcm_write <= USED 0
tg_volume_set <= USED 0
;FILE audio_resample.o
;FILE audio_server_api_ttc.o
mux27010_dlc_open <= USED 0
mux27010_read_ack <= USED 0
mux27010_write <= USED 0
;FILE audio_server_task_ttc.o
audioDownLink20msCB <= USED 0
;FILE audio_voice.o
;FILE autocorr.o
;FILE automode.o
WebRtcNetEQ_BufferLevelFilter <= USED 0
;FILE az_lsp.o
_Z14Chebps_WrappersPssPi <= USED 0
;FILE b_cn_cod.o
;FILE backend_flash.o
backend_free_env <= USED 0
;FILE base64.o
;FILE bgnscd.o
;FILE bignum.o
mbedtls_mpi_div_int <= USED 0
mbedtls_mpi_mod_int <= USED 0
mbedtls_mpi_read_binary_le <= USED 0
mbedtls_mpi_read_string <= USED 0
mbedtls_mpi_safe_cond_swap <= USED 0
mbedtls_mpi_set_bit <= USED 0
mbedtls_mpi_swap <= USED 0
mbedtls_mpi_write_string <= USED 0
;FILE bip.o
Bip_main <= USED 0
Bip_task_resp_cb <= USED 0
PollPdps <= USED 0
STK_CloseClientSocket <= USED 0
STK_CloseTcpServer <= USED 0
STK_EnvelopeFormat_DataAvail <= USED 0
STK_ReceiveData <= USED 0
STK_SendDataTcpClient <= USED 0
STK_SendDataTcpServer <= USED 0
STK_SendDataUdpClient <= USED 0
STK_SendTRFormat_GetChannelStatus <= USED 0
STK_SendTRFormat_ReceiveData <= USED 0
STK_SendTRFormat_SendData <= USED 0
STK_getChannelidByStatus <= USED 0
STK_getNumbersOfCid <= USED 0
STK_isEventInList <= USED 0
STK_isTargetPDPActive <= USED 0
STK_sendBIPData <= USED 0
processInd <= USED 0
processOth <= USED 0
;FILE bipIpSockets.o
MTIL_CloseSocket <= USED 0
MTIL_Listen <= USED 0
MTIL_SelectReadMult <= USED 0
MTIL_SendSock <= USED 0
;FILE bits2prm.o
;FILE bspUartManager.o
bspUartApplUseAltFunc <= USED 0
bspUartGetIoUart <= USED 0
bspUartNumOfUartsGet <= USED 0
bspUartPhase1Init <= USED 0
bspUartPhase2Init <= USED 0
bspUartPlatUseAltFunc <= USED 0
;FILE bsp_hisr.o
OSATaskGetCurrentRefExt <= USED 0
bspParkThread <= USED 0
;FILE bsp_tavor.o
DelayBeforeCPOff <= USED 0
EnableDataPktInfo <= USED 0
EnableDataPktInfoCmd <= USED 0
EnableDebugLog <= USED 0
GetUmtsChipVersion <= USED 0
InitGPIOWakeupForUSB <= USED 0
IsChipCraneG_A1 <= USED 0
IsChip_CraneG_A0_or_Above <= USED 0
IsChip_CraneM_A0_or_Above <= USED 0
PlatformGetConstData <= USED 0
ReEnableUSIMGPIODetection <= USED 0
SetspecialPShandle <= USED 0
USIM_GPIO_WK <= USED 0
bootup_gpio_ind <= USED 0
bootup_mode_cfun4 <= USED 0
bspBoardIsDKB <= USED 0
bspGetAcLinkName <= USED 0
bspGetIpcInts <= USED 0
bspGetPlpFrameInt <= USED 0
bspI2CToNAInit <= USED 0
bspI2cInit <= USED 0
bspInit <= USED 0
bspInitRfInterface <= USED 0
bspIpcInit <= USED 0
eeh_gpio_ind <= USED 0
hsi_gpio_init <= USED 0
hsic_emu_wait_ap_gpio <= USED 0
hsic_gpio_init <= USED 0
hsic_ready_gpio_ind <= USED 0
hsic_ready_gpio_set_after_enum <= USED 0
is_2chip_platform_func <= USED 0
is_dev_chnl_ready <= USED 0
is_pmic801_used_func <= USED 0
m_atoi <= USED 0
platform_type_init <= USED 0
set_cp2ap_pm_gpio_high <= USED 0
set_cp2ap_pm_gpio_low <= USED 0
set_ps_dev_chnl_ready <= USED 0
usim2_enable <= USED 0
usim_gpio_init <= USED 0
;FILE bufstats_decision.o
WebRtcNetEQ_BufstatsDecision <= USED 0
;FILE c1035pf.o
_Z3q_pPss <= USED 0
;FILE c2_11pf.o
;FILE c2_9pf.o
Test_build_code <= USED 0
Test_search_2i40 <= USED 0
;FILE c3_14pf.o
;FILE c4_17pf.o
;FILE c8_31pf.o
;FILE cJSON.o
cJSON_AddArrayToObject <= USED 0
cJSON_AddBoolToObject <= USED 0
cJSON_AddFalseToObject <= USED 0
cJSON_AddItemReferenceToArray <= USED 0
cJSON_AddItemReferenceToObject <= USED 0
cJSON_AddItemToArray <= USED 0
cJSON_AddItemToObject <= USED 0
cJSON_AddItemToObjectCS <= USED 0
cJSON_AddNullToObject <= USED 0
cJSON_AddNumberToObject <= USED 0
cJSON_AddObjectToObject <= USED 0
cJSON_AddRawToObject <= USED 0
cJSON_AddStringToObject <= USED 0
cJSON_AddTrueToObject <= USED 0
cJSON_Compare <= USED 0
cJSON_CreateArray <= USED 0
cJSON_CreateArrayReference <= USED 0
cJSON_CreateBool <= USED 0
cJSON_CreateDoubleArray <= USED 0
cJSON_CreateFalse <= USED 0
cJSON_CreateFloatArray <= USED 0
cJSON_CreateIntArray <= USED 0
cJSON_CreateNull <= USED 0
cJSON_CreateNumber <= USED 0
cJSON_CreateObject <= USED 0
cJSON_CreateObjectReference <= USED 0
cJSON_CreateRaw <= USED 0
cJSON_CreateString <= USED 0
cJSON_CreateStringArray <= USED 0
cJSON_CreateStringReference <= USED 0
cJSON_CreateTrue <= USED 0
cJSON_DeleteItemFromArray <= USED 0
cJSON_DeleteItemFromObject <= USED 0
cJSON_DeleteItemFromObjectCaseSensitive <= USED 0
cJSON_DetachItemFromArray <= USED 0
cJSON_DetachItemFromObject <= USED 0
cJSON_DetachItemFromObjectCaseSensitive <= USED 0
cJSON_DetachItemViaPointer <= USED 0
cJSON_Duplicate <= USED 0
cJSON_GetArrayItem <= USED 0
cJSON_GetArraySize <= USED 0
cJSON_GetErrorPtr <= USED 0
cJSON_GetNumberValue <= USED 0
cJSON_GetObjectItemCaseSensitive <= USED 0
cJSON_GetStringValue <= USED 0
cJSON_HasObjectItem <= USED 0
cJSON_InitHooks <= USED 0
cJSON_InsertItemInArray <= USED 0
cJSON_IsArray <= USED 0
cJSON_IsBool <= USED 0
cJSON_IsFalse <= USED 0
cJSON_IsInvalid <= USED 0
cJSON_IsNull <= USED 0
cJSON_IsNumber <= USED 0
cJSON_IsObject <= USED 0
cJSON_IsRaw <= USED 0
cJSON_IsString <= USED 0
cJSON_Minify <= USED 0
cJSON_ParseWithLength <= USED 0
cJSON_Print <= USED 0
cJSON_PrintBuffered <= USED 0
cJSON_PrintPreallocated <= USED 0
cJSON_PrintUnformatted <= USED 0
cJSON_ReplaceItemInArray <= USED 0
cJSON_ReplaceItemInObject <= USED 0
cJSON_ReplaceItemInObjectCaseSensitive <= USED 0
cJSON_ReplaceItemViaPointer <= USED 0
cJSON_SetNumberHelper <= USED 0
cJSON_SetValuestring <= USED 0
cJSON_Version <= USED 0
cJSON_free <= USED 0
cJSON_malloc <= USED 0
;FILE c_g_aver.o
;FILE c_ip.o
;FILE c_udp.o
;FILE calc_cor.o
;FILE calc_en.o
;FILE cam_fe.o
isp_ctl_set_banding <= USED 0
isp_ctl_set_brightness <= USED 0
isp_ctl_set_contrast <= USED 0
isp_ctl_set_effect <= USED 0
isp_ctl_set_saturation <= USED 0
isp_ctl_set_wb <= USED 0
;FILE cam_hal_drv_interface.o
camdrv_buf_flush <= USED 0
camdrv_pipe_streamoff_online_capture <= USED 0
camdrv_pipe_streamon_capture_off_preview <= USED 0
camdrv_pipe_streamon_online_capture <= USED 0
camdrv_set_ata <= USED 0
camdrv_set_param <= USED 0
camdrv_set_zoom <= USED 0
;FILE cam_i2c_api.o
TWSI_REG_READ_CAM <= USED 0
TWSI_REG_WRITE_CAM <= USED 0
;FILE cam_offline.o
cam_offline_deinit <= USED 0
cam_offline_homo_zoom <= USED 0
cam_offline_init <= USED 0
cam_offline_rotation <= USED 0
cam_offline_scaler_zoom <= USED 0
common_offline_rotation <= USED 0
;FILE cam_pipeline.o
cam_buf_flush <= USED 0
cam_irq_handler <= USED 0
cam_irq_hisr_handler <= USED 0
cam_irq_msg_thread <= USED 0
cam_pipe_prepare_online_capture_pipe <= USED 0
cam_pipe_prepare_online_preview_pipe <= USED 0
cam_pipe_setzoom <= USED 0
cam_pipe_streamoff_online_capture <= USED 0
cam_pipe_streamon_capture_off_preview <= USED 0
cam_pipe_streamon_online_capture <= USED 0
isp_dma_status_check <= USED 0
isp_get_buffer_count <= USED 0
isp_get_idle_buffer <= USED 0
isp_get_idle_buffer_by_index <= USED 0
isp_put_buffer_to_bufque <= USED 0
isp_put_idle_buffer <= USED 0
isp_streamon_fill_buf <= USED 0
isp_update_aec <= USED 0
;FILE cam_pipeline_reg.o
camera_reg_read <= USED 0
get_pipeline_cur_zoomratio <= USED 0
get_pipeline_target_zoomratio <= USED 0
isp_reg_read <= USED 0
pmu_reg_write <= USED 0
;FILE cam_sensor.o
;FILE cam_tuning.o
;FILE cam_util.o
mdelay <= USED 0
msleep <= USED 0
ui_delay_us <= USED 0
uudelay <= USED 0
;FILE camellia.o
;FILE camera_hal_core.o
CCATATakePicture <= USED 0
CCCamFullSizeStreamThread <= USED 0
CCDoJpegEncode <= USED 0
CCDumpNV12ToFile <= USED 0
CCDumpRawToFile <= USED 0
CCFreeBufferNotify <= USED 0
CCGetIspSize <= USED 0
CCPauseFullSizeStream <= USED 0
CCReleaseBufferToCamera <= USED 0
CCResumeFullSizeStream <= USED 0
CCSaveJpegToFile <= USED 0
CCSavePicture <= USED 0
CCSendMsgToThread <= USED 0
CCSetPara <= USED 0
_CCAddExifFlag <= USED 0
_CCAddExifIFDPointer <= USED 0
_CCAddExifULong <= USED 0
_CCAddExifURational <= USED 0
_CCAddExifUShort <= USED 0
_CCAddIFDASCII <= USED 0
_CCCaptureAddExif <= USED 0
_CCCaptureBackDisplay <= USED 0
_CCCaptureBottomHalfProcess <= USED 0
_CCCheckBuffersBelongToUs <= USED 0
_CCDumpBufferStatus <= USED 0
_CCExifOffsetOfRational <= USED 0
_CCIFDOffsetOfASCII <= USED 0
_CCReadyToDisplay <= USED 0
_CCReadyToSendData <= USED 0
_CCSetSoftjpegOrHardware <= USED 0
_CCSumExifSize <= USED 0
_CCTryEnqueueIdleBuffers <= USED 0
ataCloseCamera <= USED 0
ataOpenCamera <= USED 0
ataTakePicture <= USED 0
isp_get_rawdump_flag <= USED 0
isp_set_rawdump_flag <= USED 0
;FILE camera_hal_drv_ops.o
_fillIspOfflineBuffer <= USED 0
appendBufferStatus <= USED 0
clearBufferStatus <= USED 0
doHardwareJpeg <= USED 0
flushBuffer <= USED 0
getRotation <= USED 0
getZoomScale <= USED 0
queueBufIndexAlloc <= USED 0
queueBufIndexFree <= USED 0
setCamDrvParamter <= USED 0
setCameraATA <= USED 0
streamOffOnlineCapture <= USED 0
streamOnCaptureOffPreview <= USED 0
streamOnOnlineCapture <= USED 0
;FILE camera_jpeg_encoder.o
CameraCodaJpegEncode <= USED 0
CameraOnlineJpegEncode <= USED 0
;FILE cbsearch.o
;FILE cc_api.o
CC_SyncAudio <= USED 0
PARSER_START_RINGING <= USED 0
resetAllCalls <= USED 0
;FILE cc_api_mini.o
;FILE ccm.o
;FILE cellular.o
;FILE cellular_cs.o
;FILE cellular_ps.o
;FILE cgpio.o
GpioCommonWakeupCallback <= USED 0
GpioPhase1Init <= USED 0
;FILE chacha20.o
mbedtls_chacha20_crypt <= USED 0
;FILE chachapoly.o
;FILE chap.o
;FILE ci_api.o
;FILE ci_client_task_ttc.o
;FILE ci_client_ttc.o
Bcd2String <= USED 0
ReleaseCiClientMem <= USED 0
ciRequest_Transfer_client <= USED 0
ciRespond_1_client <= USED 0
ciShDeregisterReq_1_client <= USED 0
ciShRegisterReq_1_client <= USED 0
ciShRequest_1_client <= USED 0
clearCciActionlist <= USED 0
clientCiDefConfirmCallback_client <= USED 0
clientCiSgFreeReqMem <= USED 0
clientCiShConfirmCallback_client <= USED 0
findCiSgId <= USED 0
string2Number <= USED 0
;FILE ci_mem.o
ciAdjustSacCiCnf <= USED 0
ciAdjustSacCiInd <= USED 0
ciAdjustSacCiReq <= USED 0
ciNegotiateSgCiVersion <= USED 0
ciSgCiVersionInit <= USED 0
cimem_CiSgCnfIndAllocMem <= USED 0
cimem_CiSgFreeMem <= USED 0
cimem_CiShAllocCnfMem <= USED 0
cimem_CiShAllocReqMem <= USED 0
cimem_CiShFreeCnfMem <= USED 0
cimem_GetCiNumericListDataSize <= USED 0
cimem_GetCiShCnfDataSize <= USED 0
cimem_GetCiShReqDataSize <= USED 0
;FILE ci_mm_mem.o
CiGetArfcnFromNvm <= USED 0
CiGetModemInfo <= USED 0
CiInsertArfcnNvm <= USED 0
CiUpdateAllArfcnNvm <= USED 0
;FILE ci_msl.o
GPC_TransmitRequest <= USED 0
GPC_dataReceiveReady <= USED 0
MslAlignFree <= USED 0
MslUtilsMemDeInit <= USED 0
MslUtilsMemInit <= USED 0
;FILE ci_server_task_ttc.o
ACIPCDImsRxdefault <= USED 0
ACIPCDImsTxdefault <= USED 0
getATRdyPhase2 <= USED 0
;FILE cid.o
;FILE cimodem.o
DataServiceResumed <= USED 0
Deactive_PDP_Context_by_Itself <= USED 0
LinkStatusIndCB <= USED 0
SendATCmdChars <= USED 0
ci_modem_PS_data_tx <= USED 0
ci_modem_rx <= USED 0
ci_modem_set_channel_data_stop_flag <= USED 0
mprintf <= USED 0
;FILE cimodem_usb.o
atmodemDataRxInd <= USED 0
atmodemDataRxInd_modem2 <= USED 0
usb_tx <= USED 0
;FILE cinit1.o
Arm946eDisableInterrupts <= USED 0
Arm946eEnableInterrupts <= USED 0
Arm946eMemcpy <= USED 0
Arm946eRestoreInterrupts <= USED 0
Cinit1 <= USED 0
Configure_RTC_WTC <= USED 0
DisableMFPRGSSPFunction <= USED 0
DynamicMemoryInit <= USED 0
EnableMFPRGSSPFunction <= USED 0
GetSiliconFoundry <= USED 0
IS_TDNewChip <= USED 0
IsCinit1Passed <= USED 0
IsPhase2Passed <= USED 0
MFPR_Config <= USED 0
TDChipIS_A0 <= USED 0
TDChipIS_A1 <= USED 0
TDChipIS_Y2 <= USED 0
WA_40LP_PLL1_stablize <= USED 0
copyRWtoImage <= USED 0
;FILE cinit2.o
Cinit2 <= USED 0
;FILE cipher.o
mbedtls_cipher_check_tag <= USED 0
mbedtls_cipher_info_from_string <= USED 0
mbedtls_cipher_list <= USED 0
mbedtls_cipher_update_ad <= USED 0
mbedtls_cipher_write_tag <= USED 0
;FILE cl_ltp.o
;FILE cm-er-coap-13.o
cm_coap_get_header_accept <= USED 0
cm_coap_get_header_content_type <= USED 0
cm_coap_get_header_etag <= USED 0
cm_coap_get_header_if_match <= USED 0
cm_coap_get_header_if_none_match <= USED 0
cm_coap_get_header_location_path <= USED 0
cm_coap_get_header_location_query <= USED 0
cm_coap_get_header_max_age <= USED 0
cm_coap_get_header_observe <= USED 0
cm_coap_get_header_proxy_uri <= USED 0
cm_coap_get_header_size <= USED 0
cm_coap_get_header_token <= USED 0
cm_coap_get_header_uri_host <= USED 0
cm_coap_get_header_uri_path <= USED 0
cm_coap_get_header_uri_query <= USED 0
cm_coap_get_mid <= USED 0
cm_coap_get_multi_option_as_string <= USED 0
cm_coap_get_payload <= USED 0
cm_coap_get_post_variable <= USED 0
cm_coap_get_query_variable <= USED 0
cm_coap_set_header_accept <= USED 0
cm_coap_set_header_etag <= USED 0
cm_coap_set_header_if_match <= USED 0
cm_coap_set_header_if_none_match <= USED 0
cm_coap_set_header_location_path <= USED 0
cm_coap_set_header_location_query <= USED 0
cm_coap_set_header_max_age <= USED 0
cm_coap_set_header_proxy_uri <= USED 0
cm_coap_set_header_size <= USED 0
cm_coap_set_header_uri_host <= USED 0
cm_coap_set_header_uri_path_segment <= USED 0
;FILE cm_adc.o
;FILE cm_api_ntp.o
;FILE cm_api_ssl.o
;FILE cm_asocket.o
;FILE cm_async_dns.o
;FILE cm_at_adc.o
cm_get_adc <= USED 0
;FILE cm_at_gpio.o
cm_at_gpio_get_direction <= USED 0
cm_at_gpio_set_direction <= USED 0
cm_gpio_check_pin <= USED 0
cm_gpio_get_pull_state <= USED 0
cm_gpio_irq_config <= USED 0
cm_gpio_set_pull_state <= USED 0
;FILE cm_atcmd_asr.o
cmMCELLS <= USED 0
cmMFORCEDL <= USED 0
cmMPRODUCTMODE <= USED 0
;FILE cm_atcmd_cmdmp.o
;FILE cm_atcmd_cot.o
;FILE cm_atcmd_extern.o
cmMADC <= USED 0
cmMBAND <= USED 0
cmMGPIO <= USED 0
cmMNTP <= USED 0
;FILE cm_atcmd_fota.o
;FILE cm_atcmd_fs.o
cmMFCFG <= USED 0
cmMFCHECK <= USED 0
cmMFCLOSE <= USED 0
cmMFDELETE <= USED 0
cmMFGET <= USED 0
cmMFMOVE <= USED 0
cmMFOPEN <= USED 0
cmMFPUT <= USED 0
cmMFREAD <= USED 0
cmMFSEEK <= USED 0
cmMFSINFO <= USED 0
cmMFSIZE <= USED 0
cmMFSYNC <= USED 0
cmMFTRUNC <= USED 0
cmMFWRITE <= USED 0
cmMTEST <= USED 0
cm_get_file_md5 <= USED 0
;FILE cm_atcmd_ping.o
;FILE cm_atp.o
;FILE cm_audio_player.o
;FILE cm_audio_recorder.o
cm_audio_loopback_start <= USED 0
cm_audio_loopback_stop <= USED 0
;FILE cm_camera.o
;FILE cm_camera_sensor.o
;FILE cm_common_net.o
cm_get_band_list <= USED 0
cm_get_pdp_priority <= USED 0
cm_set_band_limit <= USED 0
cm_set_pdp_priority <= USED 0
;FILE cm_common_sys.o
cm_get_escstring <= USED 0
cm_parse_char_to_hex <= USED 0
cm_printf_delay <= USED 0
cm_system_poweroff <= USED 0
;FILE cm_cpuinfo.o
;FILE cm_daemon.o
__cm_light_init <= USED 0
cm_get_nells_ind <= USED 0
;FILE cm_datamode.o
__cmiot_siolib_port_process_timeout_msg <= USED 0
__cmiot_siolib_port_process_wait_msg <= USED 0
cm_datamode_deinit <= USED 0
cm_datamode_get <= USED 0
cm_datamode_init <= USED 0
enter_data_mode <= USED 0
exit_data_mode <= USED 0
;FILE cm_dmp.o
cm_mobile_dm_get_option <= USED 0
cm_mobile_dm_get_state <= USED 0
cm_mobile_dm_stop <= USED 0
;FILE cm_dmp_for_at.o
;FILE cm_eloop.o
cm_eloop_init <= USED 0
;FILE cm_export_func.o
;FILE cm_extern_api.o
cm_extern_dec2hex <= USED 0
cm_extern_hex2dec <= USED 0
cm_extern_str_upr <= USED 0
;FILE cm_flight_mode.o
cm_flight_mode_get <= USED 0
cm_flight_mode_set <= USED 0
;FILE cm_fota.o
;FILE cm_fs.o
;FILE cm_gpio.o
;FILE cm_http_client.o
;FILE cm_http_platform.o
_CM_HTTPDebugLevelName <= USED 0
__cm_http_socket_work_tsk_create <= USED 0
http_file_close <= USED 0
http_file_get_size <= USED 0
http_file_open <= USED 0
http_file_write <= USED 0
http_mutex_create <= USED 0
http_mutex_delete <= USED 0
http_mutex_release <= USED 0
http_mutex_take <= USED 0
http_socket_gethostbyname <= USED 0
http_ssl_get_receive_available <= USED 0
http_uart_claim <= USED 0
http_uart_printf <= USED 0
http_uart_printf_data <= USED 0
http_uart_printf_urc <= USED 0
http_uart_release <= USED 0
print_string <= USED 0
;FILE cm_i2c.o
;FILE cm_imei_cert.o
cm_cert_imei_write <= USED 0
;FILE cm_iomux.o
;FILE cm_keypad.o
;FILE cm_lbs.o
cm_lbs_write_nv_data <= USED 0
;FILE cm_lbs_amap.o
;FILE cm_lbs_oneospos.o
;FILE cm_lbs_platform.o
__cm_lbs_DeleteTask <= USED 0
__cm_lbs_FileRead <= USED 0
__cm_lbs_FileWrite <= USED 0
__cm_lbs_GetNetwork <= USED 0
__cm_lbs_GetServerip <= USED 0
__cm_lbs_MutexAcquire <= USED 0
__cm_lbs_MutexCreate <= USED 0
__cm_lbs_MutexDelete <= USED 0
__cm_lbs_MutexRelease <= USED 0
__cm_lbs_QueueDelete <= USED 0
__cm_lbs_TimeCreat <= USED 0
__cm_lbs_TimeDelete <= USED 0
__cm_lbs_TimeStar <= USED 0
__cm_lbs_TimeState <= USED 0
__cm_lbs_TimeStop <= USED 0
cm_lbs_asr_set_athandle <= USED 0
cm_lbs_delay <= USED 0
cm_lbs_urc_rsp <= USED 0
cm_lbs_writenv_config <= USED 0
;FILE cm_lcd.o
cm_lcd_refash <= USED 0
cm_lcd_set_backlight <= USED 0
cm_lcd_test_dma_write <= USED 0
cm_lcd_test_reg_init <= USED 0
cm_lcd_usb_log <= USED 0
spi_dma_uninit <= USED 0
;FILE cm_linklist.o
linklist_insert_after_index <= USED 0
linklist_pop_tail <= USED 0
linklist_pre_element <= USED 0
linklist_remove_head <= USED 0
linklist_remove_tail <= USED 0
;FILE cm_mem.o
;FILE cm_modem_info.o
;FILE cm_mqtt_client.o
;FILE cm_mqtt_manager.o
cm_mqtt_manager_connect <= USED 0
cm_mqtt_manager_disconnect <= USED 0
cm_mqtt_manager_get_cache_data <= USED 0
cm_mqtt_manager_get_cache_info <= USED 0
cm_mqtt_manager_get_mid <= USED 0
cm_mqtt_manager_get_opt <= USED 0
cm_mqtt_manager_get_state <= USED 0
cm_mqtt_manager_get_sub_topics <= USED 0
cm_mqtt_manager_init <= USED 0
cm_mqtt_manager_publish <= USED 0
cm_mqtt_manager_set_opt <= USED 0
cm_mqtt_manager_subscribe <= USED 0
cm_mqtt_manager_unsubscribe <= USED 0
;FILE cm_mqtt_platform.o
cm_mqtt_queue_destroy <= USED 0
cm_mqtt_thread_exit <= USED 0
cm_mqtt_timer_destroy <= USED 0
;FILE cm_mqtt_timing.o
cm_mqtt_timing_destroy <= USED 0
;FILE cm_nv_def.o
;FILE cm_onemo.o
cm_onemo_init <= USED 0
;FILE cm_opencpu_start.o
;FILE cm_ping.o
;FILE cm_pinmux.o
mfp_config_io <= USED 0
;FILE cm_pm.o
;FILE cm_pwm.o
;FILE cm_ri.o
;FILE cm_rtc.o
;FILE cm_sd.o
;FILE cm_sim.o
;FILE cm_sleep.o
cm_get_sleep_ind <= USED 0
cm_lowpower_enter <= USED 0
cm_lowpower_exit <= USED 0
cm_sleep_callback <= USED 0
;FILE cm_spi.o
;FILE cm_ssl.o
;FILE cm_ssl_cert.o
cm_cert_read <= USED 0
cm_get_cert_write_is_busy <= USED 0
;FILE cm_sys.o
;FILE cm_tts_client.o
cm_tts_is_init <= USED 0
cm_tts_set_audio_channel <= USED 0
cm_tts_set_audio_volume <= USED 0
cm_tts_set_init_flag <= USED 0
cm_tts_set_state <= USED 0
cm_ttsclient_cfg_find <= USED 0
;FILE cm_usb.o
cm_usb2com_status_indicate <= USED 0
;FILE cm_version.o
cm_get_max_version <= USED 0
cm_get_product_type <= USED 0
;FILE cm_virt_at.o
oc_virt_at_callback <= USED 0
;FILE cm_wifiscan.o
;FILE cmdintf.o
;FILE cmsis_kvlist.o
;FILE cmsis_os2.o
osEventFlagsGetName <= USED 0
osMessageQueueGetName <= USED 0
osMutexGetName <= USED 0
osSemaphoreGetName <= USED 0
;FILE cmux.o
CmuxDlcDataRxInd <= USED 0
CmuxDlcTxCompleteCallback <= USED 0
CmuxLinkStatusIndCB <= USED 0
cmux_channel_block <= USED 0
cmux_channel_get_status <= USED 0
cmux_channel_resume <= USED 0
cmux_default_tx_complete_callback <= USED 0
cmux_dlc_flow_control_off <= USED 0
cmux_dlc_open <= USED 0
cmux_frame_send_init <= USED 0
cmux_get_dlc_status <= USED 0
cmux_get_rx_count <= USED 0
cmux_quit <= USED 0
cmux_rx_task <= USED 0
cmux_send_frame <= USED 0
cmux_transmit_cmd_data_to_app <= USED 0
cmux_tx_task <= USED 0
cmux_wait_dlc_open <= USED 0
composite_timer_callback <= USED 0
get_cmux_mode <= USED 0
mux27010_Dlc_Mode_Switch_indicate <= USED 0
mux_active_dlc <= USED 0
send_disc <= USED 0
send_sabm <= USED 0
;FILE cmux_api.o
DetachCloseSocketReq <= USED 0
HexStrToByte <= USED 0
atNotifyFTPADone <= USED 0
cmuxConfigured <= USED 0
cmuxGetNetAtpIndexs <= USED 0
cmuxGetPdpInfo <= USED 0
cmuxInitChannelConNum <= USED 0
cmuxInitConNum <= USED 0
cmuxIsDataTransMode <= USED 0
cmuxIsSocketBindChannel <= USED 0
cmuxSetCommandMode <= USED 0
cmuxSetUrc <= USED 0
cmuxWaitFTPPutA <= USED 0
fillActStateCnf <= USED 0
get_ipsec_test_enable <= USED 0
get_order_key <= USED 0
init_cmux_channelCtrl <= USED 0
init_e20_config <= USED 0
init_tigx_info <= USED 0
setFtpGenericCnfA <= USED 0
setFtpPutFileDataCnf <= USED 0
setFtpPutFileDataReqA <= USED 0
setNetCloseCnf <= USED 0
setSysResetNoParamReq <= USED 0
set_ipsec_test_enable <= USED 0
set_tigx_info <= USED 0
sysinfourc_ind_reg_status <= USED 0
;FILE cmux_hal.o
cmux_uart_frame_send <= USED 0
cmux_uart_send <= USED 0
cmux_usb_frame_send <= USED 0
cmux_usb_send <= USED 0
;FILE cmux_utilities.o
cmux_dequeue_Qos_queue <= USED 0
cmux_get_dlc_feature <= USED 0
cmux_match_index_service <= USED 0
cmux_select_dlc_by_prio <= USED 0
cmux_tx_fcs_compute <= USED 0
;FILE cng.o
WebRtcNetEQ_CngUpdateState <= USED 0
WebRtcNetEQ_EvsCngUpdateState <= USED 0
;FILE cod_amr.o
cod_amr_first <= USED 0
;FILE codec_db.o
WebRtcNetEQ_DbAdd <= USED 0
WebRtcNetEQ_DbGetCodec <= USED 0
WebRtcNetEQ_DbGetPtrs <= USED 0
WebRtcNetEQ_DbRemove <= USED 0
;FILE comBasicCfg_nvm.o
;FILE commpm.o
CheckIfSoc32kFromRf26M <= USED 0
CommPMCP15D2Prepare <= USED 0
CommPMD4Enter <= USED 0
CommPMFakeSleepEnable <= USED 0
CommPMGetGSMWakeupTime <= USED 0
CommPMGetWBWakeupTime <= USED 0
CommPMHardwareInit <= USED 0
CommPMPowerdownUsbPhy <= USED 0
CommPMPowerupUsbPhy <= USED 0
DEBUG_ASSERT <= USED 0
DumpCPADebugInfo <= USED 0
DumpFlashFlushDebugInfo <= USED 0
DumpTimerThreadDebugInfo <= USED 0
EnableL1Register <= USED 0
ForceEndlessD2 <= USED 0
ForceMSADisableSleep <= USED 0
GetDDRLock <= USED 0
GetPMWakupEvent <= USED 0
SetMSASLPEN <= USED 0
SetTDRFPinFloating <= USED 0
SetTDRFPinPulldown <= USED 0
SetWakeupMaskForTDSCDMA <= USED 0
UnsetWakeupMaskForTDSCDMA <= USED 0
isr_cr5_error_resp <= USED 0
mmiC1CallbackRegister <= USED 0
mmiD2CallbackRegister <= USED 0
mmiStatusCallbackRegister <= USED 0
timer_thread_hook_before <= USED 0
;FILE commpm_debug.o
CommPMTestTask <= USED 0
perf_cnt_begin <= USED 0
perf_cnt_stop <= USED 0
test_sample <= USED 0
;FILE comp_ip_id_offset.o
rohc_comp_detect_ip_id_behavior <= USED 0
;FILE comp_list.o
;FILE comp_list_ipv6.o
;FILE comp_rfc4996.o
c_field_scaling <= USED 0
c_optional_ip_id_lsb <= USED 0
c_static_or_irreg16 <= USED 0
c_static_or_irreg32 <= USED 0
c_static_or_irreg8 <= USED 0
c_zero_or_irreg16 <= USED 0
c_zero_or_irreg32 <= USED 0
dscp_encode <= USED 0
rsf_index_enc <= USED 0
rsf_index_enc_possible <= USED 0
tcp_is_ack_scaled_possible <= USED 0
tcp_is_ack_stride_static <= USED 0
variable_length_32_enc <= USED 0
;FILE comp_scaled_rtp_ts.o
;FILE comp_wlsb.o
wlsb_ack <= USED 0
wlsb_get_k_8bits <= USED 0
wlsb_get_kp_16bits <= USED 0
wlsb_get_kp_8bits <= USED 0
wlsb_is_kp_possible_16bits <= USED 0
wlsb_is_kp_possible_32bits <= USED 0
wlsb_is_kp_possible_8bits <= USED 0
;FILE configReader.o
;FILE connect_management.o
CM_DialerStatus <= USED 0
CM_DiscardMsg <= USED 0
CM_GetDefaultApnName <= USED 0
CM_GetDefaultConnectStatus <= USED 0
CM_GetRoamingStatus <= USED 0
CM_RegisterIpChangedCb <= USED 0
CM_SendAndCompareCgdcontApn <= USED 0
CM_SetConnectionSwitchNoPersist <= USED 0
CM_Set_ConnectType_WEBUI <= USED 0
getLwipNetifDnsIp6 <= USED 0
getRadioPowerFunc <= USED 0
resetDefaultCidReplaceFlag <= USED 0
setLwipNetifDnsIp6 <= USED 0
;FILE convert.o
;FILE convolve.o
;FILE copy.o
;FILE copy_set_operations.o
;FILE cor_h.o
;FILE cor_h_x.o
;FILE cor_h_x2.o
;FILE cot_appdata.o
;FILE cot_block.o
;FILE cot_bootstrap.o
;FILE cot_buffer.o
;FILE cot_cis.o
;FILE cot_device.o
;FILE cot_discover.o
;FILE cot_dtls.o
cot_dtls_timer_msg <= USED 0
;FILE cot_fota.o
cot_block1_buf_send <= USED 0
cot_block1_buf_set <= USED 0
cot_fota_resource_clear <= USED 0
;FILE cot_lwm2m.o
cot_conf <= USED 0
cot_delobj <= USED 0
;FILE cot_manage.o
;FILE cot_message.o
;FILE cot_moniter.o
;FILE cot_observe.o
;FILE cot_platform.o
cot_check_dtls_pending <= USED 0
cot_check_socket_data <= USED 0
cot_gettime <= USED 0
;FILE cot_register.o
;FILE cot_sota.o
;FILE cot_table.o
getNULL <= USED 0
;FILE cot_task.o
cot_fota_downloaded_cb <= USED 0
cot_fota_erase_cb <= USED 0
cot_fota_timer_cb <= USED 0
cot_fota_validate_cb <= USED 0
cot_fota_write_cb <= USED 0
;FILE cot_utils.o
;FILE crane_ds_mpu.o
crane_ds_mpu_ext_flash_enable <= USED 0
;FILE crc.o
compute_crc_ctrl_fields <= USED 0
;FILE crc32.o
;FILE crossPlatformSW.o
FAT_MakeDir_M <= USED 0
FAT_eof <= USED 0
FAT_fmount <= USED 0
FDI_GetFileFlag <= USED 0
FDI_IsFormatted <= USED 0
FDI_eof <= USED 0
crossPlatformSWPhase1Init <= USED 0
crossPlatformSWPhaseAfterAllNvmInit <= USED 0
disable_nvm_flush <= USED 0
enable_nvm_flush <= USED 0
fs_mode_is_ramfs <= USED 0
fs_mode_is_tfs <= USED 0
get_fs_mode <= USED 0
set_fs_mode <= USED 0
;FILE csw_mem.o
PhysicalToVirtualNonCached <= USED 0
VirtualToPhysical <= USED 0
alignFree <= USED 0
dtcmHeapInit <= USED 0
extMemHeapInit <= USED 0
intMemHeapInit <= USED 0
memClean <= USED 0
memPoolX_Init <= USED 0
romAlloc <= USED 0
romFree <= USED 0
;FILE ctr_drbg.o
mbedtls_ctr_drbg_set_entropy_len <= USED 0
mbedtls_ctr_drbg_set_prediction_resistance <= USED 0
mbedtls_ctr_drbg_set_reseed_interval <= USED 0
mbedtls_ctr_drbg_update <= USED 0
mbedtls_ctr_drbg_update_ret <= USED 0
;FILE d1035pf.o
;FILE d2_11pf.o
;FILE d2_9pf.o
;FILE d3_14pf.o
;FILE d4_17pf.o
;FILE d8_31pf.o
;FILE d_gain_c.o
;FILE d_gain_p.o
;FILE d_ip.o
;FILE d_plsf.o
;FILE d_plsf_3.o
;FILE d_plsf_5.o
;FILE d_udp.o
;FILE dcxoControll.o
SetRfDcxoIsUsingFlgWithGPIO <= USED 0
polyfitforcomp <= USED 0
;FILE dec.o
DecodeGSMSPDUDataToSMSText <= USED 0
smsutils_ConvertBCDToAddr <= USED 0
;FILE dec_amr.o
;FILE dec_gain.o
;FILE dec_lag3.o
;FILE dec_lag6.o
;FILE decomp_ip_id_offset.o
;FILE decomp_list.o
;FILE decomp_list_ipv6.o
;FILE decomp_scaled_rtp_ts.o
;FILE decomp_wlsb.o
rohc_lsb_is_ready <= USED 0
;FILE def.o
lwip_itoa <= USED 0
lwip_mac_vlan_pton <= USED 0
lwip_stricmp <= USED 0
lwip_strnstr <= USED 0
;FILE des.o
mbedtls_des_key_check_key_parity <= USED 0
mbedtls_des_key_check_weak <= USED 0
mbedtls_des_key_set_parity <= USED 0
;FILE detect_lock.o
;FILE dev_api.o
DEV_GetEngineModeInfo <= USED 0
DEV_GetLPECID <= USED 0
DEV_GetLPOtdoaReq <= USED 0
DEV_GetMccMncCcReq <= USED 0
DEV_GetRsrpReq <= USED 0
DEV_GetRsrqReq <= USED 0
DEV_GetRssiReq <= USED 0
DEV_GetSerialNumId <= USED 0
DEV_GetSinrReq <= USED 0
DEV_GetWbAmrReq <= USED 0
DEV_SetLPOtdoaAbort <= USED 0
DEV_SetMccMncCcReq <= USED 0
DEV_SetWbAmrReq <= USED 0
DEV_setLPNWUL <= USED 0
GlbGetFuncConf <= USED 0
GlbSetFuncConf <= USED 0
SysSleepProcessSetFunc <= USED 0
sim_libConvertNumToHexString <= USED 0
;FILE dev_api_mini.o
resetDevParas <= USED 0
;FILE dhcp.o
dhcp_cleanup <= USED 0
dhcp_inform <= USED 0
dhcp_set_struct <= USED 0
;FILE dhcp6.o
dhcp6_client_tmr <= USED 0
dhcp6_find_opt_2 <= USED 0
dhcp6_reply_opt_dns <= USED 0
dhcp6_reply_opt_domain <= USED 0
dhcp6_reply_opt_ip6 <= USED 0
dhcp6_reply_opt_ip6_addr <= USED 0
dhcp6_reply_opt_prefer <= USED 0
dhcp6_reply_opt_status <= USED 0
dhcp6_reply_opt_xid <= USED 0
;FILE dhcp6d.o
dhcp6d_router_dns6 <= USED 0
dhcpv6_dump_packet <= USED 0
dhcpv6_get_cid <= USED 0
dhcpv6_opt_add_dns <= USED 0
dhcpv6_opt_add_netif_dns <= USED 0
dhcpv6_opt_add_sid <= USED 0
dhcpv6_svr_entry <= USED 0
;FILE dhcp_cntrl.o
dhcp_HdlInit <= USED 0
dhcp_HdlShutdown <= USED 0
frame_generic_packet <= USED 0
;FILE dhcp_interface.o
;FILE dhcpd.o
dhcpd_check_dongle_dns1 <= USED 0
dhcpd_check_dongle_dns2 <= USED 0
dhcpd_check_dongle_gw <= USED 0
dhcpd_check_dongle_ip <= USED 0
dhcpd_check_dongle_mask <= USED 0
dhcpd_get_opt43_flag <= USED 0
dhcpd_release_manual <= USED 0
dhcpd_reset_netif_ip <= USED 0
dhcpd_set_opt43_flag <= USED 0
;FILE dhm.o
mbedtls_dhm_parse_dhm <= USED 0
;FILE di2stub.o
di_bufferCreated <= USED 0
di_bufferDestroyed <= USED 0
di_displayLine <= USED 0
di_getInfo <= USED 0
di_getPixel <= USED 0
di_invalidateRect <= USED 0
di_powerDown <= USED 0
di_reset <= USED 0
di_setContrast <= USED 0
di_setPixel <= USED 0
di_updateDisplay <= USED 0
;FILE diagDB.o
__trace_APLP__AM__l1GsmCheckDisableD2inSetGsmSimb <= USED 0
__trace_APLP__AM__l1GsmCheckEnableD2inSetGsmSimb <= USED 0
__trace_AUDIO__ACM__ACMAudioBindECallGetHandler <= USED 0
__trace_AUDIO__ACM__ACMAudioOutGainSet <= USED 0
__trace_AUDIO__ACM__ACMAudioSetDTMFMode <= USED 0
__trace_AUDIO__ACM__ACMAudio_GetDSPSettings <= USED 0
__trace_AUDIO__ACM__ACMAudio_GetDSPSettings_Dump <= USED 0
__trace_AUDIO__ACM__ACMVoiceInGainSet <= USED 0
__trace_AUDIO__ACM__ACMVoiceMuteIn <= USED 0
__trace_AUDIO__ACM__ACMVoiceMuteOut <= USED 0
__trace_AUDIO__ACM__ACMVoiceOutGainSet <= USED 0
__trace_AUDIO__ACM__ACMVoiceSideToneGainSet <= USED 0
__trace_AUDIO__ACM__GSSPRead_WrongCookie <= USED 0
__trace_AUDIO__ACM__GSSP_SEL_BY_CP_dump <= USED 0
__trace_AUDIO__ACM__GetVoiceDataCallback <= USED 0
__trace_AUDIO__ACM__SendPCMToAP <= USED 0
__trace_AUDIO__ACM__getVoiceMix <= USED 0
__trace_AUDIO__ACM__getVoiceRx <= USED 0
__trace_AUDIO__ACM__getVoiceTx <= USED 0
__trace_AUDIO__AMR_ENC__amrGetPCMInHandle <= USED 0
__trace_AUDIO__ATC__ATC_Process_PCM_data_0 <= USED 0
__trace_AUDIO__ATC__ATC_Process_PCM_data_2 <= USED 0
__trace_AUDIO__ATC__AifSetPhoneStatus <= USED 0
__trace_AUDIO__ATC__ReceivePCMFromAP <= USED 0
__trace_AUDIO__ATC__set_phone_state <= USED 0
__trace_AUDIO__ATC__set_phone_state_done <= USED 0
__trace_AUDIO__AudioInit__amrEncInit <= USED 0
__trace_AUDIO__AudioInit__amrPlayInit <= USED 0
__trace_AUDIO__AudioInit__mp3PlayInit <= USED 0
__trace_AUDIO__HAL__AUDIOHAL_GetBufferSize_error <= USED 0
__trace_AUDIO__HAL__AifBindHeadsetDetectionCB <= USED 0
__trace_AUDIO__HAL__AifConfigure <= USED 0
__trace_AUDIO__HAL__AifHeadsetDetection <= USED 0
__trace_AUDIO__HAL__AifSetSideTone <= USED 0
__trace_AUDIO__HAL__AifSetSideTone1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err2 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Fixed <= USED 0
__trace_AUDIO__HAL__AifTonePause <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBindCodec_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBind_BTEvent_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetFMCodecGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetHeadsetInfo <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage1 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage2 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetDspRxMute <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetTxCodecGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSet_VAD_not_support <= USED 0
__trace_AUDIO__HAL__AudioHAL_AudioHAL_sspaSetDelay_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_FadingCtl <= USED 0
__trace_AUDIO__HAL__AudioHAL_SetFadingStep <= USED 0
__trace_AUDIO__HAL__AudioHAL_classgDCAlignSet <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetDelay <= USED 0
__trace_AUDIO__HAL__PCMControl <= USED 0
__trace_AUDIO__HAL__PCMControlErr <= USED 0
__trace_AUDIO__HAL__TestPlay_HalfHandler_mediaLoop_data <= USED 0
__trace_AUDIO__HAL__TestRecord_HalfHandler_mediaLoop <= USED 0
__trace_AUDIO__HAL__TestRecord_HalfHandler_mediaLoop_data <= USED 0
__trace_AUDIO__HAL__audiohal_bind_rx_handler <= USED 0
__trace_AUDIO__HAL__ningbo_power_i2c_read_error <= USED 0
__trace_AUDIO__HAL__tg_pcm_open_buffer <= USED 0
__trace_AUDIO__HAL__tg_pcm_play_buffer_init <= USED 0
__trace_AUDIO__HAL__tg_pcm_record_buffer_init <= USED 0
__trace_AUDIO__MP3_DEC__mp3Stop <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_1 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_CngUpdateMissed <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecOrReinit <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecWorkaround <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NoPacketFoundButRequired <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_SamplesOverflowCheck <= USED 0
__trace_AUDIO__NETEQ__WorkaroundForAquila1826 <= USED 0
__trace_AUDIO__OEMTest__Loopback <= USED 0
__trace_AUDIO__OEMTest__LoopbackDump <= USED 0
__trace_AUDIO__OEMTest__OEMTest_AMRRxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_AMRTxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_DTMFDetectionHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_LoopbackRxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_LoopbackRxHandler_2 <= USED 0
__trace_AUDIO__PCA_API__LteVoiceSuspend <= USED 0
__trace_AUDIO__PCA_API__RegisterCallback <= USED 0
__trace_AUDIO__PCA_API__SetAudioVoLTE <= USED 0
__trace_AUDIO__PCA_API__SetAudioVoLTE_CallNotActive <= USED 0
__trace_AUDIO__PCA_API__SetIPCControlErr1 <= USED 0
__trace_AUDIO__PCA_API__SetIPCControlErr2 <= USED 0
__trace_AUDIO__PCA_API__SetIPCControl_API <= USED 0
__trace_AUDIO__PCA_API__SetIPCControl_CMD <= USED 0
__trace_AUDIO__PCA_API__SetStreamPriority <= USED 0
__trace_AUDIO__PCA_API__StartVoIPAudio <= USED 0
__trace_AUDIO__PCA_API__StartVoIPAudio_ignore <= USED 0
__trace_AUDIO__PCA_API__StopVoIPAudio <= USED 0
__trace_AUDIO__PCA_API__StopVoIPAudio_ignore <= USED 0
__trace_AUDIO__RAT__audioBindGetRAT <= USED 0
__trace_AUDIO__RECORD__audio_record_open <= USED 0
__trace_AUDIO__RECORD__audio_record_read <= USED 0
__trace_AUDIO__RECORD__destroy_record_task <= USED 0
__trace_AUDIO__StubServer__audioDownLink20msCB_from_PS <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_close <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_record <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_already_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_already_stop_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_buf <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_data <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_notify_end <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_too_busy <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_underrun <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_buffer_init_error1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_return <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_already_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_already_stop_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_data <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_notify_end <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_overrun <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_too_busy <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_buffer_init_error1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_record <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_return <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_wait <= USED 0
__trace_AUDIO__TG_PCM__tg_volume_set <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_available <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_softgain <= USED 0
__trace_AUDIO__TRACK__audio_track_close <= USED 0
__trace_AUDIO__TRACK__destroy_track_task <= USED 0
__trace_AUDIO__Voice__CraneCodecADCOnOff_internal <= USED 0
__trace_AUDIO__Voice__IMSAmrRate <= USED 0
__trace_AUDIO__Voice__IMSAmrRateNotNormal <= USED 0
__trace_AUDIO__Voice__IMSAmrRateValue_0 <= USED 0
__trace_AUDIO__Voice__IMSAmrRateValue_1 <= USED 0
__trace_AUDIO__Voice__IMSEVSRateValue_2 <= USED 0
__trace_AUDIO__Voice__IMSEVSRateValue_3 <= USED 0
__trace_AUDIO__Voice__IMSSourceRateControl_1 <= USED 0
__trace_AUDIO__Voice__IMSSourceRateControl_2 <= USED 0
__trace_AUDIO__Voice__MuteVoicepath <= USED 0
__trace_AUDIO__Voice__PcmStreamRecordInvalidBuffer <= USED 0
__trace_AUDIO__Voice__ReceivePCMFromDSP <= USED 0
__trace_AUDIO__Voice__SendPCMToDSP <= USED 0
__trace_AUDIO__Voice__SetDebugCmdErr <= USED 0
__trace_AUDIO__Voice__SetVoiceEnhanceModuleControl <= USED 0
__trace_AUDIO__Voice__TxAMRFromBuf <= USED 0
__trace_AUDIO__Voice__audioGsmResumeCB <= USED 0
__trace_AUDIO__Voice__audioGsmResumeCB_L1G <= USED 0
__trace_AUDIO__Voice__audioGsmSuspendCB <= USED 0
__trace_AUDIO__Voice__audioGsmSuspendCB_L1G <= USED 0
__trace_AUDIO__Voice__audioGsmTestStartCB <= USED 0
__trace_AUDIO__Voice__audioGsmTestStopCB <= USED 0
__trace_AUDIO__Voice__audioResume <= USED 0
__trace_AUDIO__Voice__audioSuspend <= USED 0
__trace_AUDIO__Voice__handlePcmStreamRecordMsg <= USED 0
__trace_AUDIO__Voice__lteResume <= USED 0
__trace_AUDIO__Voice__lteSuspend <= USED 0
__trace_AUDIO__Voice__sacNotifyAudioConnected <= USED 0
__trace_AUDIO__Voice__vpathCTMControl <= USED 0
__trace_AUDIO__Voice__vpathCTMControl_0 <= USED 0
__trace_AUDIO__Voice__vpathDebugCmd <= USED 0
__trace_AUDIO__Voice__vpathDmaIntInd <= USED 0
__trace_AUDIO__Voice__vpathGSSPControl <= USED 0
__trace_AUDIO__Voice__vpathGSSPRead <= USED 0
__trace_AUDIO__Voice__vpathKWS_set_addr <= USED 0
__trace_AUDIO__Voice__vpathRxbControl <= USED 0
__trace_AUDIO__Voice__vpathSetSelfInvocation <= USED 0
__trace_AUDIO__Voice__vpathVadDumpControl <= USED 0
__trace_AUDIO__Voice__vpathVoiceControl <= USED 0
__trace_CIModem__DLC_DISC__Deactive_Arg_error <= USED 0
__trace_CIModem__DLC_DISC__Deactive_req_error <= USED 0
__trace_CIModem__DLC_DISC__PSDATA_RUN <= USED 0
__trace_CIModem__RecTask__linkstatusind <= USED 0
__trace_CIModem__RecTask__linkstatusind_abnormal <= USED 0
__trace_CP_APP__AAM_API__CPAStateChangeDualLinkLTE1 <= USED 0
__trace_CP_APP__AAM_API__CellularPowerAppExitFromOosDualLinkLTE1 <= USED 0
__trace_CP_APP__AAM_API__LtePowerOffInDualLink1 <= USED 0
__trace_CP_APP__AAM_API__LtePowerOnInDualLink1 <= USED 0
__trace_DIAG__PROTOCOL__L2_Rx_Comm_Timout <= USED 0
__trace_DIAG__PROTOCOL__L2_Rx_Error <= USED 0
__trace_DRAT__DEBUG__wgi_Bind_function <= USED 0
__trace_DRAT__DEBUG__wgi_Bind_function2 <= USED 0
__trace_Diag__Utils__TraceStopUSB <= USED 0
__trace_FDI__FDI5to6__FDI_feof <= USED 0
__trace_FDI__fatsys__F_Makedir <= USED 0
__trace_FDI__fatsys__F_Makedir_succeed <= USED 0
__trace_FDI__fatsys__F_eof_start <= USED 0
__trace_FDI__fatsys__F_eof_succeed <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx168 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx178 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx198 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx199 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx200 <= USED 0
__trace_GPLC__DCXOInit__SetRfDcxoIsUsingFlgWithGPIO <= USED 0
__trace_GRFD__CALIB__RspGet4GCalibrationStatus <= USED 0
__trace_HAL__IPC__APLP_AAAPDataBufferFreeNotification <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_DSP <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_DSP1 <= USED 0
__trace_HAL__PM802S__PM802S_LDO8_2V8 <= USED 0
__trace_HAL__PM802__PM802_LDO8_2V8 <= USED 0
__trace_HAL__PM803__PM803_LDO7_1V2 <= USED 0
__trace_HAL__PM812__PM812_REG_DUMP <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode1 <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeIBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeSBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DLrequest2 <= USED 0
__trace_HAL__USIM__USIMOemFlag <= USED 0
__trace_HAL__USIM__USIMVersionGet <= USED 0
__trace_HAL__USIM__USIM_Clck_Check <= USED 0
__trace_IMS__GetIMSEnable__1 <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsRxdefault <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsTxdefault <= USED 0
__trace_IMS__SetStChannel__ENTER_IN <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_11 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_14 <= USED 0
__trace_L1A__L1ACAL__L1aTempReadingCallback_1 <= USED 0
__trace_L1A__L1ACAL__PlatToL1aCommonIpcReq_enter <= USED 0
__trace_L1A__L1ACAL__getTxPowerBackoffMode_00 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimGsmActivated_SIM1 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimGsmActivated_SIM2 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimWbActivated_SIM1 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimWbActivated_SIM2 <= USED 0
__trace_L1A__l1a_dsds__wliDsdsLteActivateRsp_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsLteInactivateRsp_entry <= USED 0
__trace_L1A__l1adrat__L1AEnableD2EntryDualLink1 <= USED 0
__trace_LTE_PS__ERRC_AIS__AIS_DECODE_DL_DCCH_FromOtherRat <= USED 0
__trace_LTE_PS__ERRC_BAND__ErrcDsIsErrcSuspended_entry <= USED 0
__trace_LTE_PS__ERRC_BAND__LteBandGetBandsInformForPlmnSearch_entry <= USED 0
__trace_LTE_PS__ERRC_BAND__LteRrcCsrCheckIfBandInMultiBandListSupported_err <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_check <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_head <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_skip <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerConstructAndSendEutraProximityInd_earfcnErr <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerGetHoNotAlloweFlag_1 <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerGetRplmn_false <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Cell <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_CellNum <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Freq <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_FreqNum <= USED 0
__trace_LTE_PS__ERRC_CSR__GsmGetBandWidthOfSertvingCell_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCheckLockOnLteFreq_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrCheckErrcWorkingPlmn_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrCheckErrcWorkingPlmn_2 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrCheckErrcWorkingPlmn_state <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrHandleEcphySwitchRatToUmtsCnf_warning <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrIsRequestPlmnListContainCMCC_no <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrRemoveCellFromBchCrcErrorBarList_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCheckIfCsPagingOrImsPaging_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCheckIfCsPagingOrImsPaging_2 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCsrCheckIfNcellOnIntendedBand_leave <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcQueryTddConfig_error <= USED 0
__trace_LTE_PS__ERRC_DLNK__LteRrcCheckCardNum_1 <= USED 0
__trace_LTE_PS__ERRC_DSDS__LteRrcDsdsCheckIfIsCellSearching_result <= USED 0
__trace_LTE_PS__ERRC_ETE__LteAisEncodeUeCapabilityRatContainer_1 <= USED 0
__trace_LTE_PS__ERRC_ETE__LteEteSendEcphyStopEngInfoReq_notStat <= USED 0
__trace_LTE_PS__ERRC_ETE__lterrcSendFakeExtendedServiceRequest_2 <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrChangeA3ConditionTimeToTrigger_changed <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIfNeedTriggerB2_FALSE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIfScellBad_TRUE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIsCmccEsrvccImsStartForSpecialTac_TRUE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckMoreThan1DbForIrat_FALSE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrIsBadQuality_TRUE <= USED 0
__trace_LTE_PS__ERRC_SECURITY__LteCerHandleSecurityConfigForHOFromUtra1 <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_1 <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_2 <= USED 0
__trace_LTE_PS__ERR_UTIL__LLteRrcCheckEnterConnected_2 <= USED 0
__trace_LTE_PS__ERR_UTIL__LteRrcCsrGetOtherCardLteIsOos_enter <= USED 0
__trace_LTE__BM__LteBmProcessDataAbortInd0 <= USED 0
__trace_LTE__BM__LtePDCPReleaseRxL2BBlock2t <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000726 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000727 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer008 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer09w <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer009 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0_umts <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2buffer0001 <= USED 0
__trace_LTE__MACDL__LteMacdlHandleEphyDataIndDiscardTB <= USED 0
__trace_LTE__MACUL__LteMacTrafficReq4 <= USED 0
__trace_LTE__MACUT__LteMacutGsmVoiceCallInform1 <= USED 0
__trace_LTE__MACUT__isBadQualityDetected1 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq01 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq02 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq03 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea_ebiInvalid <= USED 0
__trace_LTE__PDCP__tLtePdcpSrbDataIntegrityError <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_5869 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_9858 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_gg68 <= USED 0
__trace_LTE__RABM__LteRabmProcessSnMultiDataListReq_66 <= USED 0
__trace_LTE__RABM__LteRabmStartWaitToReestTimer1 <= USED 0
__trace_LTE__RABM__LteRabmStopWaitToReestTimer1 <= USED 0
__trace_LTE__RLC__LteAmRlcDiscardUlPackets0a0 <= USED 0
__trace_MAT__MATHandleResponse___MATConfIndCB_0 <= USED 0
__trace_MEM__DUMP__GetUUID_High <= USED 0
__trace_MEM__DUMP__GetUUID_Low <= USED 0
__trace_MIFI__FOTA__126 <= USED 0
__trace_MIFI__FOTA__127 <= USED 0
__trace_MIFI__FOTA__128 <= USED 0
__trace_MIFI__FOTA__129 <= USED 0
__trace_MIFI__FOTA__338 <= USED 0
__trace_MIFI__FOTA__345 <= USED 0
__trace_MIFI__FOTA__346 <= USED 0
__trace_MIFI__FOTA__351 <= USED 0
__trace_MIFI__FOTA__352 <= USED 0
__trace_MIFI__FOTA__353 <= USED 0
__trace_MIFI__FOTA__354 <= USED 0
__trace_MIFI__FOTA__355 <= USED 0
__trace_MIFI__FOTA__369 <= USED 0
__trace_MIFI__FOTA__370 <= USED 0
__trace_MIFI__FOTA__373 <= USED 0
__trace_MIFI__FOTA__376 <= USED 0
__trace_MIFI__FOTA__377 <= USED 0
__trace_MIFI__FOTA__378 <= USED 0
__trace_MIFI__FOTA__379 <= USED 0
__trace_MIFI__FOTA__380 <= USED 0
__trace_MIFI__FOTA__393 <= USED 0
__trace_MIFI__FOTA__394 <= USED 0
__trace_MIFI__FOTA__395 <= USED 0
__trace_MIFI__FOTA__396 <= USED 0
__trace_MIFI__FOTA__397 <= USED 0
__trace_MIFI__FOTA__398 <= USED 0
__trace_MIFI__HTTPCLI__10 <= USED 0
__trace_MIFI__HTTPCLI__11 <= USED 0
__trace_MIFI__HTTPCLI__12 <= USED 0
__trace_MIFI__HTTPCLI__126 <= USED 0
__trace_MIFI__HTTPCLI__128 <= USED 0
__trace_MIFI__HTTPCLI__13 <= USED 0
__trace_MIFI__HTTPCLI__14 <= USED 0
__trace_MIFI__HTTPCLI__15 <= USED 0
__trace_MIFI__HTTPCLI__16 <= USED 0
__trace_MIFI__HTTPCLI__3 <= USED 0
__trace_MIFI__HTTPCLI__4 <= USED 0
__trace_MIFI__HTTPCLI__7 <= USED 0
__trace_MIFI__HTTPCLI__8 <= USED 0
__trace_MIFI__HTTPCLI__9 <= USED 0
__trace_MIFI__LOG__SaveToFS <= USED 0
__trace_MIFI__LWIP__lwip_Ip_filter_113 <= USED 0
__trace_MIFI__LWIP__lwip_Ip_filter_114 <= USED 0
__trace_MIFI__LWIP__lwip_Ip_filter_116 <= USED 0
__trace_MIFI__LWIP__lwip_Ip_filter_117 <= USED 0
__trace_MIFI__LWIP__lwip_api_1062 <= USED 0
__trace_MIFI__LWIP__lwip_api_1070 <= USED 0
__trace_MIFI__LWIP__lwip_api_1097 <= USED 0
__trace_MIFI__LWIP__lwip_api_1121 <= USED 0
__trace_MIFI__LWIP__lwip_api_1125 <= USED 0
__trace_MIFI__LWIP__lwip_api_1979 <= USED 0
__trace_MIFI__LWIP__lwip_api_1996 <= USED 0
__trace_MIFI__LWIP__lwip_api_2001 <= USED 0
__trace_MIFI__LWIP__lwip_api_2106 <= USED 0
__trace_MIFI__LWIP__lwip_api_2108 <= USED 0
__trace_MIFI__LWIP__lwip_api_2272 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_120 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_751 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_950 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_954 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_965 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_970 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_971 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_977 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_1250 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_1382 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_1450 <= USED 0
__trace_MIFI__LWIP__lwip_customer_700 <= USED 0
__trace_MIFI__LWIP__lwip_customer_701 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6_524 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6_527 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_133 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_166 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_281 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_293 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_300 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_308 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_311 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_314 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_317 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_320 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_322 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_328 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_336 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_355 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_368 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_394 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_412 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_70 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_829 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_835 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_847 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_851 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1314 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1319 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1330 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2220 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2231 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2234 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2239 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2244 <= USED 0
__trace_MIFI__LWIP__lwip_dns_372 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_1990 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2000 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2001 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2010 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2011 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2020 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2134 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2135 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2136 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_693 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_719 <= USED 0
__trace_MIFI__LWIP__lwip_ip6_addr_301 <= USED 0
__trace_MIFI__LWIP__lwip_ip_filter_425 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_741 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_871 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_881 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_940 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_968 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_983 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2500 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2501 <= USED 0
__trace_MIFI__LWIP__lwip_minis_1200 <= USED 0
__trace_MIFI__LWIP__lwip_minis_1201 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_1436 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_1460 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_2857 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_2867 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2130 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2135 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2136 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2160 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2200 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2220 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2230 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_392 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_481 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_484 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_497 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_336 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_338 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_353 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1004 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1070 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1270 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1308 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1650 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1669 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2518 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2598 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2649 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2650 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2659 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2660 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2661 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2662 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2664 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2665 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2685 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2695 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2700 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2725 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2740 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2745 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2750 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2775 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2780 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2800 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2810 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2815 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2820 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2850 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2860 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2865 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2880 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2890 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2891 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2942 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2969 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2970 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2971 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2972 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2975 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2976 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3010 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3080 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3095 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3100 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3115 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3150 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3175 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3180 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3190 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3200 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3210 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3215 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3219 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3220 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3260 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3262 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3263 <= USED 0
__trace_MIFI__LWIP__lwip_netif_458 <= USED 0
__trace_MIFI__LWIP__lwip_netif_511 <= USED 0
__trace_MIFI__LWIP__lwip_netif_529 <= USED 0
__trace_MIFI__LWIP__lwip_netif_544 <= USED 0
__trace_MIFI__LWIP__lwip_netif_549 <= USED 0
__trace_MIFI__LWIP__lwip_netif_578 <= USED 0
__trace_MIFI__LWIP__lwip_netif_583 <= USED 0
__trace_MIFI__LWIP__lwip_netif_712 <= USED 0
__trace_MIFI__LWIP__lwip_netif_716 <= USED 0
__trace_MIFI__LWIP__lwip_netif_985 <= USED 0
__trace_MIFI__LWIP__lwip_netif_998 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_375 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_377 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_378 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_388 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_391 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_410 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_421 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_566 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_572 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1025 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1103 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1104 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1105 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1106 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1171 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1172 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1203 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1204 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1205 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1249 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1250 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1251 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1252 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1300 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1301 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1302 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1303 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1304 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1310 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1311 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1312 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1313 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1314 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1360 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1361 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1362 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1363 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1364 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1365 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1366 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1367 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1370 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1371 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1372 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1373 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1374 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1375 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1376 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1444 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1904 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1912 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1913 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1955 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2004 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2012 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2013 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2118 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2120 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2140 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2148 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2158 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2160 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2203 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2209 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2247 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2283 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2314 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2324 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2325 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2356 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2369 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2372 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2379 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2385 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2390 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2394 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2395 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2442 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2460 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_740 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_741 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_745 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_947 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_948 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_949 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_417 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_425 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_432 <= USED 0
__trace_MIFI__LWIP__lwip_pbuf_1179 <= USED 0
__trace_MIFI__LWIP__lwip_pbuf_1182 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_580 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_581 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_582 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_750 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_751 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_752 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_753 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_754 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2790 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2791 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2792 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2793 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2794 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2795 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2796 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2797 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2800 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2801 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2802 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2803 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2870 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2871 <= USED 0
__trace_MIFI__LWIP__lwip_sttest_2872 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1080 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1083 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1109 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1114 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1117 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1138 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1149 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1153 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1173 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1193 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1203 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1230 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1238 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1247 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1255 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1294 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1298 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1311 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1313 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1333 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1339 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1345 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1393 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1752 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1754 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_191 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2333 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2335 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2339 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2341 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2345 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2347 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2400 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2401 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_in_128 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_in_1448 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1892 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1894 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1899 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1915 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1946 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1948 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1950 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_1970 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_out_2000 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1225 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1230 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1622 <= USED 0
__trace_MIFI__LWIP__lwip_timers_169 <= USED 0
__trace_MIFI__RNDIS__HALT_MSG <= USED 0
__trace_MIFI__RNDIS__INITIALIZE_MSG <= USED 0
__trace_MIFI__RNDIS__RESET_MSG <= USED 0
__trace_MIFI__RNDIS__Rndis_init_response <= USED 0
__trace_MIFI__RNDIS__Rndis_init_response1 <= USED 0
__trace_MIFI__RNDIS__Rndis_remove_hdr <= USED 0
__trace_MIFI__RNDIS__Rndis_remove_hdr1 <= USED 0
__trace_MIFI__RNDIS__UNKNOWN_MSG <= USED 0
__trace_MIFI__utl__130 <= USED 0
__trace_MIFI__utl__131 <= USED 0
__trace_MIFI__utl__161 <= USED 0
__trace_MIFI__utl__162 <= USED 0
__trace_MIFI__utl__26 <= USED 0
__trace_MIFI__utl__65 <= USED 0
__trace_MIFI__utl__66 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMutexs_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMutexs_2 <= USED 0
__trace_OSA__OSA__AccTimerStart <= USED 0
__trace_PM__CommPM__FORCED2 <= USED 0
__trace_PM__CommPM__TCMStartFITimer_1 <= USED 0
__trace_PM__CommPM__TCMTestTimerHandle_1 <= USED 0
__trace_PM__FreqChange__psram_phy_fc_config1 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_011 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_3 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_33 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_4 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_44 <= USED 0
__trace_PSNAS__HOTRESET__saveContext_begin <= USED 0
__trace_PSNAS__HOTRESET__saveContext_end <= USED 0
__trace_PSNAS__POWEROFF__saveContextForPowerOffOnKey_begin <= USED 0
__trace_PSNAS__abmm__abmmBmRequestGPRSService_41 <= USED 0
__trace_PSNAS__abmm__abmmBmRequestGPRSService_42 <= USED 0
__trace_PSNAS__abmm__abmmRmStopTimersWithoutHplmnTimer_1 <= USED 0
__trace_PS_2G__GRR__CR595_1 <= USED 0
__trace_PS_2G__GRR__CR595_11_0 <= USED 0
__trace_PS_2G__GRR__CR595_11_1 <= USED 0
__trace_PS_2G__GRR__CR595_11_2 <= USED 0
__trace_PS_2G__GRR__CR595_11_3 <= USED 0
__trace_PS_2G__GRR__CR595_11_4 <= USED 0
__trace_PS_2G__GRR__CR595_11_5 <= USED 0
__trace_PS_2G__GRR__CR595_11_6 <= USED 0
__trace_PS_2G__GRR__CR595_2 <= USED 0
__trace_PS_2G__GRR__CR595_2_00 <= USED 0
__trace_PS_2G__GRR__CR595_2_01 <= USED 0
__trace_PS_2G__GRR__CR595_2_02 <= USED 0
__trace_PS_2G__GRR__CR595_2_03 <= USED 0
__trace_PS_2G__GRR__CR595_2_11 <= USED 0
__trace_PS_2G__GRR__CR595_3 <= USED 0
__trace_PS_2G__GRR__CR595_4 <= USED 0
__trace_PS_2G__GRR__CR595_5 <= USED 0
__trace_PS_3G__ABGP__abmmsendMmrSetMedata_0 <= USED 0
__trace_PS_3G__ABMM__ABMM_RM_STATE_FUNC_abmmRmStateEcallExplicitReg <= USED 0
__trace_PS_3G__ABMM__AbNonCacheGetRecovryFlag <= USED 0
__trace_PS_3G__ABMM__AbNonCacheSetRecovryFlag <= USED 0
__trace_PS_3G__ABMM__MmNonCacheGetRecovryState <= USED 0
__trace_PS_3G__ABMM__MmNonCacheSetRecovryState <= USED 0
__trace_PS_3G__ABMM__abmmBlUpdateLteArfcnListsFromCuList <= USED 0
__trace_PS_3G__ABMM__abmmBlUpdateLteArfcnListsFromCuList_1 <= USED 0
__trace_PS_3G__ABMM__abmmBlmGetArfcnNvm_5 <= USED 0
__trace_PS_3G__ABMM__abmmBlmGetArfcnNvm_6 <= USED 0
__trace_PS_3G__ABMM__abmmBlmInsertArfcnNvm_2 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewFddLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewTdLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved3alue_0 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved4Value_0 <= USED 0
__trace_PS_3G__ABMM__abmmPlAddToTempFplmnList_1 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_7 <= USED 0
__trace_PS_3G__ABMM__abmmPlIsOPlmnsForRrc <= USED 0
__trace_PS_3G__ABMM__abmmRmContinue_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmInterruptReg_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsHplmnForOtherTask_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmNarrowHplmnSearchNetworkMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmPartiallyRegistered_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenNatLaInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenPlmnInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245EventHandle_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245Restart_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245Stop_1 <= USED 0
__trace_PS_3G__ABSM__absmProcessAlsiWriteSmCnfForStore_2 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_1 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_2 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_3 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_4 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_FDD <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_GSM <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_INVALID <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_ENH_TDD <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_PMO_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PMO_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_PSI5_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PSI5_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_3G_MEAS_FDD_1 <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_3G_MEAS_TDD_1 <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_ENH <= USED 0
__trace_PS_3G__GRR__EncodeR99MsRadAccCapbIE_0 <= USED 0
__trace_PS_3G__GRR__GpDecNcellParam_max <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0a <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0d <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0f <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_1 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_10 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_2 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_3 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_4 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_5 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_6 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_7 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_000 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_001 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_002 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_003 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_006 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_007 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_009 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_1 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_2 <= USED 0
__trace_PS_3G__GRR__GpEncPrrR99Part_1 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_0 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_1 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_10 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_11 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_2 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_3 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_4 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_5 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_6 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_6f <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_7 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_8 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_9 <= USED 0
__trace_PS_3G__GRR__IMM_ASS_EDGE_GPRS <= USED 0
__trace_PS_3G__GRR__LB_1 <= USED 0
__trace_PS_3G__GRR__LB_12 <= USED 0
__trace_PS_3G__GRR__LB_2 <= USED 0
__trace_PS_3G__GRR__LB_3 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_60 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_70 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_80 <= USED 0
__trace_PS_3G__KIOS__WarnAssertFail <= USED 0
__trace_PS_3G__MM__DecodeNonAccStrMessage <= USED 0
__trace_PS_3G__MM__L3DecodeGprsMessage <= USED 0
__trace_PS_3G__MM__L3EncodeGprsMessage <= USED 0
__trace_PS_3G__MM__L3EncodeMessage <= USED 0
__trace_PS_3G__MM__MmDualGrrChekDMmIsMmInDedicate_0 <= USED 0
__trace_PS_3G__MM__MmDualGrrChekMmIsDMmInDedicate_0 <= USED 0
__trace_PS_3G__MM__MmDualGrrChekMmIsDMmInDedicate_1 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmPendingMtCall_0 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmPsSuspend_1 <= USED 0
__trace_PS_3G__MM__MmHasPendingPlmnSearch <= USED 0
__trace_PS_3G__MM__MmMain_0606 <= USED 0
__trace_PS_3G__Mm__MmIsIratToLteAllowed_1 <= USED 0
__trace_PS_3G__PSM__saveContext_2 <= USED 0
__trace_PS_3G__RR_COM__RrDsCheckEnterSameOperatorModeSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsCheckWorkOnSameSuitableRatSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsGetIratDsAbortSearchCnfSendingSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsGetetSameOperatorSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsSetIratReselectingSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsSetSameOperatorSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetGsmPagingDrxLength_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetGsmPagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsVoiceBadInd_0 <= USED 0
__trace_PS_3G__SAC_DAT__sacGetCurrentNsapi <= USED 0
__trace_PS_3G__SM__cancelRel5QoS_1 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced0 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced01 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced02 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced04 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced05 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced10 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced11 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced14 <= USED 0
__trace_PS_3G__URRC_CSRR__IsCtccPlmn_1 <= USED 0
__trace_PS_3G__URRC_CSRR__IsRedirectionFailureHandlingEnable_0 <= USED 0
__trace_PS_3G__URRC_CSRR__NeedAbandonGsmBandSearctWhenRedirectFromLTE_0 <= USED 0
__trace_PS_3G__URRC_CSRR__NeedAbandonGsmBandSearctWhenRedirectFromLTE_1 <= USED 0
__trace_PS_3G__URRC_CSR_Priority_1__RrIsTestSimGcfMode_1 <= USED 0
__trace_PS_4G_ERRC__COMDB__RrComDbSetManuallySelectedPlmn_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsCheckIfFgPlmnOngoing_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsDbInitInterferenceDetection_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsDbIsPscInUmtsFindCellCnf_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndAddEarfcnToScanOrderTable_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndDetermineInterferedCarriersNum_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndDetermineInterferedCarriersNum_2 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsScnAllocationWbScoreResultTable_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsScnAllocationWbScoreResultTable_2 <= USED 0
__trace_PS_4G__MM__EmmImsServiceEndProtectionTimerExpiry_1 <= USED 0
__trace_PS_4G__MM__EmmServiceRejectReceived_0 <= USED 0
__trace_PS_4G__MM__EmmStartT3423_1 <= USED 0
__trace_PS_4G__MM__EmmT3448Expiry_1 <= USED 0
__trace_PS_4G__MM__MmIsInCMCCNetWork_1 <= USED 0
__trace_PS_4G__MM__MmIsInCUCCNetWork_1 <= USED 0
__trace_PS_4G__MM__MmPendEmmEsmUnitDataReq_1 <= USED 0
__trace_PS_4G__MM__MmSendMmrFlushFlashEnd_0 <= USED 0
__trace_PS_4G__MM__MmSendMmrFlushFlashReq_0 <= USED 0
__trace_PS_4G__PLMS__PlmsStoreSeqNum_1 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_0 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_1 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_freemem_debug <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_putmem_debug <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_0 <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_1 <= USED 0
__trace_PS_PLT__IPNET_UL__ipnet_uplink_transfer_ind_1 <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__DiscardPacket <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__GetPacket <= USED 0
__trace_PS_PLT__LWIP_DL__lwip_downlink_putmem_debug <= USED 0
__trace_PS_PLT__LWIP_UL__ipnet_uplink_transfer_ind_0 <= USED 0
__trace_PS_PLT__LWIP_UL__usbnet_uplink_ps_list_construct_1 <= USED 0
__trace_PS_PLT__LWIP_UL__usbnet_uplink_ps_list_construct_2 <= USED 0
__trace_PS_PLT__ModemTFT__DirectionAndNextHeader <= USED 0
__trace_PS_PLT__ModemTFT__FindOldTft <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_1 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_2 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_dstport <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_sorceport <= USED 0
__trace_PS_PLT__ModemTFT__PFNodeAllocFailed <= USED 0
__trace_PS_PLT__ModemTFT__PFReconfigure <= USED 0
__trace_PS_PLT__ModemTFT__PacketFilter_id <= USED 0
__trace_PS_PLT__ModemTFT__QosInfo <= USED 0
__trace_PS_PLT__ModemTFT__RemoteAddrInfo <= USED 0
__trace_PS_PLT__ModemTFT__SpiInfo <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft_1 <= USED 0
__trace_PS_PLT__ModemTFT__addNewPFOK <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_0 <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_1 <= USED 0
__trace_PS_PLT__ModemTFT__filtercount <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_Add <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_port <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp4_No_Proto <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp6 <= USED 0
__trace_PS_PLT__ModemTFT__ipfragment_failure <= USED 0
__trace_PS_PLT__ModemTFT__ipv6fragment_failure <= USED 0
__trace_PS_PLT__ModemTFT__portInfo <= USED 0
__trace_PS_PLT__ModemTFT__submasksetip6 <= USED 0
__trace_PS_PLT__ModemTFT__submasksetipv4 <= USED 0
__trace_PS_PLT__ModemTFT__submasksetipv7 <= USED 0
__trace_PS_PLT__PrintCid__GetDefault4g_parameter_invalid <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocERROR <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocFailed <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocOk <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektHighLevel <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektPush1 <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektPush2 <= USED 0
__trace_PS_PLT__VoLteBuffer__VoLTERxRelease <= USED 0
__trace_PS_PLT__VoLteBuffer__VoLTETxRegister <= USED 0
__trace_PS_PLT__VoLteToAP__ToAP <= USED 0
__trace_PS_PLT__VoLteToLocal__ToVolteEngine <= USED 0
__trace_PS_PLT__VolteUpLink__AllocBufferfail <= USED 0
__trace_PS_PLT__VolteUpLink__Allocparameterfail <= USED 0
__trace_PS_PLT__VolteUpLink__Allocreturnbuffer <= USED 0
__trace_PS_PLT__VolteUpLink__Freecparameterfail <= USED 0
__trace_PS_PLT__VolteUpLink__uplinkAddNode <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_0 <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_2 <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_Leave <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_enter <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_0 <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_2 <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_Leave <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_enter <= USED 0
__trace_PS_PLT__Volte__IpFilterComparison <= USED 0
__trace_PS_PLT__Volte__RemoveIp4Filter_enter <= USED 0
__trace_PS_PLT__Volte__RemoveIp4Filter_leave <= USED 0
__trace_PS_PLT__resume__ipnet_rx_buffer <= USED 0
__trace_PS_PLT__usbnet_uplink_ps_list_construct__usbnet_uplink_ps_list_construct_0 <= USED 0
__trace_PS_PLT__usbnet_uplink_ps_list_construct__usbnet_uplink_ps_list_construct_1 <= USED 0
__trace_SAC__DATA__SAC_TRACE_API <= USED 0
__trace_SAC__DEV__sacCiDevPrimSetBandModeReqFunc_14 <= USED 0
__trace_SAC__DEV__sacCiDevPrimSetBandModeReqFunc_15 <= USED 0
__trace_SAC__DEV__sacCiDevPrimSetBandModeReqFunc_16 <= USED 0
__trace_SAC__DEV__sacCiDevPrimSetBandModeReqFunc_17 <= USED 0
__trace_SAC__PS__sacPsGetSim4GQciValue_1 <= USED 0
__trace_SAC__SACDEV__sacCiDevPrimGetInternalRevisionIdReqFunc_01 <= USED 0
__trace_SAC__SACLIB__sacShCheckSpecPendingCiReq_1 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_1 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_2 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_3 <= USED 0
__trace_SSIPC__NET_SIPC__MATGetDataFromStr1 <= USED 0
__trace_SSIPC__NET_SIPC__MATGetDataFromStr2 <= USED 0
__trace_SW_PLAT__DIAG__DIAG_INT_RX_COMM_ALLOC_FAIL <= USED 0
__trace_SW_PLAT__DIAG__DIAG_INT_RX_COMM_MSG_TO_Q_FAIL <= USED 0
__trace_SW_PLAT__DIAG__DIAG_INT_RX_COMM_Multiple_DATA <= USED 0
__trace_SW_PLAT__DIAG__DIAG_INT_RX_COMM_NO_DATA <= USED 0
__trace_SW_PLAT__DIAG__RX_AP_CMD <= USED 0
__trace_SW_PLAT__DIAG__setGLFeatureFlag <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR0 <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR1 <= USED 0
__trace_SW_PLAT__I2C__I2CMasterNotifyDataReceived2 <= USED 0
__trace_SW_PLAT__I2C__I2C_ERROR_INFO_READ <= USED 0
__trace_SW_PLAT__I2C__I2C_ERROR_INFO_WRITE <= USED 0
__trace_SW_PLAT__I2C__I2C_GOT_ERROR <= USED 0
__trace_SW_PLAT__I2C__I2C_RESET <= USED 0
__trace_SW_PLAT__I2C__NotifyErrorCBack <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive2 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend2 <= USED 0
__trace_SW_PLAT__Log__LOG <= USED 0
__trace_SW_PLAT__NUTick__dumpNutickDelayInfo_0 <= USED 0
__trace_SW_PLAT__NUTick__dumpNutickDelayInfo_1 <= USED 0
__trace_SW_PLAT__NUTick__dumpNutickDelayInfo_2 <= USED 0
__trace_SW_PLAT__NVM__BAD_ComBasicCfg <= USED 0
__trace_SW_PLAT__NVM__Create <= USED 0
__trace_SW_PLAT__NVM__NoFile <= USED 0
__trace_SW_PLAT__NVM__OldVerUpdate <= USED 0
__trace_SW_PLAT__PERFORMANCE__IdleTaskCreateError <= USED 0
__trace_SW_PLAT__PERFORMANCE__IdleTaskCreateError1 <= USED 0
__trace_SW_PLAT__RDA__ReliableDataUnPackDo <= USED 0
__trace_SW_PLAT__RDA__rd_Port_DataInd <= USED 0
__trace_SW_PLAT__partition_ops__read <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_01 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_02 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_03 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_04 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_05 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_READ_NVM_06 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_WRITE_NVM_01 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_WRITE_NVM_02 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_WRITE_NVM_03 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_WRITE_NVM_04 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_WRITE_NVM_05 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_1 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_2 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_3 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_4 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_5 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_7 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_8 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_9 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG001 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG002 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG003 <= USED 0
__trace_diag__throughput__Test_02 <= USED 0
__trace_generic_mini_no_params_func <= USED 0
__trace_generic_mini_params_func <= USED 0
__trace_generic_no_params_func_000 <= USED 0
__trace_generic_no_params_func_001 <= USED 0
__trace_generic_no_params_func_002 <= USED 0
__trace_generic_no_params_func_003 <= USED 0
__trace_generic_no_params_func_004 <= USED 0
__trace_generic_no_params_func_005 <= USED 0
__trace_generic_no_params_func_006 <= USED 0
__trace_generic_no_params_func_007 <= USED 0
__trace_generic_no_params_func_008 <= USED 0
__trace_generic_no_params_func_009 <= USED 0
__trace_generic_no_params_func_010 <= USED 0
__trace_generic_no_params_func_011 <= USED 0
__trace_generic_no_params_func_012 <= USED 0
__trace_generic_no_params_func_013 <= USED 0
__trace_generic_no_params_func_014 <= USED 0
__trace_generic_no_params_func_015 <= USED 0
__trace_generic_no_params_func_016 <= USED 0
__trace_generic_no_params_func_017 <= USED 0
__trace_generic_no_params_func_018 <= USED 0
__trace_generic_no_params_func_019 <= USED 0
__trace_generic_no_params_func_020 <= USED 0
__trace_generic_no_params_func_021 <= USED 0
__trace_generic_no_params_func_022 <= USED 0
__trace_generic_no_params_func_023 <= USED 0
__trace_generic_no_params_func_024 <= USED 0
__trace_generic_no_params_func_025 <= USED 0
__trace_generic_no_params_func_026 <= USED 0
__trace_generic_no_params_func_027 <= USED 0
__trace_generic_no_params_func_028 <= USED 0
__trace_generic_params_func_000 <= USED 0
__trace_generic_params_func_001 <= USED 0
__trace_generic_params_func_002 <= USED 0
__trace_generic_params_func_003 <= USED 0
__trace_generic_params_func_004 <= USED 0
__trace_generic_params_func_005 <= USED 0
__trace_generic_params_func_006 <= USED 0
__trace_generic_params_func_007 <= USED 0
__trace_generic_params_func_008 <= USED 0
__trace_generic_params_func_009 <= USED 0
__trace_generic_params_func_010 <= USED 0
__trace_generic_params_func_011 <= USED 0
__trace_generic_params_func_012 <= USED 0
__trace_generic_params_func_013 <= USED 0
__trace_generic_params_func_014 <= USED 0
__trace_generic_params_func_015 <= USED 0
__trace_generic_params_func_016 <= USED 0
__trace_generic_params_func_017 <= USED 0
__trace_generic_params_func_018 <= USED 0
__trace_generic_params_func_019 <= USED 0
__trace_generic_params_func_020 <= USED 0
__trace_generic_params_func_021 <= USED 0
__trace_generic_params_func_022 <= USED 0
__trace_generic_params_func_023 <= USED 0
__trace_generic_params_func_024 <= USED 0
__trace_generic_params_func_025 <= USED 0
__trace_generic_params_func_026 <= USED 0
__trace_generic_params_func_027 <= USED 0
__trace_generic_params_func_028 <= USED 0
__trace_generic_params_func_029 <= USED 0
__trace_generic_params_func_030 <= USED 0
__trace_generic_params_func_031 <= USED 0
__trace_generic_params_func_032 <= USED 0
__trace_generic_params_func_033 <= USED 0
__trace_generic_params_func_034 <= USED 0
__trace_generic_params_func_035 <= USED 0
__trace_generic_params_func_036 <= USED 0
__trace_generic_params_func_037 <= USED 0
__trace_generic_params_func_038 <= USED 0
__trace_generic_params_func_039 <= USED 0
__trace_generic_params_func_040 <= USED 0
__trace_generic_params_func_041 <= USED 0
__trace_generic_params_func_042 <= USED 0
__trace_generic_params_func_043 <= USED 0
__trace_generic_params_func_044 <= USED 0
__trace_generic_params_func_045 <= USED 0
__trace_generic_params_func_046 <= USED 0
__trace_generic_params_func_047 <= USED 0
__trace_generic_params_func_048 <= USED 0
__trace_generic_params_func_049 <= USED 0
__trace_generic_params_func_050 <= USED 0
__trace_generic_params_func_051 <= USED 0
__trace_generic_params_func_052 <= USED 0
__trace_generic_params_func_053 <= USED 0
__trace_generic_params_func_054 <= USED 0
__trace_generic_params_func_055 <= USED 0
__trace_generic_params_func_056 <= USED 0
__trace_generic_params_func_057 <= USED 0
__trace_generic_params_func_058 <= USED 0
__trace_generic_params_func_059 <= USED 0
__trace_generic_params_func_060 <= USED 0
__trace_generic_params_func_061 <= USED 0
__trace_generic_params_func_062 <= USED 0
__trace_generic_params_func_063 <= USED 0
__trace_generic_params_func_064 <= USED 0
__trace_generic_params_func_065 <= USED 0
__trace_generic_params_func_066 <= USED 0
__trace_generic_params_func_067 <= USED 0
__trace_generic_params_func_068 <= USED 0
__trace_generic_params_func_069 <= USED 0
__trace_generic_params_func_070 <= USED 0
__trace_generic_params_func_071 <= USED 0
__trace_generic_params_func_072 <= USED 0
__trace_generic_params_func_073 <= USED 0
;FILE diag_API.o
diagStructPrintf_Empty <= USED 0
isDiagTracePermitted <= USED 0
setDiagTraceProhibited <= USED 0
;FILE diag_Utils.o
DiagAPI_GetTimeStamp <= USED 0
;FILE diag_buff.o
SetDiagTxMsgCounterPtr <= USED 0
updateStatsOnApps <= USED 0
;FILE diag_comm.o
diagCommEnforceTransmit <= USED 0
;FILE diag_comm_EXTif.o
CopyDiagDirectToRingbuf <= USED 0
DiagCommExtIfTxTask <= USED 0
DiagCommExtIfTxTimerCallback <= USED 0
DiagUsbCmdHISR <= USED 0
diagCommTransmitToExtIf <= USED 0
diagCommTransmitToRingBuf <= USED 0
diagExtIFStatusDiscNotify <= USED 0
diagExtIFstatusConnectNotify <= USED 0
handle_diag_command <= USED 0
;FILE diag_comm_EXTif_OSA_NUCLEUS.o
ActivateDiagThroughPutHisr <= USED 0
DiagCommExtIfRxTask <= USED 0
diagCommTransmitToUart1 <= USED 0
diagExtIfcon_discon <= USED 0
diagThrougPutHISR <= USED 0
diag_RxTransactionCompleted <= USED 0
diag_TxNoDMATransactionCompleted <= USED 0
diag_enable_next_rx_cmd <= USED 0
diag_os_TransmitToExtIf <= USED 0
diag_os_TransmitToExtIf1 <= USED 0
diag_os_TransmitToExtIfMulti <= USED 0
flush_sd_log <= USED 0
setExtUsbWayLock <= USED 0
uartHISRRx <= USED 0
uartHISRTx <= USED 0
uartRxLISRCB <= USED 0
uartTxLISRCB <= USED 0
;FILE diag_comm_FS.o
DiagCommFsMutexInit <= USED 0
diagCommFsInit <= USED 0
diagCommFsUninit <= USED 0
diagCommGetLogInfo <= USED 0
diagCommSaveLogToFs <= USED 0
diagCommSendLogToUart <= USED 0
;FILE diag_comm_INTif.o
;FILE diag_comm_L2.o
CalcLongMessageLength <= USED 0
diagCommL2ReceiveL1Packet <= USED 0
diagCommL2ResetRxStateMachine <= USED 0
diagCommSspL2TimerExpired <= USED 0
;FILE diag_comm_L4.o
HandleHandshakeSequence <= USED 0
SendHandshakeMessage <= USED 0
diagCommL4Init <= USED 0
diagCommL4ReceiveL2FullMessage <= USED 0
diagCommL4ReceiveL2Packet <= USED 0
diagL4InitHSprotocolWithCB <= USED 0
;FILE diag_comm_SD.o
DiagCommSDMutexInit <= USED 0
diag_comm_log_init <= USED 0
diag_online_sdl_enable <= USED 0
getSDMemcheckVal <= USED 0
setSDMemCheckVal <= USED 0
;FILE diag_comm_if.o
diagCommSetChunk <= USED 0
;FILE diag_header_handler.o
FindSoTOffset <= USED 0
GetMessageCoreIDForIntIf <= USED 0
GetMsgIDFromTxHeader <= USED 0
GetRxMsgSourceID <= USED 0
GetUsbMsgLen <= USED 0
IsC2CStats <= USED 0
;FILE diag_init.o
SetTraceBeFiltered <= USED 0
SetTraceNotFiltered <= USED 0
diagEnterBootLoaderCBFuncBind <= USED 0
diagICATReadyNotifyEventBind <= USED 0
diagPhase1Init <= USED 0
diagStubPsCheckOKfn <= USED 0
isTraceFiltered <= USED 0
;FILE diag_mem.o
DiagMemFreeInternalItem <= USED 0
rtdm_memory_pool_delete <= USED 0
;FILE diag_nvm.o
diagCfgFilter <= USED 0
diagCfgUartIsEnable <= USED 0
diagCfgUseHighUart <= USED 0
diagStartPsCfg <= USED 0
eeh_dump_output_dev_is_spi <= USED 0
eeh_dump_output_dev_set_spi <= USED 0
eeh_dump_output_dev_set_usb_sd <= USED 0
getDiagUartSpeed <= USED 0
get_diag_dbversion <= USED 0
sulog_output_dev_set_sd <= USED 0
sulog_output_dev_set_usb <= USED 0
;FILE diag_osif.o
diagBSPconfig <= USED 0
diagOSspecificPhase1Init <= USED 0
diagOSspecificPhase2Init <= USED 0
;FILE diag_port.o
DiagTxMsg <= USED 0
diag_fix <= USED 0
fix_command <= USED 0
;FILE diag_restore_fixups.o
;FILE diag_rx.o
IsExternalConnectCeased <= USED 0
ResumeDiagMsgs <= USED 0
UpdateExtIfConnectionStatus <= USED 0
diagGetLibsVersion <= USED 0
diagPreDefCMMInit <= USED 0
;FILE diag_rx_OSA_NUCLEUS.o
setDiagCmdServerStatus <= USED 0
;FILE diag_tx.o
SetDiagTxMsgFramNumberPtr <= USED 0
;FILE dial.o
at_over_uart_setcb <= USED 0
at_uart_resp_cb <= USED 0
;FILE dialer_task.o
Is_2Gmode <= USED 0
dialer_get_compare_auth_flag <= USED 0
dialer_get_deact_default_pdp_flag <= USED 0
dialer_get_deactive_pdp_context_flag <= USED 0
dialer_set_compare_auth_flag <= USED 0
dialer_set_deact_default_pdp_flag <= USED 0
dialer_set_deactive_pdp_context_flag <= USED 0
getCurrentConnectNum <= USED 0
get_auto_apn_match_info <= USED 0
get_auto_apn_name <= USED 0
set_cgdf_save_mode <= USED 0
set_dialer_connmode <= USED 0
set_force_apn_reattach <= USED 0
;FILE digest.o
digest_authenticate <= USED 0
;FILE diskio.o
ff_test <= USED 0
ff_test0 <= USED 0
test_diskio <= USED 0
;FILE div_32.o
;FILE div_s.o
;FILE division_operations.o
WebRtcSpl_DivU32U16 <= USED 0
WebRtcSpl_DivW32W16ResW16 <= USED 0
;FILE dlmwdcfg.o
;FILE dmnvmfile.o
;FILE dmnvminit.o
;FILE dmnvmlcl.o
dmNvMTest <= USED 0
sendDmNvPReadCalReq <= USED 0
;FILE dmnvmtask.o
DmNvMTask1 <= USED 0
;FILE dmnvptask.o
DmNvPHandleReadCalCnf <= USED 0
DmNvPNvramSelectCal <= USED 0
DmNvPTask1 <= USED 0
;FILE dmpmdown.o
;FILE dmpmup.o
DmPmStartUpFromCharging <= USED 0
;FILE dns.o
dns_clear_setflg <= USED 0
dns_get_DNSserv <= USED 0
dns_get_seqno <= USED 0
dns_get_ttl <= USED 0
dns_set_ttl <= USED 0
;FILE dns_relay.o
dnsr_check_serverip <= USED 0
dnsr_get_ip4_server1 <= USED 0
dnsr_get_ip4_server2 <= USED 0
webui_set_dns_server <= USED 0
;FILE download.o
fota_dump_buffer <= USED 0
fota_image_verify <= USED 0
fota_pkg_flush_flash <= USED 0
fota_pkg_write <= USED 0
get_fota_result <= USED 0
mini_fota_image_verify <= USED 0
mini_fota_pkg_write <= USED 0
test_fota_api <= USED 0
test_mini_fota_api <= USED 0
;FILE drat_coma_bsp_adap.o
dratComGetL1Version <= USED 0
;FILE drat_coma_power_adap.o
;FILE dsp_filters.o
;FILE dtc_api.o
dtcPostF8TransferReq <= USED 0
dtcPreF8TransferReq <= USED 0
dtcResetHw <= USED 0
dtcSetDtcResetAfterRequest <= USED 0
dtcTransferReq <= USED 0
;FILE dtc_spy.o
;FILE dtx_dec.o
;FILE dtx_enc.o
;FILE dump.o
_Z11dump_vectorRKN7android6VectorIjEEPKc <= USED 0
_Z23dump_frame_info_verbosebRK9MediaInfoPKcS3_ <= USED 0
;FILE duster_common.o
ConvertASCtoString <= USED 0
ConvertStrToChar <= USED 0
ConvertStrToInteger64 <= USED 0
ConvertV6IntegertoString <= USED 0
GetNTPServer <= USED 0
SaveNTPParameters <= USED 0
SimRemove_cb <= USED 0
clear_pdp_list <= USED 0
close_logical_channel <= USED 0
duster_at_tok_nextint <= USED 0
duster_at_tok_nextstr <= USED 0
duster_at_tok_start <= USED 0
duster_auto_select_netwrok <= USED 0
enableAutoTimeZone <= USED 0
getMncLen <= USED 0
get_iccid <= USED 0
get_imei <= USED 0
get_imsi <= USED 0
get_meid <= USED 0
get_ntp_time_type <= USED 0
get_qci_status <= USED 0
get_rx_byte <= USED 0
get_sys_mode <= USED 0
get_tx_byte <= USED 0
htonl <= USED 0
htons <= USED 0
isAutoTimeZone <= USED 0
is_mcc_mnc_rau <= USED 0
is_pdp_connected <= USED 0
is_uicc_sim <= USED 0
open_logical_channel <= USED 0
sendCmuxDataToBindChannel <= USED 0
sendUartDataToBindChannel <= USED 0
setUsbModemBindChannel <= USED 0
set_ntp_time_type <= USED 0
set_qci_status <= USED 0
set_stk_process_entity <= USED 0
sim_check_wrong_msg <= USED 0
wan_get_apn_iptype_change_type <= USED 0
;FILE duster_dongle.o
discard_event_msg <= USED 0
sim_plugout_event_cb <= USED 0
;FILE ebndcfg.o
EbndCombinationFddAndTddBands <= USED 0
EbndDoEutraBandsDlOverlap <= USED 0
EbndGetOffsetInBandForEarfcn <= USED 0
EbndIsEarfcnInStoredList <= USED 0
;FILE ec_gains.o
;FILE ecdh.o
;FILE ecdsa.o
mbedtls_ecdsa_genkey <= USED 0
mbedtls_ecdsa_sign <= USED 0
mbedtls_ecdsa_verify <= USED 0
;FILE ecp.o
mbedtls_ecp_curve_info_from_name <= USED 0
mbedtls_ecp_curve_list <= USED 0
mbedtls_ecp_gen_key <= USED 0
mbedtls_ecp_gen_keypair <= USED 0
mbedtls_ecp_gen_keypair_base <= USED 0
mbedtls_ecp_muladd <= USED 0
mbedtls_ecp_point_cmp <= USED 0
mbedtls_ecp_point_read_string <= USED 0
mbedtls_ecp_read_key <= USED 0
mbedtls_ecp_tls_read_group <= USED 0
;FILE ecp_curves.o
;FILE egi_bind.o
egiBindAfcDacSaveValue <= USED 0
egiBindBcchGsmDeactivateReq <= USED 0
egiBindCfgCorrectRssi <= USED 0
egiBindCopyGainTables <= USED 0
egiBindDebugGetGsmFrameNumber <= USED 0
egiBindEnableHslLogging <= USED 0
egiBindFillGsmMeasParams <= USED 0
egiBindFrGetReferenceTime <= USED 0
egiBindGSMInitStaticSharedMemory <= USED 0
egiBindGSMPowerup <= USED 0
egiBindGSMReset <= USED 0
egiBindGSMStartup <= USED 0
egiBindGSMTerminate <= USED 0
egiBindGetBcchInfo <= USED 0
egiBindGetFujitsuAFCValues <= USED 0
egiBindGetGainTablesSize <= USED 0
egiBindGetGsmTimingParams <= USED 0
egiBindGetNumOfGainTables <= USED 0
egiBindGetResumeFlag <= USED 0
egiBindGetSignedAfcDacVal <= USED 0
egiBindL1BgEmphDetectedCellMeasInd <= USED 0
egiBindL1BgEmphLteAbort <= USED 0
egiBindL1BgEmphLteDetAbort <= USED 0
egiBindL1BgEmphMeasRadioReq <= USED 0
egiBindLteL1aGetRspRxPowerUpParamsLteBand <= USED 0
egiBindReportLteFrameNumber <= USED 0
egiBindResetBcchInLteIdleStateMachine <= USED 0
egiBindResetBcchInfo <= USED 0
egiBindResetHoTo4GOnGoingFlag <= USED 0
egiBindResetUtranMeas <= USED 0
egiBindRetreiveLteBchParameters <= USED 0
egiBindRetreiveMphDeactivateCnf <= USED 0
egiBindSaveGsmAFCValues <= USED 0
egiBindSendLteBchDecodeResults <= USED 0
egiBindSetEventForBchDecodeTermination <= USED 0
egiBindSetEventForPldGsmCnf <= USED 0
egiBindSetLtevStartBcchForLte <= USED 0
egiBindSetLtevStartMultiBcchForLte <= USED 0
egiBindSetLtevUpdateMultiBcchForLte <= USED 0
egiBindSetReferenceTime <= USED 0
egiBindSetResumeFlag <= USED 0
egiDsdsBGPlmnSearchMultiBcchDecodeReqForLte <= USED 0
;FILE emaccf.o
;FILE emacdl.o
;FILE emacra.o
;FILE emacsig.o
KiLteL2SendFreqIntSignal <= USED 0
KiLteL2SendFreqSignal <= USED 0
;FILE emacul.o
LtePdcpSetLochData <= USED 0
;FILE emacut.o
LteMacGetL2Stat <= USED 0
LteMacSendEcphyFlashDspReq <= USED 0
LteMacutDumpUlDataFromIPC <= USED 0
LteMacutGsmVoiceCallInform <= USED 0
isBadQualityDetected <= USED 0
isTherePendingData <= USED 0
;FILE emm_errc.o
EmmConstructFakeExtendedServiceRequest <= USED 0
EmmEncodeDecode <= USED 0
EmmTriggerFakeExtendedServiceRequest <= USED 0
;FILE emm_esm.o
;FILE emm_mmr.o
EmmClearRejectCauseCodes <= USED 0
;FILE emm_psms.o
;FILE emm_rxmsg.o
EmmServiceAccept <= USED 0
;FILE emm_security.o
DerivateCkIkAtHandover <= USED 0
DerivateCkIkAtIdleMobility <= USED 0
DerivateGprsGsmKc <= USED 0
DerivateGsmKc <= USED 0
DerivateKasmeDuringHandover <= USED 0
DerivateNasToken <= USED 0
DerivateSrvccCkIkAtHandover <= USED 0
;FILE emm_sim.o
;FILE emm_timer.o
EmmImsServiceEndProtectionTimerExpiry <= USED 0
EmmStartT3423 <= USED 0
EmmStopAuthTimers <= USED 0
EmmT3448Expiry <= USED 0
;FILE emm_txmsg.o
;FILE emm_utils.o
EmmCheckIfMmrNregIndNeeded <= USED 0
EmmIsCurrentCardInNormalService <= USED 0
EmmReduceServiceType <= USED 0
EmmSendMmsiStoreLocationInfoReq <= USED 0
EmmServiceRejectReceived <= USED 0
EmmSimDeleteCipherKeyData <= USED 0
;FILE enc.o
smipEncodeMO3gpp2SMS <= USED 0
smipEncodeMO3gppSMS <= USED 0
;FILE enc_lag3.o
;FILE enc_lag6.o
;FILE entropy.o
mbedtls_entropy_gather <= USED 0
mbedtls_entropy_update_manual <= USED 0
;FILE epdcp.o
LteBlockAlloc <= USED 0
LteBlockRelease <= USED 0
LteDeleteUlBlockStub <= USED 0
tLtePdcpTask <= USED 0
;FILE epdcpcipherandintegrity.o
LteDtcCh0InfoInit <= USED 0
LteHisrConstructRlcDataListCh1 <= USED 0
LteHisrConstructRrcDataInd <= USED 0
LteHisrConstructRrcIntegrityCalcCnf <= USED 0
LteHisrConstructRrcIntegrityCheckErrorInd <= USED 0
LteHisrConstructSnDataInd <= USED 0
LteHisrConstructSnDataReq <= USED 0
LteHisrHandleSrbDataInd <= USED 0
LtePdcpEia <= USED 0
LtePdcpReUpdateSrbDecipherInfo <= USED 0
LtePdcpReverseMacI <= USED 0
LtePdcpSrbSoftEea <= USED 0
LtePdcpUpdataDeintegrityInfo <= USED 0
LtePdcpUpdateSrbDecipherInfo <= USED 0
;FILE epdcplinklist.o
LteTraceDataList <= USED 0
;FILE epdcprlcsap.o
LtePdcpAddSnDataIndLList <= USED 0
LtePdcpConstructIpDlDataIndNode <= USED 0
LtePdcpConstructIpDlDataIndNodeForLoop <= USED 0
LtePdcpConstructRrcIntegrityCheckErrorInd <= USED 0
LtePdcpConstructSnUlDataReqNode <= USED 0
LtePdcpMaciCompare <= USED 0
LtePdcpRlcTriggerPdcpReportInd <= USED 0
LtePdcpTriggerStatisticReportInd <= USED 0
;FILE epdcprrcsap.o
LtePdcpAddPduHeaderForRrcData <= USED 0
;FILE epdcpupsap.o
LtePdcpAddPduHeaderForUserData <= USED 0
LtePdcpHandleUlPdicDoneInd <= USED 0
LtePdcpSetUlPdcpDataLengthToZero <= USED 0
LtePdcpSnDataReq <= USED 0
LtePdcpSubUlPdcpDataLength <= USED 0
LtePdcpUlDiscardSnSdu <= USED 0
enablePdcpDiag <= USED 0
;FILE err.o
;FILE esmab.o
SendAbgpSmApnReadRsp <= USED 0
;FILE esmdevfeature.o
HandleEsmActDedEPSBearerContextReq <= USED 0
HandleEsmSnsmActivateRsp <= USED 0
;FILE esmemm.o
;FILE esmmain.o
;FILE esmreg.o
SendSmregPdpModInd <= USED 0
;FILE esmsn.o
;FILE esmtimer.o
;FILE esmutil.o
EsmComparePdpAddresses <= USED 0
EsmCpDataStatus <= USED 0
;FILE etharp.o
etharp_add_static_entry <= USED 0
etharp_find_addr <= USED 0
etharp_remove_static_entry <= USED 0
ethernet_copy_data_all <= USED 0
ethernet_copy_data_ref <= USED 0
ethernet_copy_data_ref2 <= USED 0
ethernet_ifinput_pbuf <= USED 0
ip_mac_stat_bytes <= USED 0
lwip_macaddr_ntoa <= USED 0
lwip_wan_delete_device <= USED 0
lwip_wan_delete_device_r <= USED 0
mac_header_validcheck <= USED 0
;FILE ethip6.o
;FILE ets_to_if2.o
;FILE ets_to_wmf.o
;FILE evs_payload.o
unpackEvsFrame <= USED 0
;FILE ex_ctrl.o
;FILE extract_h.o
;FILE extract_l.o
;FILE fatwk_psm.o
FDI_fread_psm <= USED 0
FDI_fseek_psm <= USED 0
FDI_fwrite_psm <= USED 0
PSM_EraseAll <= USED 0
PSM_GetDefaultPSMAddress <= USED 0
PSM_ReadFlash <= USED 0
PSM_WriteFlash <= USED 0
get_psm_data_addr <= USED 0
psm_read_flash <= USED 0
;FILE feedback_create.o
;FILE feedback_parse.o
;FILE ff.o
f_gets <= USED 0
f_printf <= USED 0
f_putc <= USED 0
f_puts <= USED 0
f_stat <= USED 0
f_truncate <= USED 0
f_utime <= USED 0
ff_sync_files <= USED 0
ffst_print_all_file <= USED 0
;FILE ff_dump_mem.o
;FILE ffsystem.o
;FILE ffunicode.o
;FILE fsm_app.o
fsmAppGetCurrentEvent <= USED 0
fsmAppProcessEvent <= USED 0
fsmAppRelease <= USED 0
fsmAppSetTraceInfo <= USED 0
fsmGetVersion <= USED 0
;FILE fstdio_wrap.o
WRAP_fclose <= USED 0
WRAP_fopen <= USED 0
WRAP_fprintf <= USED 0
WRAP_fread <= USED 0
WRAP_fwrite <= USED 0
fstdio_bindDefault <= USED 0
;FILE ftp_client.o
api_ftp_reset_init <= USED 0
;FILE g_adapt.o
;FILE g_code.o
;FILE g_pitch.o
;FILE gain_q.o
;FILE gba.o
;FILE gc_pred.o
;FILE gcm.o
;FILE gmed_n.o
;FILE gpenc_ie.o
GpEncR97MsRadAccCapb <= USED 0
GpEncR99MsRadAccCapb <= USED 0
GpLenFirstMrac <= USED 0
;FILE gplc_stub_ds3.o
ConfigRfContMux <= USED 0
DlBbcGetBbcMirrorState <= USED 0
DlBbcGetBbcWriteHistory <= USED 0
Get1920FFFlag <= USED 0
Get_Dcxo_Add_Config <= USED 0
GplcSpyGetBufferSize <= USED 0
L1FrSendGmphPktOpCtrlInd <= USED 0
L1GetFrameNumber <= USED 0
L1GetState <= USED 0
L1SqNbCanTurnSpeechOn <= USED 0
RfBindInit <= USED 0
dlEnableAudioInts <= USED 0
l1MnAllowSleep <= USED 0
plgBindGl1CipherDataCnf <= USED 0
plgBindGmphDlSbConfigCnf <= USED 0
plgBindGmphDlTbfConfigCnf <= USED 0
plgBindGmphDynTsReconfigCnf <= USED 0
plgBindGmphExtMeasCnf <= USED 0
plgBindGmphFxdTsReconfigCnf <= USED 0
plgBindGmphIntMeasInd <= USED 0
plgBindGmphPccchConfigCnf <= USED 0
plgBindGmphPdchReleaseCnf <= USED 0
plgBindGmphPrachCnf <= USED 0
plgBindGmphPtmEnterInd <= USED 0
plgBindGmphPtmExitInd <= USED 0
plgBindGmphPtmMeasInd <= USED 0
plgBindGmphUlDynTbfConfigCnf <= USED 0
plgBindGmphUlFxdAllocEndInd <= USED 0
plgBindGmphUlFxdTbfConfigCnf <= USED 0
plgBindGmphUlSbConfigCnf <= USED 0
plgBindGmphUnitDataInd <= USED 0
plgBindMacControlAckReq <= USED 0
plgBindMacDlConfigReq <= USED 0
plgBindMacDlFlushReq <= USED 0
plgBindMacDlStartInd <= USED 0
plgBindMacDlTimeslotAllocInd <= USED 0
plgBindMacL1Sequencer <= USED 0
plgBindMacRemoveUlSblockReq <= USED 0
plgBindMacRxDecodeErrorInd <= USED 0
plgBindMacRxRadioBlockInd <= USED 0
plgBindMacTxBlockInfoReq <= USED 0
plgBindMacTxBlockSentInd <= USED 0
plgBindMacTxDataBlocksPurgedInd <= USED 0
plgBindMacTxPayloadReq <= USED 0
plgBindMacUlConfigReq <= USED 0
plgBindMacUlFlushReq <= USED 0
plgBindMacUlSblockReq <= USED 0
plgBindMacUlStartInd <= USED 0
plgBindMacUlTimeslotAllocInd <= USED 0
plgBindMphBcchMeasInd <= USED 0
plgBindMphBchConfigCnf <= USED 0
plgBindMphBsicDecodeCnf <= USED 0
plgBindMphBsicDecodeInd <= USED 0
plgBindMphChanAssignmentCnf <= USED 0
plgBindMphChanAssignmentFailCnf <= USED 0
plgBindMphChannelModeCnf <= USED 0
plgBindMphCipherModeCnf <= USED 0
plgBindMphDeactivateCnf <= USED 0
plgBindMphDedicatedMeasInd <= USED 0
plgBindMphDownlinkSignalFailInd <= USED 0
plgBindMphErrorInd <= USED 0
plgBindMphExtMeasurementCnf <= USED 0
plgBindMphFrequencyChangeCnf <= USED 0
plgBindMphHandoverCnf <= USED 0
plgBindMphHandoverFailCnf <= USED 0
plgBindMphHandoverStartInd <= USED 0
plgBindMphIdleNcellMeasInd <= USED 0
plgBindMphIdleScellMeasInd <= USED 0
plgBindMphImmAssignmentCnf <= USED 0
plgBindMphMonitorPlmnCnf <= USED 0
plgBindMphMonitorPlmnInd <= USED 0
plgBindMphRadioLinkTimeoutInd <= USED 0
plgBindMphRandomAccessCnf <= USED 0
plgBindMphUnitDataInd <= USED 0
plgBindPhConnectInd <= USED 0
plgBindPhDataInd <= USED 0
plgBindPhReadyToSendInd <= USED 0
plgBindRtl1DownlinkDataInd <= USED 0
plgCalDevGsmBurstReq <= USED 0
plgCalDevGsmGainProgramReq <= USED 0
plgCalDevGsmRampScaleReq <= USED 0
plgCalDevGsmReq <= USED 0
plgCalDevGsmRssiReq <= USED 0
plgCalDevGsmSetAfcDacReq <= USED 0
plgCalDevGsmSetBandModeReq <= USED 0
plgCalDevRssiDbmResult <= USED 0
plgGl1CipherDataReq <= USED 0
plgGmphDlSbConfigReq <= USED 0
plgGmphDlTbfConfigReq <= USED 0
plgGmphDlTbfShutdownReq <= USED 0
plgGmphDynTsReconfigReq <= USED 0
plgGmphExtMeasReq <= USED 0
plgGmphFxdTsReconfigReq <= USED 0
plgGmphIntMeasReq <= USED 0
plgGmphNetCtrlMeasReq <= USED 0
plgGmphPbcchDecodeReq <= USED 0
plgGmphPccchConfigReq <= USED 0
plgGmphPdchReleaseReq <= USED 0
plgGmphPrachFormatReq <= USED 0
plgGmphPrachReq <= USED 0
plgGmphPwrCtrlConfigReq <= USED 0
plgGmphStopPrachReq <= USED 0
plgGmphTimingPowerReq <= USED 0
plgGmphUlDynTbfConfigReq <= USED 0
plgGmphUlFxdTbfConfigReq <= USED 0
plgGmphUlSbAbortReq <= USED 0
plgGmphUlSbConfigReq <= USED 0
plgGmphUlTbfShutdownReq <= USED 0
plgMphAbortNcellOpReq <= USED 0
plgMphBcchDecodeReq <= USED 0
plgMphBchConfigReq <= USED 0
plgMphBsicDecodeReq <= USED 0
plgMphCbchControlReq <= USED 0
plgMphChanAssignmentFailReq <= USED 0
plgMphChanAssignmentReq <= USED 0
plgMphChannelModeReq <= USED 0
plgMphCipherModeReq <= USED 0
plgMphClassmarkReq <= USED 0
plgMphDeactivateReq <= USED 0
plgMphExtMeasurementReq <= USED 0
plgMphFindBcchReq <= USED 0
plgMphFrequencyChangeReq <= USED 0
plgMphHandoverFailReq <= USED 0
plgMphHandoverReq <= USED 0
plgMphImmAssignmentReq <= USED 0
plgMphMeasureAllReq <= USED 0
plgMphMonitorPlmnReq <= USED 0
plgMphNcellMeasReq <= USED 0
plgMphNextBcchReq <= USED 0
plgMphPageModeReq <= USED 0
plgMphRadioLinkTimeoutReq <= USED 0
plgMphRandomAccessReq <= USED 0
plgMphServingCellBcchReq <= USED 0
plgMphStopRachReq <= USED 0
plgMphTimingAdvReq <= USED 0
plgPhDataReq <= USED 0
plgPhEmptyFrameInd <= USED 0
plgRtl1UplinkDataReq <= USED 0
;FILE gprsal.o
GPRSAL_GetCASIMS <= USED 0
GPRSAL_GetCAVIMS <= USED 0
GPRSAL_GetCREG <= USED 0
GPRSAL_GetCaps <= USED 0
GPRSAL_SetCREG <= USED 0
GRPSAL_GETBANDIND <= USED 0
;FILE guilin.o
GuilinDisableWDT <= USED 0
GuilinLcdBackLightLevelRecord <= USED 0
GuilinLcdBackLightStatusRecord <= USED 0
Guilin_Aditional_Workaround <= USED 0
Guilin_Ldo_6_set_2_8 <= USED 0
Guilin_VBUCK1_Set_FPWM <= USED 0
Guilin_VBUCK3_Set_FPWM <= USED 0
Guilin_VBUCK_Set_DVC_Enable <= USED 0
Guilin_VBUCK_Set_Skip_Mode <= USED 0
Guilin_VsimSleep_Disable <= USED 0
Guilin_VsimSleep_Enable <= USED 0
Guilin_miccoConfigUsimV <= USED 0
Guilin_miccoDisableUsimV <= USED 0
Guilin_miccoEnableUsimV <= USED 0
PM802_LcdBacklight_Level_Get <= USED 0
PM802_LcdBacklight_Status_Get <= USED 0
;FILE guilin_lite.o
GuilinLiteDisableWDT <= USED 0
GuilinLite_Aditional_Workaround <= USED 0
GuilinLite_Ldo_11_set <= USED 0
GuilinLite_Ldo_11_set_1_2 <= USED 0
GuilinLite_Ldo_7_set_1_2 <= USED 0
GuilinLite_VBUCK1_Set_FPWM <= USED 0
GuilinLite_VBUCK1_Set_Skip_Mode <= USED 0
GuilinLite_VBUCK_Set_DVC_Enable <= USED 0
GuilinLite_VBUCK_Set_Enable <= USED 0
GuilinLite_VBUCK_Set_Slpmode <= USED 0
;FILE hacomtsk.o
HaCommsTask1 <= USED 0
;FILE hadsimhd.o
hadGetCardNum <= USED 0
hadSetUiccTransmissionProtocolT0Only <= USED 0
;FILE hadsimmn.o
L1SimDriverTask1 <= USED 0
L1SimDriverTask21 <= USED 0
;FILE hafailcf.o
di_LockDisplay <= USED 0
;FILE hexdump.o
_ZN14streamingmedia7hexdumpEPKvjjPNS_7AStringE <= USED 0
;FILE hp_max.o
;FILE http_client_base64.o
;FILE httpcon.o
;FILE httpencdec.o
Http_GetParam <= USED 0
;FILE i2c.o
get_timestamp <= USED 0
i2c_bus_reset <= USED 0
;FILE icmp.o
;FILE icmp6.o
;FILE idletask.o
idleHookBind <= USED 0
idleInit <= USED 0
;FILE if2_to_ets.o
;FILE imsCapApi.o
;FILE imsConfEvent.o
;FILE imsMediaFsm.o
hdlCreateInRemoved <= USED 0
hdlRemovedInWFRnReady <= USED 0
imsRTPSession_SessionFeedback <= USED 0
;FILE imsSessApi.o
IMS_GetSessionInfo <= USED 0
IMS_SetReferToUser <= USED 0
;FILE imsSessConf.o
;FILE imsSessEarlyMedia.o
;FILE imsSessEvents.o
ims_FindSessEventDataSize <= USED 0
ims_SessHdlBitrateEvent <= USED 0
ims_SessPostMediaStartEvent <= USED 0
ims_SessPostRtpRtcpInactivityEvent <= USED 0
;FILE imsSessFsm.o
;FILE imsSessFsmModify.o
hdlOptionsStackInDialog <= USED 0
;FILE imsSessFsmOrig.o
hdlTerminateAppInResendInvite <= USED 0
;FILE imsSessFsmTerm.o
;FILE imsSessUtils.o
ims_EncodeMultiPartContent <= USED 0
ims_FreeBWInfoList <= USED 0
ims_FreeMediaStreamCapsMembers <= USED 0
ims_GetMediaIdFromMSRPId <= USED 0
ims_IsMediaDirUpgraded <= USED 0
ims_ParseCRList <= USED 0
ims_UpdateVideoCapsForDirChange <= USED 0
ims_findMediaIndexByMediaSessHd <= USED 0
;FILE imsUICC.o
;FILE imsUSSIFsmFunc.o
;FILE imsUssiXmlInterpreter.o
;FILE ims_Media.o
Media_AudioModeSet <= USED 0
Media_GetSessionCodecConfig <= USED 0
Media_GetSessionConfig <= USED 0
Media_LinkAVSessions <= USED 0
Media_SetSession_Vowifi <= USED 0
RetreiveVideoBandwidth <= USED 0
_ZN18MediaEventListenerC1EPv <= USED 0
media_DirIdToString <= USED 0
media_VideoDefaultLevelDimension <= USED 0
media_VideoPlatformId2String <= USED 0
;FILE ims_api.o
IMS_CallDivert <= USED 0
IMS_Modify <= USED 0
IMS_QueryCallCaps <= USED 0
IMS_QueryFirstAvailableNetwork <= USED 0
IMS_QueryFirstCall <= USED 0
IMS_QueryNextAvailableNetwork <= USED 0
IMS_QueryNextCall <= USED 0
IMS_Reject <= USED 0
IMS_ReloadConfig <= USED 0
IMS_SMS_CancelOperation <= USED 0
;FILE ims_api_psram.o
GetImsEnable <= USED 0
ims_set_at_channel <= USED 0
ims_set_cid <= USED 0
;FILE ims_main.o
;FILE ims_syssocket.o
SysSocket_Attach <= USED 0
SysSocket_GetIoInterface <= USED 0
SysSocket_GetNameInfo <= USED 0
SysSocket_GetPeerName <= USED 0
SysSocket_GetSockName <= USED 0
SysSocket_Inet_Pton2 <= USED 0
SysSocket_SetPartialStreamSupportFlag <= USED 0
;FILE ims_test_main.o
ASSERT_EQ_imp <= USED 0
ASSERT_NE_imp <= USED 0
;FILE ims_ua_ipsec.o
;FILE ims_uaclient.o
;FILE imsd.o
thread_main <= USED 0
;FILE inet6.o
;FILE inet_chksum.o
;FILE init.o
BBU_UART_Open <= USED 0
BBU_puthexd <= USED 0
app_start_4m <= USED 0
app_start_8m <= USED 0
initGPIO <= USED 0
setGPIO121_HIGH <= USED 0
setGPIO121_LOW <= USED 0
setGPIO122_HIGH <= USED 0
setGPIO122_LOW <= USED 0
setGPIO60_HIGH <= USED 0
setGPIO60_LOW <= USED 0
setGPIO79_HIGH <= USED 0
setGPIO79_LOW <= USED 0
setGPIO81_HIGH <= USED 0
setGPIO81_LOW <= USED 0
setGPIO82_HIGH <= USED 0
setGPIO82_LOW <= USED 0
setGPIO83_HIGH <= USED 0
setGPIO83_LOW <= USED 0
setGPIO84_HIGH <= USED 0
setGPIO84_LOW <= USED 0
;FILE initTaskUtil.o
DSPResetAck <= USED 0
InitHSITimers <= USED 0
PMIC8211workaroundForFirstLDOoperation <= USED 0
PeripheralInits <= USED 0
StartDiagThroughPutTimer <= USED 0
SyncTimerHandler <= USED 0
SyncTimerInit <= USED 0
TCMStartFITimer <= USED 0
TCMTestTimerHandle <= USED 0
check_sys_vbat <= USED 0
dumpTimerRegs <= USED 0
get_sys_vbat <= USED 0
hwAcs_IPC_REG__READ <= USED 0
sendTestIPCcommand <= USED 0
timer_monitor <= USED 0
;FILE initatcmdsvr.o
initATCmdSvr <= USED 0
initATCmdSvr_Thread <= USED 0
;FILE int_lpc.o
;FILE int_lsf.o
;FILE intc_memRetain.o
INTCEnableInterruptOutput <= USED 0
;FILE intc_xirq.o
GPIOClearUsedForInterrupt <= USED 0
GPIOInterruptClear <= USED 0
GPIOSetUsedForInterrupt <= USED 0
Get_ICU_INTC_STATUS_0 <= USED 0
Get_ICU_INTC_STATUS_1 <= USED 0
INTCBindVirtualInit <= USED 0
INTCConfigurationGet <= USED 0
INTCConfigurationGet_XIRQ <= USED 0
INTCConfigureSwi <= USED 0
INTCD2rmD2Register <= USED 0
INTCDisableAllInts <= USED 0
INTCISRGet <= USED 0
INTCISRGet_XIRQ <= USED 0
INTCIdleMaskDisable <= USED 0
INTCIdleMaskEnable <= USED 0
INTCInterruptHandlerFIQ_XIRQ <= USED 0
INTCInterruptHandlerIRQ <= USED 0
INTCPhase1Init <= USED 0
INTCPhase1Init_XIRQ <= USED 0
INTCSwIntCrossAssert <= USED 0
INTCXswiAssert <= USED 0
INTC_P_ICU_ConfigurationGet <= USED 0
INTC_P_ICU_Init <= USED 0
XIRQ_init <= USED 0
get_int_status_0 <= USED 0
get_int_status_1 <= USED 0
get_int_status_2 <= USED 0
;FILE inter_36.o
;FILE interval.o
rohc_interval_get_rfc5225_id_id_p <= USED 0
rohc_interval_get_rfc5225_msn_p <= USED 0
;FILE intf_api.o
IPSec_SetDefault <= USED 0
;FILE inv_sqrt.o
;FILE ip.o
;FILE ip4.o
ip_route <= USED 0
;FILE ip4_addr.o
ip4_addr_netmask_valid <= USED 0
;FILE ip6.o
ip6_route <= USED 0
ip6_route_opt <= USED 0
;FILE ip6_addr.o
ip6addr_print_withnote <= USED 0
;FILE ip6_frag.o
;FILE ip_filter.o
ip_filter_get_type <= USED 0
ip_filter_icmp_info <= USED 0
ip_filter_ip_layer <= USED 0
ip_filter_ip_port <= USED 0
ip_filter_mac_layer <= USED 0
ip_filter_packet <= USED 0
ip_filter_set_one <= USED 0
ip_filter_set_rules <= USED 0
ip_filter_tcp_info <= USED 0
ip_filter_udp_info <= USED 0
port_filter_set_flag <= USED 0
port_filter_set_rules <= USED 0
;FILE ip_frag.o
ip_frag <= USED 0
ip_frag_for_td <= USED 0
;FILE ip_nat.o
ip_port_fwd_rules <= USED 0
;FILE ip_numbers.o
rohc_get_ip_proto_descr <= USED 0
;FILE ip_recovery.o
ip_recovery_enable <= USED 0
lwip_ip_recovery_disable_resetps <= USED 0
lwip_ip_recovery_enable_resetps <= USED 0
;FILE ipcp.o
IpcpSendConfigReq <= USED 0
IpcpUpdateIpParams <= USED 0
IpcpUpdateIpParamsTest <= USED 0
;FILE ipnetbuf.o
ACIPC_PS_uplink_indication <= USED 0
IpNetLwipGetUlPacketPoll <= USED 0
ipnet_uplink_indication <= USED 0
ipnet_uplink_resume <= USED 0
lwip_uplink_ps_packet_send <= USED 0
usbnet_uplink_ps_list_construct <= USED 0
usbnet_uplink_ps_list_deconstruct_SIM <= USED 0
;FILE ippcDES_emb.o
CopyBlock24 <= USED 0
CopyBlock32 <= USED 0
CopyBlock8 <= USED 0
FillBlock24 <= USED 0
FillBlock32 <= USED 0
FillBlock8 <= USED 0
SetKey_DES <= USED 0
XorBlock <= USED 0
XorBlock24 <= USED 0
XorBlock32 <= USED 0
XorBlock8 <= USED 0
ippsDESBufferSize <= USED 0
ippsDESDecryptCBC <= USED 0
ippsDESDecryptCFB <= USED 0
ippsDESDecryptECB <= USED 0
ippsDESDecrypt_I <= USED 0
ippsDESEncryptCBC <= USED 0
ippsDESEncryptCFB <= USED 0
ippsDESEncryptECB <= USED 0
ippsDESEncrypt_I <= USED 0
ippsDESInit <= USED 0
ippsTDESDecryptCBC <= USED 0
ippsTDESDecryptCFB <= USED 0
ippsTDESDecryptECB <= USED 0
ippsTDESDecrypt_I <= USED 0
ippsTDESEncryptCBC <= USED 0
ippsTDESEncryptCFB <= USED 0
ippsTDESEncryptECB <= USED 0
ippsTDESEncrypt_I <= USED 0
;FILE ippcRIJ128.o
ippsRijndael128DecryptCFB <= USED 0
ippsRijndael128DecryptECB <= USED 0
ippsRijndael128EncryptCFB <= USED 0
ippsRijndael128EncryptECB <= USED 0
;FILE ippcRIJTools.o
;FILE ipsec.o
ipsec_in_sa <= USED 0
;FILE ipsec_aes.o
;FILE ipsec_ah.o
;FILE ipsec_des.o
;FILE ipsec_esp.o
ipsec_esp_decapsulate_tunel <= USED 0
;FILE ipsec_md5.o
;FILE ipsec_sa.o
ipsec_sad_flush <= USED 0
ipsec_sad_print <= USED 0
ipsec_sad_print_single <= USED 0
ipsec_spd_flush <= USED 0
ipsec_spd_print <= USED 0
ipsec_spd_print_single <= USED 0
ipsec_spd_release_dbs <= USED 0
;FILE ipsec_sha1.o
;FILE ipsec_util.o
ipsec_check_replay_window <= USED 0
ipsec_inet_addr <= USED 0
ipsec_inet_aton <= USED 0
ipsec_inet_ntoa <= USED 0
ipsec_print_ip <= USED 0
ipsec_update_replay_window <= USED 0
;FILE ipsecdev.o
ipsec_set_tunnel <= USED 0
ipsecdev_netlink_output <= USED 0
ipsecdev_service <= USED 0
;FILE jpeg_dma.o
jdma_cfg <= USED 0
jdma_clr_bit <= USED 0
jdma_regs_check <= USED 0
jdma_regs_dump <= USED 0
jdma_trigger <= USED 0
;FILE jpeg_enc_drv.o
JpegIntrHandler <= USED 0
JpgEncHeaderSetup <= USED 0
JpgEncHuffTabSetUp <= USED 0
JpgEncQMatTabSetUp <= USED 0
cam_jpu_encclose <= USED 0
cam_jpu_encfinish <= USED 0
cam_jpu_encopen <= USED 0
jpeg_enc_cfg <= USED 0
jpeg_quality_set <= USED 0
;FILE kinu.o
KiPoolBlockInUse <= USED 0
;FILE kiosfail.o
warnAssertPrint <= USED 0
warnCheckParam <= USED 0
;FILE kiosinit.o
KiOsSystemIsInitialised <= USED 0
;FILE kioslow.o
KiOsIsMemorySignal <= USED 0
;FILE kiosmem.o
KiOsAllocPollMemory <= USED 0
KiOsResizeMemory <= USED 0
;FILE kiosq.o
KiOsFlushBlockQueue <= USED 0
;FILE kiossem.o
KiOsIncIntSemaphore <= USED 0
KiOsPollSemaphore <= USED 0
;FILE kiossig.o
KiOsReceiveNonTraceSignal <= USED 0
KiOsRequestSignal <= USED 0
KiOsRequestZeroSignal <= USED 0
KiOsSendNonTraceIntSignal <= USED 0
KiOsSendSignalPoll <= USED 0
;FILE kiosstat.o
KiCheckStackOverFlow <= USED 0
KiSendDevAssertInd <= USED 0
KiSendDevCheckInd <= USED 0
KiSendDevFailInd <= USED 0
KiSendDumpMemReq <= USED 0
;FILE kiostask.o
KiGetErrnoAddr <= USED 0
KiOsIsSim1Task <= USED 0
;FILE kiostim.o
KiOsMaximumSleep <= USED 0
KiOsRestartTimer <= USED 0
KiOsTick <= USED 0
KiTimGetActiveListHeadAddr <= USED 0
KiTimGetCurrentTickTimeAddr <= USED 0
KiTimGetTimerArrayAddr <= USED 0
;FILE kiostti.o
KiOsGetTheFreeHeaderAddr <= USED 0
KiTtiFilterLogSignal <= USED 0
KiTtiGetFirstLoggedSignal <= USED 0
KiTtiGetNextLoggedSignal <= USED 0
KiTtiProcessReceivedSignal <= USED 0
;FILE kiprintf.o
_printf <= USED 0
vprintf <= USED 0
;FILE kmdynmem.o
KmMemoryCreatePool <= USED 0
KmMemoryFree <= USED 0
KmMemoryGet <= USED 0
KmMemoryGetFail <= USED 0
KmMemoryGetSize <= USED 0
KmMemoryReSize <= USED 0
KmMemoryWalk <= USED 0
KmMemoryWalkStart <= USED 0
;FILE l1a_cal.o
GetLtePmaxReductionFlag <= USED 0
L1aGetCalibrationModeState <= USED 0
L1aHandleAdditionalMprTableData <= USED 0
L1aHandleDcxoCalData <= USED 0
L1aHandleLteQtEtFreqAdjTableData <= USED 0
L1aHandleRfConfigTableData <= USED 0
L1aSendDefaultTableWithZero <= USED 0
L1aTempReadingCallback <= USED 0
RspGet4GCalibrationStatus <= USED 0
getTxPowerBackoffMode <= USED 0
;FILE l1a_drat.o
L1aCheckHasRxModeReport <= USED 0
L1aMultiIratL1GetRAT <= USED 0
L1aSetL1aDspDuringSleep <= USED 0
L1aSetSimGsmActivated <= USED 0
L1aSetSimWbActivated <= USED 0
plDratsendGsmMeasureParams <= USED 0
;FILE l1a_dsds.o
L1aDsdsHandleGsmEarlyWakUpReq <= USED 0
L1aDsdsInformOtherCardSuspendState <= USED 0
L1aHandleL1aHandoverToLteInd <= USED 0
L1aSendDsGsmDeactInd <= USED 0
L1aSendSchdGsmGapAbortReq <= USED 0
L1aSendSim2ActivateInd <= USED 0
L1aSendSim2DeactivateInd <= USED 0
gliDsdsBGPlmnSearchBcchDecodeCnf <= USED 0
gliDsdsGsm2LteSleepReq <= USED 0
gliDsdsGsmActivateInd <= USED 0
gliDsdsGsmActivateRsp <= USED 0
gliDsdsGsmDeactivateInd <= USED 0
gliDsdsGsmEarlyWakUpReq <= USED 0
gliDsdsGsmGapCanCelInd <= USED 0
gliDsdsGsmUrgentReq <= USED 0
gliDsdsRcvLtePchInGsmPsReq <= USED 0
gliDsdsResumeLteReq <= USED 0
gliDsdsSchdGsmGapFinishedInd <= USED 0
gliDsdsSchdGsmGapReq <= USED 0
gliDsdsSchdLteGapInd <= USED 0
gliDsdsSuspendLteReq <= USED 0
gliIratcurrentProcedure <= USED 0
teiReselectToLteReq <= USED 0
weiReselectToUtraFddReq <= USED 0
wliDsdsLteActivateRsp <= USED 0
wliDsdsLteInactivateRsp <= USED 0
wliDsdsLteIsSleepState <= USED 0
wliDsdsRcvLtePchInWbPsReq <= USED 0
wliDsdsResumeLteReq <= USED 0
wliDsdsSchdLteGapInd <= USED 0
wliDsdsSchdWbGapAbortCnf <= USED 0
wliDsdsSchdWbGapCanCelInd <= USED 0
wliDsdsSchdWbGapFinishedInd <= USED 0
wliDsdsSchdWbGapReq <= USED 0
wliDsdsSuspendLteReq <= USED 0
wliDsdsWbActivateInd <= USED 0
wliDsdsWbDeactivateInd <= USED 0
wliDsdsWbEarlyWakeUpReq <= USED 0
wliDsdsWbSleepReq <= USED 0
;FILE l1a_dsds_lgi_stub.o
lgiDsds2GPlmnInLteStart <= USED 0
lgiDsdsBGPlmnSearchBcchDecodeReq <= USED 0
lgiDsdsDisableGsmSleep <= USED 0
lgiDsdsGsmActivateInd <= USED 0
lgiDsdsGsmActivateRsp <= USED 0
lgiDsdsGsmDeactivateRsp <= USED 0
lgiDsdsLteDrxReq <= USED 0
lgiDsdsRelatchLteReq <= USED 0
lgiDsdsSchdGsmGapInd <= USED 0
lgiDsdsSchdLteGapCancelInd <= USED 0
lgiDsdsSchdLteGapFinishedInd <= USED 0
lgiDsdsSchdLteGapReq <= USED 0
lgiDsdsSchdSetGsmSuspendState <= USED 0
;FILE l1a_dsds_lwi_stub.o
lwiDsdsLteActivateInd <= USED 0
lwiDsdsLteInactivateInd <= USED 0
lwiDsdsSchdLteGapCancelInd <= USED 0
lwiDsdsSchdLteGapFinishInd <= USED 0
lwiDsdsSchdLteGapReq <= USED 0
lwiDsdsSchdSetWbSuspendState <= USED 0
lwiDsdsSchdWbGapAbortReq <= USED 0
lwiDsdsSchdWbGapInd <= USED 0
lwiDsdsWbActivateRsp <= USED 0
lwiDsdsWbDeactivateRsp <= USED 0
;FILE l1a_main.o
L1aGetIpcShareMemoryBaseAddress <= USED 0
L1aHandleTimerExpiryL1AssertDebugInd <= USED 0
L1aSetLteCalQtEtFreqAdjTableDataShareMemoryAddress <= USED 0
L1aSetLteCalRfConfigTableDataShareMemoryAddress <= USED 0
;FILE l1a_meas.o
L1aAdaptForEcphyInterFreqInfoReq <= USED 0
L1aAdaptForEcphyIntraFreqInfoReq <= USED 0
L1aAdaptForEcphyMonitorInterFreqCellReq <= USED 0
;FILE l1a_phy.o
L1aAdaptForEcphyHandoverReq <= USED 0
L1aAdaptForEcphyRlCommonConfigReq <= USED 0
L1aAdaptForEcphyRlCommonSib1ConfigReq <= USED 0
L1aAdaptForEcphyRlDedicatedConfigReq <= USED 0
L1aCheckIfDirectSendDeactiveReq <= USED 0
L1aGetCpTimerSleepInd <= USED 0
L1aHandleLteSleepEndInd <= USED 0
L1aNormalDeactivate <= USED 0
L1aSendEcphyFlashDspReq <= USED 0
etiHandoverToLteFailReq <= USED 0
etiReselectToLteFailReq <= USED 0
etiReselectToTddUtraCnf <= USED 0
ewiHandoverToLteFailReq <= USED 0
;FILE l1a_response.o
L1aHandleEcphyL1AssertDebugInd <= USED 0
L1aHandleLteBackupL1DataInd <= USED 0
PlatToL1aCommonIpcReq <= USED 0
;FILE l1a_sys.o
L1aAdaptForEcphyClassmarkReq <= USED 0
L1aCalcTimestampOutRemain <= USED 0
L1aCheckMacResetBufferNeeded <= USED 0
L1aCheckShareMemoryOverlay <= USED 0
L1aGetCurrentServingEuarfcn <= USED 0
L1aIratIntermediateState <= USED 0
L1aIsInConnectedStatus <= USED 0
L1aMemCpyInt16 <= USED 0
L1aMemCpyInt32 <= USED 0
;FILE l1a_task.o
LteL1aTask1 <= USED 0
;FILE l1a_task2.o
LteL1aTask21 <= USED 0
;FILE l1cellularPowerApplication.o
CPACheckNeedSendIPCDirectly <= USED 0
CPAStateChangeDlLteFromLteActive <= USED 0
CPAStateChangeDlLteFromLteDrx <= USED 0
CPAStateChangeDlLteFromLteOos <= USED 0
CPAStateChangeDlLteFromNoRat <= USED 0
CPAStateChangeDualLinkLTE <= USED 0
CPAStateChangeLGOosToWOos <= USED 0
CPAStateChangeLWOosToGsmOos <= USED 0
CellularPowerAppExitFromOosDualLinkLTE <= USED 0
CellularPowerAppGetCpaState <= USED 0
CellularPowerAppGoToOosDualLinkLTE <= USED 0
CellularPowerAppIsOOSState <= USED 0
L1AEnableD2EntryDualLink <= USED 0
L1CGetCPAState <= USED 0
L1cCellularPowerAppDisableD2 <= USED 0
L1cCellularPowerAppEnableD2 <= USED 0
LtePowerOffInDualLink <= USED 0
LtePowerOnInDualLink <= USED 0
UMTSResetStickBit <= USED 0
l1GsmCheckDisableD2inSetGsmSimb <= USED 0
l1GsmCheckEnableD2inSetGsmSimb <= USED 0
l1GsmRebindTCUinSetGsmSimb <= USED 0
;FILE l3dec_ie.o
DecodeAlertingPatternIE <= USED 0
DecodeAuxiliaryStatesIE <= USED 0
DecodeBackupBearerCapabilityIE <= USED 0
DecodeBackupBearerCapabilityIEBody <= USED 0
DecodeBearerCapabilityIE <= USED 0
DecodeCallStateIE <= USED 0
DecodeCalledPartyBCDNumIE <= USED 0
DecodeCalledPartySubaddressIE <= USED 0
DecodeCallingPartyBCDNumIE <= USED 0
DecodeCallingPartySubaddressIE <= USED 0
DecodeCauseIE <= USED 0
DecodeCauseOfNoCliIE <= USED 0
DecodeCipheringKeySeqNumIE <= USED 0
DecodeCongestionLevelIE <= USED 0
DecodeConnectedNumberIE <= USED 0
DecodeConnectedSubaddressIE <= USED 0
DecodeHighLayerCompatibilityIE <= USED 0
DecodeIdTypeIE <= USED 0
DecodeKeypadFacilityIE <= USED 0
DecodeLSAIdentityIE <= USED 0
DecodeLowLayerCompatibilityIE <= USED 0
DecodeMmTimer <= USED 0
DecodeNetworkCallControlCapabilitiesIE <= USED 0
DecodeNotificationIndicatorIE <= USED 0
DecodePriorityLevelIE <= USED 0
DecodeProgressIndicatorIE <= USED 0
DecodeRedirPartyBCDNumIE <= USED 0
DecodeRedirPartySubaddressIE <= USED 0
DecodeRejectCauseIE <= USED 0
DecodeRepeatIndicatorIE <= USED 0
DecodeShiftIE <= USED 0
DecodeSignalIE <= USED 0
DecodeSmallUserUserIE <= USED 0
DecodeSubaddressIE <= USED 0
DecodeUserUserIE <= USED 0
;FILE l3decgm.o
DecodeBitRateExtended <= USED 0
DecodeEgprsModeFlagIE <= USED 0
DecodeModeFlagIE <= USED 0
DecodePduDescriptionIE <= USED 0
L3DecodeGprsMessage <= USED 0
;FILE l3declow.o
AssertFixedContentLength <= USED 0
AssertFixedIELength <= USED 0
AssertValue <= USED 0
DecodeFrameNumber <= USED 0
DecodeFreqListRangeInfo <= USED 0
Extract3ByteBitField <= USED 0
ExtractFreqListInfoFields <= USED 0
ExtractNibble <= USED 0
GpExtractBit <= USED 0
GpExtractBitField16 <= USED 0
GpExtractBitField32 <= USED 0
GpExtractBitField8 <= USED 0
GpExtractRestOctetBit <= USED 0
;FILE l3declte.o
DecodeCsfbResponseIE <= USED 0
DecodeEmmExServiceTypeIE <= USED 0
DecodeEpsAttachTypeIE <= USED 0
DecodeEpsAuthParamResIE <= USED 0
DecodeEpsMoDetachTypeIE <= USED 0
DecodeEpsUpdateTypeIE <= USED 0
DecodeHcConfigStatusIE <= USED 0
DecodeKsiAndSnIE <= USED 0
DecodeSpareHalfOctetIE <= USED 0
DecodeSupportedCodecsListIE <= USED 0
DecodeTrackingAreaIdentityIE <= USED 0
DecodeUeNetworkCapabilityIE <= USED 0
DecodeUeRadioCapabilityInfoUpdateNeededIE <= USED 0
;FILE l3decmn.o
;FILE l3enc_ie.o
EncodeAdditionalUpdateParametersIE <= USED 0
EncodeAuthParamSresExtIE <= USED 0
EncodeAuthParamSresIE <= USED 0
EncodeAuxiliaryStatesIE <= USED 0
EncodeCMServiceTypeIE <= USED 0
EncodeCallStateIE <= USED 0
EncodeCalledPartyBCDNumIE <= USED 0
EncodeCalledPartySubaddressIE <= USED 0
EncodeCallingPartyBCDNumIE <= USED 0
EncodeCallingPartySubaddressIE <= USED 0
EncodeCcCapabilitiesIE <= USED 0
EncodeClirInvocationIE <= USED 0
EncodeClirSuppressionIE <= USED 0
EncodeCongestionLevelIE <= USED 0
EncodeConnectedSubaddressIE <= USED 0
EncodeEmergencyServiceCategoryIE <= USED 0
EncodeHighLayerCompatibilityIE <= USED 0
EncodeImmediateModificationIndicatorIE <= USED 0
EncodeKeypadFacilityIE <= USED 0
EncodeLaiWith3DigitMncIE <= USED 0
EncodeLocationUpdatingTypeIE <= USED 0
EncodeLowLayerCompatibilityIE <= USED 0
EncodeMoreDataIE <= USED 0
EncodeNotificationIndicatorIE <= USED 0
EncodePriorityIE <= USED 0
EncodeRejectCauseIE <= USED 0
EncodeRepeatIndicatorIE <= USED 0
EncodeReverseCallSetupDirectionIE <= USED 0
EncodeSmallUserUserIE <= USED 0
EncodeStreamIdentifierIE <= USED 0
L3EncodeHeader <= USED 0
;FILE l3encgm.o
EncodeGprsTimer2IE <= USED 0
EncodeGprsTimerIE <= USED 0
EncodePdpAddressIE <= USED 0
EncodePtmsiSignature2IE <= USED 0
EncodeR99MsRadAccCapbIE <= USED 0
EncodeSmCauseIE <= USED 0
L3EncodeGprsMessage <= USED 0
cancelRel5QoS <= USED 0
;FILE l3enclte.o
EncodeAPNAMaxBitRateIE <= USED 0
EncodeAdditionalUpdateResultIE <= USED 0
EncodeCliIE <= USED 0
EncodeEmergencyNumberListIE <= USED 0
EncodeEpsAttachResultIE <= USED 0
EncodeEpsNetworkFeatureSupportIE <= USED 0
EncodeEpsUpdateResultIE <= USED 0
EncodeEsmDevicePropertiesIE <= USED 0
EncodeLcsClientIdentityIE <= USED 0
EncodeLcsIndicatorIE <= USED 0
EncodeNasSecurityAlgorithmsIE <= USED 0
EncodePagingIdentityIE <= USED 0
EncodeSpareHalfOctetIE <= USED 0
EncodeSsCodeIE <= USED 0
EncodeTaiListIE <= USED 0
EncodeUeSecurityCapabilityIE <= USED 0
;FILE l3encode.o
EncodeCMReestablishmentRequest <= USED 0
EncodeCMServiceRequest <= USED 0
EncodeImsiDetachIndication <= USED 0
EncodeLocationUpdatingRequest <= USED 0
EncodeMmMessage <= USED 0
GpEncodeOptField7 <= USED 0
GpEncodeOptField8 <= USED 0
L3EncodeAnyMessage <= USED 0
L3EncodeMessage <= USED 0
;FILE l_abs.o
;FILE l_comp.o
;FILE l_deposit_h.o
;FILE l_deposit_l.o
;FILE l_extract.o
;FILE l_negate.o
;FILE l_shr_r.o
;FILE lag_wind.o
;FILE lcp.o
LcpKickoffEchoRequest <= USED 0
LcpSendEchoReq <= USED 0
lcpHandleEchoReqTimeout <= USED 0
lcpSendNextEchoReq <= USED 0
;FILE levinson.o
;FILE lfs.o
lfs_dir_rewind <= USED 0
lfs_dir_seek <= USED 0
lfs_dir_tell <= USED 0
lfs_file_rewind <= USED 0
lfs_file_sync <= USED 0
lfs_file_truncate <= USED 0
lfs_getattr <= USED 0
lfs_removeattr <= USED 0
lfs_setattr <= USED 0
lfs_stat <= USED 0
lfs_unmount <= USED 0
;FILE lfs_api.o
lfs_io_dir_make <= USED 0
lfs_io_eof <= USED 0
lfs_io_stat <= USED 0
lfs_to_fatstat <= USED 0
;FILE lfs_cache.o
LfsMsgQRemove <= USED 0
lfs_cache_task_resume <= USED 0
lfs_cache_task_suspend <= USED 0
;FILE lfs_util.o
;FILE libhttpclient.o
;FILE loadTable.o
StrtupIsPowerup <= USED 0
commImageTableInit <= USED 0
getAppComShareMemAddr <= USED 0
getAppCom_RDATA_Bak_MemAddr <= USED 0
getCommImageBaseAddr <= USED 0
getCommNumOfLife <= USED 0
get_apn_begin_address <= USED 0
get_apn_bin_size <= USED 0
get_apn_end_address <= USED 0
get_apn_ex_begin_address <= USED 0
get_apn_ex_bin_size <= USED 0
get_apn_ex_end_address <= USED 0
get_btbin_begin_address <= USED 0
get_btbin_bin_size <= USED 0
get_btbin_end_address <= USED 0
get_btlst_begin_address <= USED 0
get_btlst_bin_size <= USED 0
get_btlst_end_address <= USED 0
get_cp_binary_size <= USED 0
get_ddr_heap_size <= USED 0
get_dsp_backup_addr <= USED 0
get_dsp_backup_size <= USED 0
get_dsp_bin_size <= USED 0
get_dsp_copy_size <= USED 0
get_dsp_partition_size <= USED 0
get_dsp_start_address <= USED 0
get_factory_a_bin_size <= USED 0
get_factory_b_end_address <= USED 0
get_factory_b_start_address <= USED 0
get_gpsfw_begin_addr <= USED 0
get_gpsfw_bin_size <= USED 0
get_gpspvt_begin_addr <= USED 0
get_gpspvt_bin_size <= USED 0
get_legabin_begin_address <= USED 0
get_legabin_end_address <= USED 0
get_mmipool_end_address <= USED 0
get_mmipool_start_address <= USED 0
get_nvm_bin_size <= USED 0
get_reserved_end_address <= USED 0
get_reserved_start_address <= USED 0
get_rf_bin_size <= USED 0
get_rf_load_addr <= USED 0
get_rf_partition_size <= USED 0
get_rf_start_address <= USED 0
get_system_end_address <= USED 0
get_system_start_address <= USED 0
get_updater_backup_end_address <= USED 0
get_updater_backup_start_address <= USED 0
get_updater_copy_size <= USED 0
get_updater_end_address <= USED 0
incrementCommNumOfLife <= USED 0
whether_RW_region_compressed <= USED 0
;FILE log.o
_log_output_ICAT <= USED 0
_log_output_ICAT_ln <= USED 0
_log_output_empty <= USED 0
_log_output_uart <= USED 0
log_dump <= USED 0
log_flush <= USED 0
log_get_module_level <= USED 0
log_init <= USED 0
log_module_get <= USED 0
log_set_trace_level <= USED 0
;FILE log2.o
;FILE log2_norm.o
;FILE lowtasks.o
LowEventUnBind <= USED 0
;FILE lpc.o
;FILE lrm.o
Lrm_CheckLocationInfoValid <= USED 0
Lrm_GetPosBaseInfo <= USED 0
Lrm_RegisterLocationMonitor <= USED 0
Lrm_UnRegisterLocationMonitor <= USED 0
Lrm_UpdateLocationInfo <= USED 0
;FILE lrm_gml.o
_gml_format_pidf_lo_circle <= USED 0
_gml_format_pidf_lo_ellipse <= USED 0
_gml_format_pidf_lo_ellipsoid <= USED 0
_gml_format_pidf_lo_sphere <= USED 0
lrmGml_Destroy <= USED 0
lrmGml_Init <= USED 0
;FILE lsfwt.o
;FILE lsp.o
;FILE lsp_avg.o
;FILE lsp_az.o
_Z19Get_lsp_pol_wrapperPsPiS0_ <= USED 0
;FILE lsp_lsf.o
;FILE ltebm.o
LteBmHandleUlUmDataHarqCnf <= USED 0
LteBmProcessDataAbortInd <= USED 0
LteIPCReleaseRxSBBlock <= USED 0
LteMacKeepReservedRxBlock <= USED 0
LteMacSFAllocateRxL2BBlock <= USED 0
LteMacSfSendIPCDlBlockMove <= USED 0
UMTSAllocateTxL2Buffer <= USED 0
UMTSMacAllocateL1Buffer <= USED 0
UMTSMacAllocateL2RxBuffer <= USED 0
UMTSMacReleaseL1Buffer <= USED 0
UMTSMacSetL1BufferNum <= USED 0
UMTSReleaseTxL2Buffer <= USED 0
UMTSRlcReleaseL2RxBuffer <= USED 0
;FILE ltenetworkcard.o
LteUsbDataTxReq <= USED 0
checksum <= USED 0
usbNetTaskInit <= USED 0
usbRxEnable <= USED 0
usbRxUnBlocked <= USED 0
;FILE lterabmgmm.o
;FILE lterabmisc.o
isLteInLoopbackMode <= USED 0
;FILE lterabmmain.o
;FILE lterabmpdp.o
LteRabmReleaseSnDataList <= USED 0
LteRabmSendSignalToPdp <= USED 0
LteRabmSnLteSnDataRsp <= USED 0
RabmProcessWaitToReestExpireInd <= USED 0
checkRabmPendingData <= USED 0
;FILE lterabmrrc.o
LteRabmRabmRrcEstablishRej <= USED 0
LteRabmRabmRrcEstablishRes <= USED 0
LteRabmRabmRrcReleaseRes <= USED 0
;FILE lterabmsm.o
LteRabmLoopBackProcSmDataInd <= USED 0
LteRabmSnSmDeactivateReq <= USED 0
;FILE lterabmtimers.o
LteRabmSendWatchReportInd <= USED 0
LteRabmStartWaitToReestTimer <= USED 0
LteRabmStopAllLteRabmTimers <= USED 0
LteRabmStopWaitToReestTimer <= USED 0
;FILE lterabmutil.o
LteRabmFillStatistics <= USED 0
LteRabmReleaseEpsCommon <= USED 0
LteRabmReleaseLtePdcpSduList <= USED 0
LteRabmReleaseSnDataReq <= USED 0
LteRabmReplacePacketFilter <= USED 0
;FILE lterlc.o
LteMacDataInd <= USED 0
LteMacDataReq <= USED 0
LteRlcGetUplaneData <= USED 0
LteRlcProcessPdcpDiscardReq <= USED 0
LteRlcUMTransmitIndSetData <= USED 0
lteRlcHeaderMinLen <= USED 0
;FILE lterlcam.o
LteDiscardUlPacketsDueToHwm <= USED 0
LteRlcMemoryCheck <= USED 0
;FILE lterlccommon.o
ErlcPrintf <= USED 0
LteRlcGetIdxFromSn <= USED 0
LteRlcGetSnFromIdx <= USED 0
LteRlcSetHeader <= USED 0
;FILE lterlcum.o
LteUmRlcAddPdcpData <= USED 0
LteUmRlcProcessPdcpDiscardReq <= USED 0
;FILE lterrc_decb_r1551.o
PerDec_EuAMF_Identifier_r15 <= USED 0
PerDec_EuAUL_Config_r15_setup_aul_Subframes_r15_str <= USED 0
PerDec_EuAbsoluteTimeInfo_r10_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm4_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm8_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm9and10_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n8TxAntenna_tm9and10_r15_str <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13_messageClassExtension_str <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_MBMS_r14_messageClassExtension_str <= USED 0
PerDec_EuCSFBParametersResponseCDMA2000_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuCSFBParametersResponseCDMA2000_v8a0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuCSG_Identity <= USED 0
PerDec_EuC_RNTI <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalId1XRTT_str <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalIdHRPD_str <= USED 0
PerDec_EuCellIdentity <= USED 0
PerDec_EuDL_CCCH_MessageType_messageClassExtension_messageClassExtensionFuture_r15_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig0_r11_str <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig6_r11_str <= USED 0
PerDec_EuIP_Address_r13_ipv6_r13_str <= USED 0
PerDec_EuInitialUE_Identity_5GC_ng_5G_S_TMSI_Part1_str <= USED 0
PerDec_EuInitialUE_Identity_5GC_randomValue_str <= USED 0
PerDec_EuInitialUE_Identity_randomValue_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMBMSCountingRequest_r10_nonCriticalExtension_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v1430_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMMEC <= USED 0
PerDec_EuMobilityFromEUTRACommand_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v8d0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuNeighCellConfig <= USED 0
PerDec_EuP_C_AndCBSR_r15_codebookSubsetRestriction4_r15_str <= USED 0
PerDec_EuParametersCDMA2000_r11_parameters1XRTT_r11_longCodeState1XRTT_r11_str <= USED 0
PerDec_EuPosSystemInformation_r15_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRAND_CDMA2000 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v13c0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCEarlyDataComplete_r15_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCEarlyDataComplete_r15_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuRRC_InactiveConfig_r15_nonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_BR_r14_nonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_v1340_nonCriticalExtension_str <= USED 0
PerDec_EuSPDCCH_Elements_r15_resourceBlockAssignment_r15_resourceBlockAssignment_r15_str <= USED 0
PerDec_EuSSB_ToMeasure_r15_longBitmap_r15_str <= USED 0
PerDec_EuSS_RSSI_Measurement_r15_measurementSlots_r15_str <= USED 0
PerDec_EuShortI_RNTI_r15 <= USED 0
PerDec_EuShortMAC_I <= USED 0
PerDec_EuSubframeBitmapSL_r12_bs40_r12_str <= USED 0
PerDec_EuSubframeBitmapSL_r12_bs42_r12_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs100_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs40_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs50_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs60_r14_str <= USED 0
PerDec_EuSupportedBandwidthCombinationSet_r10_str <= USED 0
PerDec_EuSystemInformationBlockType1_MBMS_r14_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType2_v13c0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType3_v10l0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType8_parameters1XRTT_longCodeState1XRTT_str <= USED 0
PerDec_EuSystemTimeInfoCDMA2000_cdma_SystemTime_asynchronousSystemTime_str <= USED 0
PerDec_EuSystemTimeInfoCDMA2000_cdma_SystemTime_synchronousSystemTime_str <= USED 0
PerDec_EuTrackingAreaCode <= USED 0
PerDec_EuUECapabilityEnquiry_v1550_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuUEInformationRequest_r9_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuUEInformationRequest_v1530_IEs_nonCriticalExtension_str <= USED 0
;FILE lterrc_decc_r1551.o
PerDec_EuAUL_Config_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction <= USED 0
PerDec_EuAntennaInfoDedicated_v1530 <= USED 0
PerDec_EuAntennaInfoDedicated_v1530_setup <= USED 0
PerDec_EuAreaConfiguration_r10 <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13 <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13_c1 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_triggers_r14 <= USED 0
PerDec_EuCSI_RS_ConfigZP_ApList_r14 <= USED 0
PerDec_EuCellGlobalIdCDMA2000 <= USED 0
PerDec_EuCellIdentity_5GC_r15 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12_drb_Type_r12 <= USED 0
PerDec_EuDelayBudgetReport_r14 <= USED 0
PerDec_EuEnable256QAM_r14 <= USED 0
PerDec_EuEnable256QAM_r14_setup <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_c1 <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_criticalExtensions <= USED 0
PerDec_EuIDC_SubframePattern_r11 <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11 <= USED 0
PerDec_EuIP_Address_r13 <= USED 0
PerDec_EuInitialUE_Identity <= USED 0
PerDec_EuInitialUE_Identity_5GC <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_c1 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_criticalExtensions <= USED 0
PerDec_EuMeasConfig_heightThreshRef_r15 <= USED 0
PerDec_EuOtherConfig_r9_delayBudgetReportingConfig_r14 <= USED 0
PerDec_EuOtherConfig_r9_measConfigAppLayer_r15 <= USED 0
PerDec_EuOtherConfig_r9_overheatingAssistanceConfig_r14 <= USED 0
PerDec_EuOtherConfig_r9_rlm_ReportConfig_r14 <= USED 0
PerDec_EuPLMN_IdentityInfo2_r12 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_plmn_Identity_5GC_r15 <= USED 0
PerDec_EuPMCH_Config_r12_dataMCS_r12 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_n1PUCCH_AN_CS_v13c0 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530_uci_OnPUSCH_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_ce_PUSCH_FlexibleStartPRB_AllocConfig_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_ce_PUSCH_SubPRB_Config_r15 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14 <= USED 0
PerDec_EuParametersCDMA2000_r11_systemTimeInfo_r11 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0_pucch_SCell_v13c0 <= USED 0
PerDec_EuRACH_Skip_r14_targetTA_r14 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10_tdd <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10_type01_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10_type2_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_subframeConfigPattern_r10 <= USED 0
PerDec_EuSCG_Configuration_r12 <= USED 0
PerDec_EuSCG_Configuration_v12f0 <= USED 0
PerDec_EuSCG_Configuration_v13c0 <= USED 0
PerDec_EuSIB8_PerPLMN_r11_parametersCDMA2000_r11 <= USED 0
PerDec_EuSI_OrPSI_GERAN <= USED 0
PerDec_EuSSB_ToMeasure_r15 <= USED 0
PerDec_EuS_NSSAI_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_handoverType_v1530 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_crs_IntfMitigConfig_r15 <= USED 0
PerDec_EuSystemTimeInfoCDMA2000_cdma_SystemTime <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14 <= USED 0
PerDec_EuTMGI_r9_plmn_Id_r9 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15_uac_AC_BarringListType_r15 <= USED 0
PerDec_EuUEInformationRequest_r9_c1 <= USED 0
PerDec_EuUEInformationRequest_r9_criticalExtensions <= USED 0
;FILE lterrc_dece_r1551.o
PerDec_EuAUL_Config_r15_aul_RetransmissionTimer_r15 <= USED 0
PerDec_EuAUL_Config_r15_aul_StartingPartialBW_InsideMCOT_r15 <= USED 0
PerDec_EuAUL_Config_r15_aul_StartingPartialBW_OutsideMCOT_r15 <= USED 0
PerDec_EuAUL_Config_r15_contentionWindowSizeTimer_r15 <= USED 0
PerDec_EuAUL_Config_r15_transmissionModeUL_AUL_r15 <= USED 0
PerDec_EuAccessStratumRelease <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_maxLayersMIMO_STTI_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_transmissionModeDL_MBSFN_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_transmissionModeDL_nonMBSFN_r15 <= USED 0
PerDec_EuAntennaInfoDedicated_v1430_ce_UE_TxAntennaSelection_config_r14 <= USED 0
PerDec_EuAntennaInfoDedicated_v1530_ue_TxAntennaSelection_SRS_2T4R_NrOfPairs_r15 <= USED 0
PerDec_EuAntennaInfoUL_STTI_r15_transmissionModeUL_STTI_r15 <= USED 0
PerDec_EuCA_BandwidthClass_r10 <= USED 0
PerDec_EuCDMA2000_Type <= USED 0
PerDec_EuCQI_ReportConfigSCell_r15_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportConfig_r15_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportConfig_v1530_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportPeriodicSCell_r15_subbandCQI_r15_periodicityFactor_r15 <= USED 0
PerDec_EuCQI_ReportPeriodicSCell_r15_widebandCQI_r15_csi_ReportMode_r15 <= USED 0
PerDec_EuCQI_ShortConfigSCell_r15_subbandCQI_Short_r15_periodicityFactor_r15 <= USED 0
PerDec_EuCQI_ShortConfigSCell_r15_widebandCQI_Short_r15_csi_ReportModeShort_r15 <= USED 0
PerDec_EuCRS_AssistanceInfo_r15_crs_IntfMitigEnabled_15 <= USED 0
PerDec_EuCSFB_RegistrationParam1XRTT_v920_powerDownReg_r9 <= USED 0
PerDec_EuCSI_RS_ConfigBeamformed_r14_alternativeCodebookEnabledBeamformed_r14 <= USED 0
PerDec_EuCSI_RS_ConfigBeamformed_r14_channelMeasRestriction_r14 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_Activation_r14_csi_RS_NZP_mode_r14 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430_cdmType_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1430_codebookConfigN1_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1430_codebookConfigN2_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1480_codebookConfigN1_v1480 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1480_codebookConfigN2_r1480 <= USED 0
PerDec_EuCarrierFreqInfoUTRA_v1250_reducedMeasPerformance_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_FDD_Ext_r12_reducedMeasPerformance_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_TDD_r12_reducedMeasPerformance_r12 <= USED 0
PerDec_EuCellReselectionInfoCommon_v1460_s_SearchDeltaP_r14 <= USED 0
PerDec_EuCellSelectionInfoCE_v1530_powerClass14dBm_Offset_r15 <= USED 0
PerDec_EuCellSelectionInfoNFreq_r13_q_Hyst_r13 <= USED 0
PerDec_EuDRB_ToAddMod_lwa_WLAN_AC_r14 <= USED 0
PerDec_EuDRX_Config_r15_drx_RetransmissionTimerShortTTI_r15 <= USED 0
PerDec_EuDRX_Config_r15_drx_UL_RetransmissionTimerShortTTI_r15 <= USED 0
PerDec_EuDataInactivityTimer_r14 <= USED 0
PerDec_EuDelayBudgetReport_r14_type1 <= USED 0
PerDec_EuDelayBudgetReport_r14_type2 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1a_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format3_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_RM_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_TBCC_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_Format1and1a_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_RM_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_TBCC_Format4_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1a_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format3_r15 <= USED 0
PerDec_EuEDT_PRACH_ParametersCE_r15_prach_StartingSubframe_r15 <= USED 0
PerDec_EuEstablishmentCause <= USED 0
PerDec_EuFlightPathInfoReportConfig_r15_includeTimeStamp_r15 <= USED 0
PerDec_EuGNSS_ID_r15_gnss_id_r15 <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs_dualRxTxRedirectIndicator_r10 <= USED 0
PerDec_EuHandover_targetRAT_Type <= USED 0
PerDec_EuLAA_SCellConfiguration_v1430_absenceOfAnyOtherTechnology_r14 <= USED 0
PerDec_EuLoggingDuration_r10 <= USED 0
PerDec_EuLoggingInterval_r10 <= USED 0
PerDec_EuLogicalChannelConfig_bitRateQueryProhibitTimer_r14 <= USED 0
PerDec_EuLogicalChannelConfig_logicalChannelSR_Restriction_r15_setup <= USED 0
PerDec_EuMAC_MainConfig_rai_Activation_r14 <= USED 0
PerDec_EuMAC_MainConfig_setup_dormantSCellDeactivationTimer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_periodicBSR_Timer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_proc_Timeline_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_sCellHibernationTimer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_skipUplinkTxDynamic_r14 <= USED 0
PerDec_EuMAC_MainConfig_setup_skipUplinkTxSPS_r14 <= USED 0
PerDec_EuMBMS_CarrierType_r14_carrierType_r14 <= USED 0
PerDec_EuMeasGapSharingConfig_r14_measGapSharingScheme_r14 <= USED 0
PerDec_EuMeasIdleCarrierEUTRA_r15_reportQuantities <= USED 0
PerDec_EuMeasIdleConfigDedicated_r15_measIdleDuration_r15 <= USED 0
PerDec_EuMeasSensing_Config_r15_sensingPeriodicity_r15 <= USED 0
PerDec_EuMobilityControlInfoSCG_r12_makeBeforeBreakSCG_r14 <= USED 0
PerDec_EuMobilityControlInfoSCG_r12_t307_r12 <= USED 0
PerDec_EuMobilityControlInfo_handoverWithoutWT_Change_r14 <= USED 0
PerDec_EuMobilityControlInfo_makeBeforeBreak_r14 <= USED 0
PerDec_EuMobilityControlInfo_sameSFN_Indication_r14 <= USED 0
PerDec_EuNZP_FrequencyDensity_r14 <= USED 0
PerDec_EuOtherConfig_r9_bw_PreferenceIndicationTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_delayBudgetReportingProhibitTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_overheatingIndicationProhibitTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_rlmReportRep_MPDCCH_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_rlmReportTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_serviceType <= USED 0
PerDec_EuPDCCH_CandidateReductionValue_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_maxNumberOfSchedSubframes_Format0B_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_maxNumberOfSchedSubframes_Format4B_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_skipMonitoringDCI_Format0A_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_skipMonitoringDCI_Format4A_r14 <= USED 0
PerDec_EuPDCP_Config_bufferSize_r15 <= USED 0
PerDec_EuPDCP_Config_dictionary_r15 <= USED 0
PerDec_EuPDCP_Config_setup_pdcp_Duplication_r15 <= USED 0
PerDec_EuPDCP_Config_setup_ul_LWA_DataSplitThreshold_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicatedSCell_v1430_tbsIndexAlt2_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_HARQ_AckBundling_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_PDSCH_MaxBandwidth_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_PDSCH_TenProcesses_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_SchedulingEnhancement_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_tbsIndexAlt2_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_altMCS_TableScalingConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_CQI_AlternativeTableConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_PDSCH_64QAM_Config_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_PDSCH_FlexibleStartPRB_AllocConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_qcl_Operation_v1530 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_tbs_IndexAlt3_r15 <= USED 0
PerDec_EuPDSCH_RE_MappingQCL_Config_r11_setup_crs_PortsCount_v1530 <= USED 0
PerDec_EuPDSCH_RE_MappingQCL_Config_r11_setup_pdsch_Start_v1530 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_cellReservedForOperatorUse_CRS_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_cellReservedForOperatorUse_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_v1530_cellReservedForOperatorUse_CRS_r15 <= USED 0
PerDec_EuPLMN_Info_r15_upperLayerIndication_r15 <= USED 0
PerDec_EuPMCH_Config_r9_mch_SchedulingPeriod_r9 <= USED 0
PerDec_EuPUCCH_ConfigCommon_v1430_pucch_NumRepetitionCE_Msg4_Level3_r14 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1430_pucch_NumRepetitionCE_format1_r14 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1530_codebooksizeDeterminationSTTI_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430_ce_PUSCH_MaxBandwidth_r14 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430_ce_PUSCH_NB_MaxTBS_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14_interval_FDD_PUSCH_Enh_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14_interval_TDD_PUSCH_Enh_r14 <= USED 0
PerDec_EuParametersCDMA2000_r11_csfb_DualRxTxSupport_r11 <= USED 0
PerDec_EuPerCC_GapIndication_r14_gapIndication_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_k_max_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_maxNumber_SlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_maxNumber_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_mcs_restrictionSlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_mcs_restrictionSubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_p_a_must_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_rv_SlotsublotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_rv_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_ce_pdsch_pusch_EnhancementConfig_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_k_max_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_maxNumber_SlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_maxNumber_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_mcs_restrictionSlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_mcs_restrictionSubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_p_a_must_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_rv_SlotsublotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_rv_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPollByte_r14 <= USED 0
PerDec_EuPollPDU_r15 <= USED 0
PerDec_EuPosSIB_Type_r15_encrypted_r15 <= USED 0
PerDec_EuPosSIB_Type_r15_posSibType_r15 <= USED 0
PerDec_EuPosSchedulingInfo_r15_posSI_Periodicity_r15 <= USED 0
PerDec_EuProcessingTimelineSet_r15 <= USED 0
PerDec_EuRACH_CE_LevelInfo_r13_edt_TBS_r15 <= USED 0
PerDec_EuRACH_CE_LevelInfo_r13_mac_ContentionResolutionTimer_r15 <= USED 0
PerDec_EuRACH_ConfigCommon_edt_SmallTBS_Subset_r15 <= USED 0
PerDec_EuRACH_Skip_r14_ul_SchedInterval_r14 <= USED 0
PerDec_EuRF_Parameters_v12b0_maxLayersMIMO_Indication_r12 <= USED 0
PerDec_EuRLC_Config_r15_reestablishRLC_r15 <= USED 0
PerDec_EuRLC_Config_r15_rlc_OutOfOrderDelivery_r15 <= USED 0
PerDec_EuRLC_Config_v1510_reestablishRLC_r15 <= USED 0
PerDec_EuRLC_Config_v1530_rlc_OutOfOrderDelivery_r15 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_n313_r12 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_n314_r12 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_t313_r12 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10_interleaving_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10_noInterleaving_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceAllocationType_r10 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1250_IEs_setup_t350_r12 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1430_IEs_perCC_GapIndicationRequest_r14 <= USED 0
PerDec_EuRRCConnectionRelease_v1530_IEs_cn_Type_r15 <= USED 0
PerDec_EuRRCConnectionRelease_v1530_IEs_drb_ContinueROHC_r15 <= USED 0
PerDec_EuRRC_InactiveConfig_r15_periodic_RNAU_timer_r15 <= USED 0
PerDec_EuRRC_InactiveConfig_r15_ran_PagingCycle_r15 <= USED 0
PerDec_EuRSS_Config_r15_duration_r15 <= USED 0
PerDec_EuRSS_Config_r15_periodicity_r15 <= USED 0
PerDec_EuRSS_Config_r15_powerBoost_r15 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_harq_ReferenceConfig_r14 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_soundingRS_FlexibleTiming_r14 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_ul_FreqInfo_r14_ul_Bandwidth_r14 <= USED 0
PerDec_EuReportConfigEUTRA_purpose_r15 <= USED 0
PerDec_EuReportConfigEUTRA_purpose_v1430 <= USED 0
PerDec_EuReportConfigInterRAT_reportSFTD_Meas_r15 <= USED 0
PerDec_EuReportQuantityWLAN_r13_availableAdmissionCapacityRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_backhaulDL_BandwidthRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_backhaulUL_BandwidthRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_bandRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_carrierInfoRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_channelUtilizationRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_stationCountRequestWLAN_r13 <= USED 0
PerDec_EuReselectionInfoRelay_r13_minHyst_r13 <= USED 0
PerDec_EuSBAS_ID_r15_sbas_id_r15 <= USED 0
PerDec_EuSL_DiscConfigOtherInterFreq_r13_refCarrierCommon_r13 <= USED 0
PerDec_EuSL_DiscConfigRelayUE_r13_hystMax_r13 <= USED 0
PerDec_EuSL_DiscConfigRelayUE_r13_hystMin_r13 <= USED 0
PerDec_EuSL_DiscConfigRemoteUE_r13_hystMax_r13 <= USED 0
PerDec_EuSN_FieldLength_r15 <= USED 0
PerDec_EuSPDCCH_Config_r15_spdcch_L1_ReuseIndication_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_rateMatchingMode_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_spdcch_SetReferenceSig_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_subframeType_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_transmissionType_r15 <= USED 0
PerDec_EuSchedulingRequestConfig_v1530_dssr_TransMax_r15 <= USED 0
PerDec_EuShortTTI_Length_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_altCQI_Table1024QAM_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_altCQI_TableSTTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_resourceAllocation_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt2_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt3_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt_STTI_r15 <= USED 0
PerDec_EuSubframeAssignment_r15 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_dl_256QAM_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_ul_64QAM_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1310_ue_PowerClass_5_r13 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320_intraFreq_CE_NeedForGaps_r13 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320_ue_PowerClass_N_r13 <= USED 0
PerDec_EuSystemInformationBlockType1_v1430_IEs_eCallOverIMS_Support_r14 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_cellBarred_CRS_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_crs_IntfMitigNumPRBs_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_hsdn_Cell_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs_si_posOffset_r15 <= USED 0
PerDec_EuSystemInformationBlockType26_r15_slss_TxMultiFreq_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_cp_EDT_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_idleModeMeasurements_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_reducedCP_LatencyEnabled_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_up_EDT_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_videoServiceCauseIndication_r14 <= USED 0
PerDec_EuSystemInformationBlockType3_crs_IntfMitigNeighCellsCE_r15 <= USED 0
PerDec_EuSystemInformationBlockType8_csfb_DualRxTxSupport_r10 <= USED 0
PerDec_EuTDD_ConfigSL_r12_subframeAssignmentSL_r12 <= USED 0
PerDec_EuTDD_Config_v1430_specialSubframePatterns_v1430 <= USED 0
PerDec_EuTDD_Config_v1450_specialSubframePatterns_v1450 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_dmrs_LessUpPTS_Config_r14 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_symPUSCH_UpPTS_r14 <= USED 0
PerDec_EuTimeReferenceInfo_r15_timeInfoType_r15 <= USED 0
PerDec_EuUAC_AC1_SelectAssistInfo_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15_uac_BarringFactor_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15_uac_BarringTime_r15 <= USED 0
PerDec_EuUECapabilityEnquiry_v1530_IEs_requestSTTI_SPT_Capability_r15 <= USED 0
PerDec_EuUEInformationRequest_v1020_IEs_logMeasReportReq_r10 <= USED 0
PerDec_EuUEInformationRequest_v1130_IEs_connEstFailReportReq_r11 <= USED 0
PerDec_EuUEInformationRequest_v1250_IEs_mobilityHistoryReportReq_r12 <= USED 0
PerDec_EuUEInformationRequest_v1530_IEs_idleModeMeasurementReq_r15 <= USED 0
PerDec_EuUE_TimersAndConstants_t300_r15 <= USED 0
PerDec_EuUL_AM_RLC_r15_maxRetxThreshold_r15 <= USED 0
PerDec_EuUL_PDCP_DelayResult_r13_qci_Id_r13 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12_deltaF_PUCCH_Format1bCS_r12 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12_deltaF_PUCCH_Format3_r12 <= USED 0
PerDec_EuWLAN_BandIndicator_r13 <= USED 0
PerDec_EuWLAN_CarrierInfo_r13_countryCode_r13 <= USED 0
PerDec_EuWLAN_RTT_r15_rttUnits_r15 <= USED 0
PerDec_EuWLAN_Status_r13 <= USED 0
PerDec_EuWLAN_Status_v1430 <= USED 0
PerDec_EuWLAN_backhaulRate_r12 <= USED 0
PerDec_EuWUS_Config_r15_freqLocation_r15 <= USED 0
PerDec_EuWUS_Config_r15_maxDurationFactor_r15 <= USED 0
PerDec_EuWUS_Config_r15_numPOs_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffsetDRX_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffset_eDRX_Long_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffset_eDRX_Short_r15 <= USED 0
;FILE lterrc_deco_r1551.o
PerDec_EuBT_Name_r15_str <= USED 0
PerDec_EuCellInfoUTRA_TDD_r10_utra_BCCH_Container_r10_str <= USED 0
PerDec_EuCellInfoUTRA_TDD_r9_utra_BCCH_Container_r9_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v890_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuHandover_nas_SecurityParamFromEUTRA_str <= USED 0
PerDec_EuHandover_targetRAT_MessageContainer_str <= USED 0
PerDec_EuIKE_Identity_r13_idI_r13_str <= USED 0
PerDec_EuLWA_Config_r13_wt_MAC_Address_r14_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs_tce_Id_r10_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs_traceRecordingSessionRef_r10_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1080_IEs_lateNonCriticalExtension_r10_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v8a0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityParametersCDMA2000_str <= USED 0
PerDec_EuOtherConfig_r9_setup_measConfigAppLayerContainer_r15_str <= USED 0
PerDec_EuPosSystemInformation_r15_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10l0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v12f0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1430_IEs_systemInformationBlockType2Dedicated_r14_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v8m0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_BR_r14_lateNonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_r13_lateNonCriticalExtension_str <= USED 0
PerDec_EuSecurityConfigHO_v1530_epc_To5GC_r15_nas_Container_r15_str <= USED 0
PerDec_EuSecurityConfigHO_v1530_intra5GC_r15_nas_Container_r15_str <= USED 0
PerDec_EuSystemInformationBlockPos_r15_assistanceDataSIB_Element_r15_str <= USED 0
PerDec_EuSystemInformationBlockPos_r15_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType12_r9_warningAreaCoordinatesSegment_r15_str <= USED 0
PerDec_EuSystemInformationBlockType2_v10n0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType6_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType7_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType8_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType9_hnb_Name_str <= USED 0
PerDec_EuSystemInformationBlockType9_lateNonCriticalExtension_str <= USED 0
PerDec_EuTMGI_r9_serviceId_r9_str <= USED 0
PerDec_EuTraceReference_r10_traceId_r10_str <= USED 0
PerDec_EuUEInformationRequest_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_bssid_r12_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_hessid_r12_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_ssid_r12_str <= USED 0
PerDec_EuWLAN_Name_r15_str <= USED 0
;FILE lterrc_decs_r1551.o
PerDec_EuAC_BarringConfig1XRTT_r9 <= USED 0
PerDec_EuAUL_Config_r15_setup <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_setup <= USED 0
PerDec_EuAntennaInfoDedicated_v1430 <= USED 0
PerDec_EuAntennaInfoUL_STTI_r15 <= USED 0
PerDec_EuAreaConfiguration_v1130 <= USED 0
PerDec_EuBCCH_DL_SCH_Message_BR <= USED 0
PerDec_EuBandClassInfoCDMA2000 <= USED 0
PerDec_EuBandClassListCDMA2000 <= USED 0
PerDec_EuBandCombinationListEUTRA_r10 <= USED 0
PerDec_EuBandCombinationList_r14 <= USED 0
PerDec_EuBandCombination_r14 <= USED 0
PerDec_EuBandIndication_r14 <= USED 0
PerDec_EuBandInfoEUTRA <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_oneBit_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_threeBit_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_twoBit_r14 <= USED 0
PerDec_EuCSFB_RegistrationParam1XRTT <= USED 0
PerDec_EuCSFB_RegistrationParam1XRTT_v920 <= USED 0
PerDec_EuCSI_RS_ConfigNZPToReleaseList_r15 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430_nzp_resourceConfigListExt_r14 <= USED 0
PerDec_EuCSI_RS_ConfigZP_ApList_r14_setup <= USED 0
PerDec_EuCandidateServingFreqListNR_r15 <= USED 0
PerDec_EuCarrierFreqInfoUTRA_FDD_v8h0 <= USED 0
PerDec_EuCarrierFreqInfoUTRA_FDD_v8h0_multiBandInfoList <= USED 0
PerDec_EuCarrierFreqInfoUTRA_v1250 <= USED 0
PerDec_EuCarrierFreqListUTRA_FDD <= USED 0
PerDec_EuCarrierFreqListUTRA_FDD_Ext_r12 <= USED 0
PerDec_EuCarrierFreqListUTRA_TDD <= USED 0
PerDec_EuCarrierFreqListUTRA_TDD_Ext_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_FDD <= USED 0
PerDec_EuCarrierFreqUTRA_FDD_Ext_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_FDD_Ext_r12_multiBandInfoList_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_FDD_Ext_r12_threshX_Q_r12 <= USED 0
PerDec_EuCarrierFreqUTRA_FDD_threshX_Q_r9 <= USED 0
PerDec_EuCarrierFreqUTRA_TDD <= USED 0
PerDec_EuCarrierFreqUTRA_TDD_r12 <= USED 0
PerDec_EuCarrierFreqsInfoGERAN <= USED 0
PerDec_EuCarrierFreqsInfoGERAN_commonInfo <= USED 0
PerDec_EuCarrierFreqsInfoListGERAN <= USED 0
PerDec_EuCellGlobalIdEUTRA <= USED 0
PerDec_EuCellGlobalIdGERAN <= USED 0
PerDec_EuCellGlobalIdList_r10 <= USED 0
PerDec_EuCellGlobalIdUTRA <= USED 0
PerDec_EuCellInfoListUTRA_TDD_r10 <= USED 0
PerDec_EuCellInfoListUTRA_TDD_r9 <= USED 0
PerDec_EuCellInfoUTRA_TDD_r10 <= USED 0
PerDec_EuCellInfoUTRA_TDD_r9 <= USED 0
PerDec_EuCellReselectionParametersCDMA2000 <= USED 0
PerDec_EuCellReselectionParametersCDMA2000_r11 <= USED 0
PerDec_EuCellReselectionParametersCDMA2000_r11_neighCellList_r11 <= USED 0
PerDec_EuCellReselectionParametersCDMA2000_v920 <= USED 0
PerDec_EuCellSelectionInfoCE_v1530 <= USED 0
PerDec_EuCellSelectionInfoNFreq_r13 <= USED 0
PerDec_EuCellsToAddModCDMA2000 <= USED 0
PerDec_EuCellsToAddModListCDMA2000 <= USED 0
PerDec_EuCellsToAddModListUTRA_TDD <= USED 0
PerDec_EuCellsToAddModUTRA_TDD <= USED 0
PerDec_EuCountingRequestInfo_r10 <= USED 0
PerDec_EuCountingRequestList_r10 <= USED 0
PerDec_EuCrossCarrierSchedulingConfigLAA_UL_r14 <= USED 0
PerDec_EuDL_CCCH_Message <= USED 0
PerDec_EuDL_DCCH_Message <= USED 0
PerDec_EuDRB_CountInfo <= USED 0
PerDec_EuDRB_CountInfoList <= USED 0
PerDec_EuDRB_CountInfoListExt_r15 <= USED 0
PerDec_EuDRB_ToAddModListSCG_r12 <= USED 0
PerDec_EuDRB_ToAddModListSCG_r15 <= USED 0
PerDec_EuDRB_ToAddModList_r15 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12_scg_r12 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15 <= USED 0
PerDec_EuEnable256QAM_r14_tpc_SubframeSet_Configured_r14 <= USED 0
PerDec_EuEnable256QAM_r14_tpc_SubframeSet_NotConfigured_r14 <= USED 0
PerDec_EuFreqPriorityListUTRA_TDD <= USED 0
PerDec_EuFreqPriorityUTRA_TDD <= USED 0
PerDec_EuHandover <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_r8_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v890_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v920_IEs <= USED 0
PerDec_EuIKE_Identity_r13 <= USED 0
PerDec_EuInterFreqBandInfo <= USED 0
PerDec_EuInterFreqBandList <= USED 0
PerDec_EuInterFreqCarrierFreqInfo_v1530 <= USED 0
PerDec_EuInterFreqCarrierFreqListExt_v1530 <= USED 0
PerDec_EuInterFreqCarrierFreqList_v1530 <= USED 0
PerDec_EuInterFreqNeighHSDN_CellList_r15 <= USED 0
PerDec_EuInterRAT_BandInfo <= USED 0
PerDec_EuInterRAT_BandList <= USED 0
PerDec_EuIntraFreqNeighHSDN_CellList_r15 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1080_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1130_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1250_IEs <= USED 0
PerDec_EuMeasObjectCDMA2000 <= USED 0
PerDec_EuMobilityControlInfoSCG_r12 <= USED 0
PerDec_EuMobilityControlInfo_v10l0 <= USED 0
PerDec_EuNeighCellCDMA2000 <= USED 0
PerDec_EuNeighCellCDMA2000_r11 <= USED 0
PerDec_EuNeighCellCDMA2000_r11_neighFreqInfoList_r11 <= USED 0
PerDec_EuNeighCellCDMA2000_v920 <= USED 0
PerDec_EuNeighCellListCDMA2000 <= USED 0
PerDec_EuNeighCellListCDMA2000_v920 <= USED 0
PerDec_EuNeighCellsPerBandclassCDMA2000 <= USED 0
PerDec_EuNeighCellsPerBandclassCDMA2000_r11 <= USED 0
PerDec_EuNeighCellsPerBandclassCDMA2000_r11_physCellIdList_r11 <= USED 0
PerDec_EuNeighCellsPerBandclassCDMA2000_v920 <= USED 0
PerDec_EuNeighCellsPerBandclassListCDMA2000 <= USED 0
PerDec_EuNeighCellsPerBandclassListCDMA2000_v920 <= USED 0
PerDec_EuOtherConfig_r9_setup <= USED 0
PerDec_EuOtherConfig_r9_setup_1 <= USED 0
PerDec_EuOtherConfig_r9_setup_2 <= USED 0
PerDec_EuOtherConfig_r9_setup_3 <= USED 0
PerDec_EuPCI_ARFCN_r13 <= USED 0
PerDec_EuPDSCH_ConfigDedicatedSCell_v1430 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_v1530 <= USED 0
PerDec_EuPLMN_IdentityList2 <= USED 0
PerDec_EuPLMN_IdentityList3_r11 <= USED 0
PerDec_EuPLMN_IdentityList4_r12 <= USED 0
PerDec_EuPLMN_IdentityList_r15 <= USED 0
PerDec_EuPLMN_IdentityList_v1530 <= USED 0
PerDec_EuPLMN_InfoList_r15 <= USED 0
PerDec_EuPLMN_Info_r15 <= USED 0
PerDec_EuPRACH_Config_v1430 <= USED 0
PerDec_EuPSCellToAddMod_r12 <= USED 0
PerDec_EuPSCellToAddMod_r12_cellIdentification_r12 <= USED 0
PerDec_EuPSCellToAddMod_v12f0 <= USED 0
PerDec_EuPSCellToAddMod_v1440 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_channelSelection_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_n1PUCCH_AN_CS_v13c0_setup <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_setup_n1PUCCH_AN_CS_ListP1_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedSCell_v1430 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530_setup <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_setup <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_setup_1 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_setup <= USED 0
PerDec_EuParametersCDMA2000_r11 <= USED 0
PerDec_EuParametersCDMA2000_r11_parameters1XRTT_r11 <= USED 0
PerDec_EuParametersCDMA2000_r11_parametersHRPD_r11 <= USED 0
PerDec_EuPhysCellIdListCDMA2000 <= USED 0
PerDec_EuPhysCellIdListCDMA2000_v920 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0_setup <= USED 0
PerDec_EuPhysicalConfigDedicated_v1370 <= USED 0
PerDec_EuPhysicalConfigDedicated_v13c0 <= USED 0
PerDec_EuPowerCoordinationInfo_r12 <= USED 0
PerDec_EuRACH_Skip_r14 <= USED 0
PerDec_EuRACH_Skip_r14_ul_ConfigInfo_r14 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_setup <= USED 0
PerDec_EuRN_SubframeConfig_r10_channelSelectionMultiplexingBundling_n1PUCCH_AN_List_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10_fdd <= USED 0
PerDec_EuRN_SubframeConfig_r10_rpdcch_Config_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_tdd_channelSelectionMultiplexingBundling <= USED 0
PerDec_EuRN_SubframeConfig_r10_tdd_fallbackForFormat3 <= USED 0
PerDec_EuROHC_ProfileSupportList <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10i0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10l0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v12f0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1370_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v13c0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v8m0_IEs <= USED 0
PerDec_EuRSRQ_Type_r12 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_r12 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_v12f0 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v10l0 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v10l0_ul_Configuration_v10l0 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440_ul_Configuration_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440_ul_FreqInfo_v1440 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_r12 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_v1370 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_v13c0 <= USED 0
PerDec_EuRadioResourceConfigDedicatedSCG_r12 <= USED 0
PerDec_EuRadioResourceConfigDedicatedSCell_v13c0 <= USED 0
PerDec_EuRadioResourceConfigDedicated_dummy <= USED 0
PerDec_EuRadioResourceConfigDedicated_v1370 <= USED 0
PerDec_EuRadioResourceConfigDedicated_v13c0 <= USED 0
PerDec_EuRegisteredMME <= USED 0
PerDec_EuReportQuantityNR_r15 <= USED 0
PerDec_EuReportQuantityWLAN_r13 <= USED 0
PerDec_EuSBAS_ID_r15 <= USED 0
PerDec_EuSCG_ConfigPartSCG_r12 <= USED 0
PerDec_EuSCG_ConfigPartSCG_v12f0 <= USED 0
PerDec_EuSCG_ConfigPartSCG_v13c0 <= USED 0
PerDec_EuSCG_Configuration_r12_scg_ConfigPartMCG_r12 <= USED 0
PerDec_EuSCG_Configuration_r12_setup <= USED 0
PerDec_EuSCG_Configuration_v12f0_setup <= USED 0
PerDec_EuSCG_Configuration_v13c0_setup <= USED 0
PerDec_EuSCPTM_NeighbourCellList_r13 <= USED 0
PerDec_EuSCellConfigCommon_r15 <= USED 0
PerDec_EuSCellGroupToReleaseList_r15 <= USED 0
PerDec_EuSCellToAddModExt_r13 <= USED 0
PerDec_EuSCellToAddModExt_r13_cellIdentification_r13 <= USED 0
PerDec_EuSCellToAddModExt_v1370 <= USED 0
PerDec_EuSCellToAddModListExt_r13 <= USED 0
PerDec_EuSCellToAddModListExt_v1370 <= USED 0
PerDec_EuSCellToAddModListExt_v13c0 <= USED 0
PerDec_EuSCellToAddModList_v10l0 <= USED 0
PerDec_EuSCellToAddModList_v13c0 <= USED 0
PerDec_EuSCellToAddMod_v10l0 <= USED 0
PerDec_EuSCellToAddMod_v13c0 <= USED 0
PerDec_EuSCellToReleaseListExt_r13 <= USED 0
PerDec_EuSIB8_PerPLMN_List_r11 <= USED 0
PerDec_EuSIB8_PerPLMN_r11 <= USED 0
PerDec_EuSS_RSSI_Measurement_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530 <= USED 0
PerDec_EuSecurityConfigHO_v1530_epc_To5GC_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_fivegc_ToEPC_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_intra5GC_r15 <= USED 0
PerDec_EuSoundingRS_UL_ConfigDedicatedAperiodic_v1430_setup <= USED 0
PerDec_EuSupportedBandEUTRA <= USED 0
PerDec_EuSupportedBandEUTRA_v1250 <= USED 0
PerDec_EuSupportedBandEUTRA_v1310 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320 <= USED 0
PerDec_EuSupportedBandEUTRA_v9e0 <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs_freqInfo_v10l0 <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs_multiBandInfoList_v10l0 <= USED 0
PerDec_EuSystemInformationBlockType2_v10n0_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v13c0_IEs <= USED 0
PerDec_EuSystemInformationBlockType3_v10j0_IEs <= USED 0
PerDec_EuSystemInformationBlockType3_v10l0_IEs <= USED 0
PerDec_EuSystemInformationBlockType6 <= USED 0
PerDec_EuSystemInformationBlockType6_carrierFreqListUTRA_FDD_v1250 <= USED 0
PerDec_EuSystemInformationBlockType6_carrierFreqListUTRA_TDD_v1250 <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs_carrierFreqListUTRA_FDD_v8h0 <= USED 0
PerDec_EuSystemInformationBlockType7 <= USED 0
PerDec_EuSystemInformationBlockType8 <= USED 0
PerDec_EuSystemInformationBlockType8_parameters1XRTT <= USED 0
PerDec_EuSystemInformationBlockType8_parametersHRPD <= USED 0
PerDec_EuSystemInformationBlockType9 <= USED 0
PerDec_EuSystemTimeInfoCDMA2000 <= USED 0
PerDec_EuTDD_ConfigSL_r12 <= USED 0
PerDec_EuTDD_Config_v1430 <= USED 0
PerDec_EuTDD_Config_v1450 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_setup <= USED 0
PerDec_EuTMGI_r9 <= USED 0
PerDec_EuTargetMBSFN_AreaList_r12 <= USED 0
PerDec_EuTargetMBSFN_Area_r12 <= USED 0
PerDec_EuThresholdListNR_r15 <= USED 0
PerDec_EuTraceReference_r10 <= USED 0
PerDec_EuTrackingAreaCodeList_r10 <= USED 0
PerDec_EuTrackingAreaCodeList_v1130 <= USED 0
PerDec_EuTrackingAreaCodeList_v1130_plmn_Identity_perTAC_List_r11 <= USED 0
PerDec_EuTx_ResourcePoolMeasList_r14 <= USED 0
PerDec_EuUAC_BarringInfoSetList_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15 <= USED 0
PerDec_EuUAC_BarringPerCatList_r15 <= USED 0
PerDec_EuUAC_BarringPerCat_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_List_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15_uac_AC_BarringListType_r15_uac_ImplicitAC_BarringList_r15 <= USED 0
PerDec_EuUEInformationRequest_r9 <= USED 0
PerDec_EuUEInformationRequest_r9_IEs <= USED 0
PerDec_EuUEInformationRequest_v1020_IEs <= USED 0
PerDec_EuUEInformationRequest_v1130_IEs <= USED 0
PerDec_EuUEInformationRequest_v1250_IEs <= USED 0
PerDec_EuUEInformationRequest_v930_IEs <= USED 0
PerDec_EuUL_PDCP_DelayResultList_r13 <= USED 0
PerDec_EuUL_PDCP_DelayResult_r13 <= USED 0
PerDec_EuUplinkPUSCH_LessPowerControlDedicated_v1430 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12 <= USED 0
PerDec_EuUplinkPowerControlCommonPUSCH_LessCell_v1430 <= USED 0
PerDec_EuUplinkPowerControlDedicatedSTTI_r15 <= USED 0
PerDec_EuUplinkPowerControlDedicated_v1530 <= USED 0
PerDec_EuWLAN_CarrierInfo_r13 <= USED 0
PerDec_EuWLAN_ChannelList_r13 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdBackhaul_Bandwidth_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdChannelUtilization_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRP_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_OnAllSymbolsWithWB_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_OnAllSymbols_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_WB_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdWLAN_RSSI_r12 <= USED 0
PerDec_EuWLAN_RTT_r15 <= USED 0
;FILE lterrc_encb_r1551.o
PerEnc_EuAbsoluteTimeInfo_r10_str <= USED 0
PerEnc_EuBandCombinationParameters_v1430_v2x_SupportedRxBandCombListPerBC_r14_str <= USED 0
PerEnc_EuBandCombinationParameters_v1430_v2x_SupportedTxBandCombListPerBC_r14_str <= USED 0
PerEnc_EuCSG_Identity <= USED 0
PerEnc_EuC_RNTI <= USED 0
PerEnc_EuCellIdentity <= USED 0
PerEnc_EuFailureInformation_r15_nonCriticalExtension_str <= USED 0
PerEnc_EuFlightPathInfoReport_r15_nonCriticalExtension_str <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig0_r11_str <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig6_r11_str <= USED 0
PerEnc_EuI_RNTI_r15_str <= USED 0
PerEnc_EuInitialUE_Identity_5GC_ng_5G_S_TMSI_Part1_str <= USED 0
PerEnc_EuInitialUE_Identity_5GC_randomValue_str <= USED 0
PerEnc_EuMMEC <= USED 0
PerEnc_EuNG_5G_S_TMSI_r15_str <= USED 0
PerEnc_EuNeighCellConfig <= USED 0
PerEnc_EuRRCConnectionReconfigurationComplete_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionReestablishmentComplete_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1540_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuResumeIdentity_r13_str <= USED 0
PerEnc_EuShortMAC_I <= USED 0
PerEnc_EuTrackingAreaCode <= USED 0
PerEnc_EuUEAssistanceInformation_r11_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuUEAssistanceInformation_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUECapabilityInformation_v1250_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_r9_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuUEInformationResponse_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_v9e0_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUE_EUTRA_Capability_v10j0_IEs_nonCriticalExtension_str <= USED 0
;FILE lterrc_encc_r1551.o
PerEnc_EuBandCombinationParameters_r13_supportedCellGrouping_r13 <= USED 0
PerEnc_EuIDC_SubframePattern_r11 <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11 <= USED 0
PerEnc_EuInitialUE_Identity_5GC <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11 <= USED 0
PerEnc_EuTMGI_r9_plmn_Id_r9 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_c1 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_criticalExtensions <= USED 0
PerEnc_EuVisitedCellInfo_r12_visitedCellId_r12 <= USED 0
;FILE lterrc_ence_r1551.o
PerEnc_EuAffectedCarrierFreq_r11_interferenceDirection_r11 <= USED 0
PerEnc_EuAlpha_r12 <= USED 0
PerEnc_EuBandCombinationParameters_r13_asynchronous_r13 <= USED 0
PerEnc_EuCA_BandwidthClass_r10 <= USED 0
PerEnc_EuCipheringAlgorithm <= USED 0
PerEnc_EuDC_Parameters_r12_drb_TypeSCG_r12 <= USED 0
PerEnc_EuDC_Parameters_r12_drb_TypeSplit_r12 <= USED 0
PerEnc_EuEstablishmentCause_5GC <= USED 0
PerEnc_EuFailedLogicalChannelInfo_r15_failureType <= USED 0
PerEnc_EuIRAT_ParametersGERAN_v920_dtm_r9 <= USED 0
PerEnc_EuIRAT_ParametersGERAN_v920_e_RedirectionGERAN_r9 <= USED 0
PerEnc_EuLogMeasInfo_r10_inDeviceCoexDetected_r13 <= USED 0
PerEnc_EuMIMO_CapabilityUL_r10 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config1_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config2_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config3_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config4_r13 <= USED 0
PerEnc_EuMIMO_UE_BeamformedCapabilities_r13_altCodebook_r13 <= USED 0
PerEnc_EuMeasCycleSCell_r10 <= USED 0
PerEnc_EuMeasReportAppLayer_r15_IEs_serviceType <= USED 0
PerEnc_EuMeasScaleFactor_r12 <= USED 0
PerEnc_EuMobilityStateParameters_t_Evaluation <= USED 0
PerEnc_EuMobilityStateParameters_t_HystNormal <= USED 0
PerEnc_EuP_a <= USED 0
PerEnc_EuPeriodicBSR_Timer_r12 <= USED 0
PerEnc_EuPhyLayerParameters_v1280_alternativeTBS_Indices_r12 <= USED 0
PerEnc_EuPhyLayerParameters_v1310_pdcch_CandidateReductions_r13 <= USED 0
PerEnc_EuPhyLayerParameters_v1310_skipMonitoringDCI_Format0_1A_r13 <= USED 0
PerEnc_EuPhysCellIdRange_range <= USED 0
PerEnc_EuPreambleTransMax <= USED 0
PerEnc_EuQ_OffsetRange <= USED 0
PerEnc_EuRF_Parameters_v10j0_multiNS_Pmax_r10 <= USED 0
PerEnc_EuReleaseCause <= USED 0
PerEnc_EuRetxBSR_Timer_r12 <= USED 0
PerEnc_EuSL_CP_Len_r12 <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_discPeriod_r12 <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_freqInfo_ul_Bandwidth <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_setup <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_ue_SelectedResourceConfig_r12_txProbability_r12 <= USED 0
PerEnc_EuSL_DiscSysInfoReport_r13_q_Hyst_r13 <= USED 0
PerEnc_EuSL_DiscSysInfoReport_r13_ul_Bandwidth_r13 <= USED 0
PerEnc_EuSL_GapPattern_r13_gapPeriod_r13 <= USED 0
PerEnc_EuSL_Parameters_r12_commSimultaneousTx_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_discScheduledResourceAlloc_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_discSupportedProc_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_disc_SLSS_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_disc_UE_SelectedResourceAlloc_r12 <= USED 0
PerEnc_EuSL_PeriodComm_r12 <= USED 0
PerEnc_EuSL_SyncConfigNFreq_r13_discSyncWindow_r13 <= USED 0
PerEnc_EuSL_SyncConfigNFreq_r13_syncTxPeriodic_r13 <= USED 0
PerEnc_EuSRS_AntennaPort <= USED 0
PerEnc_EuSupportedBandInfo_r12_support_r12 <= USED 0
PerEnc_EuTDD_ConfigSL_r12_subframeAssignmentSL_r12 <= USED 0
PerEnc_EuTDD_Config_specialSubframePatterns <= USED 0
PerEnc_EuTDD_Config_subframeAssignment <= USED 0
PerEnc_EuTDD_Config_v1130_specialSubframePatterns_v1130 <= USED 0
PerEnc_EuTDD_Config_v1430_specialSubframePatterns_v1430 <= USED 0
PerEnc_EuTDD_Config_v1450_specialSubframePatterns_v1450 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_ActiveTime_r11 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_CycleLength_r11 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs_powerPrefIndication_r11 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10_loggedMeasurementsIdle_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10_standaloneGNSS_Location_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250_loggedMBSFNMeasurements_r12 <= USED 0
PerEnc_EuUL_CyclicPrefixLength <= USED 0
PerEnc_EuVictimSystemType_r11_bds_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_bluetooth_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_galileo_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_glonass_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_gps_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_wlan_r11 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12_wlan_IW_ANDSF_Policies_r12 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12_wlan_IW_RAN_Rules_r12 <= USED 0
;FILE lterrc_enco_r1551.o
PerEnc_EuHandover_nas_SecurityParamFromEUTRA_str <= USED 0
PerEnc_EuHandover_targetRAT_MessageContainer_str <= USED 0
PerEnc_EuLocationInfo_r10_gnss_TOD_msec_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_horizontalVelocity_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidArc_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithAltitudeAndUncertaintyEllipsoid_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithAltitude_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithUncertaintyCircle_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithUncertaintyEllipse_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoid_Point_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_polygon_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_verticalVelocityInfo_r15_verticalVelocityAndUncertainty_r15_str <= USED 0
PerEnc_EuLocationInfo_r10_verticalVelocityInfo_r15_verticalVelocity_r15_str <= USED 0
PerEnc_EuMeasReportAppLayer_r15_IEs_measReportAppLayerContainer_r15_str <= USED 0
PerEnc_EuOtherConfig_r9_setup_measConfigAppLayerContainer_r15_str <= USED 0
PerEnc_EuTMGI_r9_serviceId_r9_str <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs_lateNonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_v930_IEs_lateNonCriticalExtension_str <= USED 0
;FILE lterrc_encs_r1551.o
PerEnc_EuAffectedCarrierFreqCombList_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqCombList_r13 <= USED 0
PerEnc_EuAffectedCarrierFreqComb_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqComb_r13 <= USED 0
PerEnc_EuAffectedCarrierFreqList_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqList_v1310 <= USED 0
PerEnc_EuAffectedCarrierFreq_r11 <= USED 0
PerEnc_EuAffectedCarrierFreq_v1310 <= USED 0
PerEnc_EuBLER_Result_r12 <= USED 0
PerEnc_EuBLER_Result_r12_blocksReceived_r12 <= USED 0
PerEnc_EuBandParametersDL_r10 <= USED 0
PerEnc_EuBandParametersUL_r10 <= USED 0
PerEnc_EuCA_MIMO_ParametersDL_r10 <= USED 0
PerEnc_EuCA_MIMO_ParametersDL_r13_intraBandContiguousCC_InfoList_r13 <= USED 0
PerEnc_EuCA_MIMO_ParametersUL_r10 <= USED 0
PerEnc_EuCE_Parameters_r13 <= USED 0
PerEnc_EuCE_Parameters_v1320 <= USED 0
PerEnc_EuCellIndexList <= USED 0
PerEnc_EuDC_Parameters_r12 <= USED 0
PerEnc_EuFormat4_resource_r13 <= USED 0
PerEnc_EuFormat5_resource_r13 <= USED 0
PerEnc_EuFreqBandIndicatorListEUTRA_r12 <= USED 0
PerEnc_EuIDC_SubframePatternList_r11 <= USED 0
PerEnc_EuIMSI <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13 <= USED 0
PerEnc_EuMeasResult2EUTRA_r9 <= USED 0
PerEnc_EuMeasResult2EUTRA_v1250 <= USED 0
PerEnc_EuMeasResult2EUTRA_v9e0 <= USED 0
PerEnc_EuMeasResultList2EUTRA_r9 <= USED 0
PerEnc_EuMeasResultList2EUTRA_v1250 <= USED 0
PerEnc_EuMeasResultList2EUTRA_v9e0 <= USED 0
PerEnc_EuMeasResults_measResultPCell_v1310 <= USED 0
PerEnc_EuMobilityStateParameters <= USED 0
PerEnc_EuMultiBandInfoList <= USED 0
PerEnc_EuMultiBandInfoList_r11 <= USED 0
PerEnc_EuMultiBandInfoList_v10j0 <= USED 0
PerEnc_EuMultiBandInfoList_v10l0 <= USED 0
PerEnc_EuMultiBandInfoList_v9e0 <= USED 0
PerEnc_EuMultiBandInfo_v9e0 <= USED 0
PerEnc_EuNS_PmaxList_r10 <= USED 0
PerEnc_EuNS_PmaxList_v10l0 <= USED 0
PerEnc_EuNS_PmaxValue_r10 <= USED 0
PerEnc_EuNS_PmaxValue_v10l0 <= USED 0
PerEnc_EuPLMN_IdentityList3_r11 <= USED 0
PerEnc_EuPhyLayerParameters_v1280 <= USED 0
PerEnc_EuPhyLayerParameters_v1320 <= USED 0
PerEnc_EuPhysCellIdRange <= USED 0
PerEnc_EuPhysCellIdRangeUTRA_FDDList_r9 <= USED 0
PerEnc_EuPhysCellIdRangeUTRA_FDD_r9 <= USED 0
PerEnc_EuRF_Parameters_v10j0 <= USED 0
PerEnc_EuRSRQ_Type_r12 <= USED 0
PerEnc_EuSL_CommTxResourceReq_r12 <= USED 0
PerEnc_EuSL_DestinationInfoList_r12 <= USED 0
PerEnc_EuSL_DiscTxResourceReqPerFreqList_r13 <= USED 0
PerEnc_EuSL_DiscTxResourceReq_r13 <= USED 0
PerEnc_EuSL_Parameters_r12 <= USED 0
PerEnc_EuSL_TxParameters_r12 <= USED 0
PerEnc_EuSupportedBandInfoList_r12 <= USED 0
PerEnc_EuSupportedBandInfo_r12 <= USED 0
PerEnc_EuTDD_Config <= USED 0
PerEnc_EuTDD_ConfigSL_r12 <= USED 0
PerEnc_EuTDD_Config_v1130 <= USED 0
PerEnc_EuTDD_Config_v1430 <= USED 0
PerEnc_EuTDD_Config_v1450 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_AssistanceInfo_r11 <= USED 0
PerEnc_EuTMGI_r9 <= USED 0
PerEnc_EuTargetMBSFN_Area_r12 <= USED 0
PerEnc_EuUEAssistanceInformation_r11 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250 <= USED 0
PerEnc_EuUE_EUTRA_CapabilityAddXDD_Mode_v1180 <= USED 0
PerEnc_EuUE_EUTRA_Capability_v10j0_IEs <= USED 0
PerEnc_EuVictimSystemType_r11 <= USED 0
PerEnc_EuVisitedCellInfoList_r12 <= USED 0
PerEnc_EuVisitedCellInfo_r12 <= USED 0
PerEnc_EuVisitedCellInfo_r12_pci_arfcn_r12 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12 <= USED 0
;FILE lterrc_psm.o
ErrcCerReadPsmContext <= USED 0
ErrcCerWritePsmContext <= USED 0
ErrcFreePsmLocalMem <= USED 0
;FILE lterrcais.o
LteAisDecodeDlDcchMsgRrcReconfigFromOtherRat <= USED 0
LteAisEncodeUeCapabilityRatContainer <= USED 0
;FILE lterrcband.o
;FILE lterrccell.o
LteRrcCellMgrCalcEutraNcellNum <= USED 0
LteRrcCellMgrCheckCellPool <= USED 0
LteRrcCellMgrCheckDb <= USED 0
LteRrcCellMgrCheckFreqPool <= USED 0
LteRrcCellMgrCheckRliPool <= USED 0
LteRrcCellMgrClearFreq <= USED 0
LteRrcCellMgrDebugAssert466 <= USED 0
LteRrcCellMgrExtractCell <= USED 0
lteRrcCellSwapIntraInterFreq <= USED 0
;FILE lterrccer.o
LteCerConstructAndSendEutraProximityInd <= USED 0
LteCerDamResetParams <= USED 0
LteCerGetHoNotAlloweFlag <= USED 0
LteCerGetRplmn <= USED 0
LteCerHandleIfCsCallIgnoredByNW <= USED 0
LteCerHandleT301Expiry <= USED 0
LteCerInitialReestRecord <= USED 0
LteCerSortFreqRsrp <= USED 0
LtePdcpGmmReestablishReqForTest <= USED 0
LteRrcBuildMeasResultList2EUTRA_v9e0 <= USED 0
LteRrcBuildMeasResultListEUTRA_v1130 <= USED 0
LteRrcCerSetAutoRedirect <= USED 0
LteRrcCheckMeasResultListEUTRA <= USED 0
LteRrcClearHandoverProtectTimer <= USED 0
LteRrcClearRbEstablishProtectTimer <= USED 0
LteRrcClearVoiceBadReestStart <= USED 0
LteRrcFreeMemEpsAsSecurityContextBeforeHandover <= USED 0
LteRrcSendEcphyMeasSubframePatternPCellConfigReq <= USED 0
LteRrcSetHandoverProtectTimer <= USED 0
LteRrcSetRbEstablishProtectTimer <= USED 0
LteRrcTranslatePresentNeedOP <= USED 0
lterrcSendFakeExtendedServiceRequest <= USED 0
;FILE lterrccerrb.o
LteCerRbGetNotSuspendedSrbs <= USED 0
;FILE lterrccsr.o
ErrcDsIsErrcSuspended <= USED 0
GsmGetBandWidthOfSertvingCell <= USED 0
GsmGetSetLteCellStatus <= USED 0
LteBandGetBandsInformForPlmnSearch <= USED 0
LteCheckLockOnLteFreq <= USED 0
LteCsrAddCurrentCellToEarfcnList <= USED 0
LteCsrB41SupportedAndRequested <= USED 0
LteCsrCheckErrcWorkingPlmn <= USED 0
LteCsrCheckIfDrxFindCellisNeed <= USED 0
LteCsrCheckIsSpecialTacForGuangzhou <= USED 0
LteCsrFillGsmCellListInformation <= USED 0
LteCsrHandleCachedSib1ForLongEdrxExit <= USED 0
LteCsrHandleEcphySwitchRatToUmtsCnf <= USED 0
LteCsrIsCmccEsrvccTac <= USED 0
LteCsrIsNorthAmericanMcc <= USED 0
LteCsrIsRequestPlmnListContainCMCC <= USED 0
LteCsrProcessIfSib5Change <= USED 0
LteCsrRemoveCellFromBchCrcErrorBarList <= USED 0
LteCsrSearchNorthAmericaMccInSib1 <= USED 0
LteCsrSetLtePlmnStateForIcsSelection <= USED 0
LteCsrSetSib5 <= USED 0
LteCsrStartTreselection <= USED 0
LteRrcCheckIfLowThanMinSuitableCellRsrp <= USED 0
LteRrcCheckIfPendingErrcPlmnListAbortReq <= USED 0
LteRrcCsrCheckCellOtherThanB39Exists <= USED 0
LteRrcCsrCheckIfBandInMultiBandListSupported <= USED 0
LteRrcCsrCheckIfNcellOnIntendedBand <= USED 0
LteRrcCsrPlmnSearchContextAllocBand <= USED 0
LteRrcCsrPlmnSearchContextInit <= USED 0
LteRrcCsrSaveRsrpRsrq <= USED 0
LteRrcCsrSaveRssiC1Average <= USED 0
LteRrcQueryTddConfig <= USED 0
;FILE lterrccsrics.o
LteCsrIcsAddFreqToListFromMm <= USED 0
LteCsrIcsHandleEcphyDeactiveCnf <= USED 0
LteCsrIcsResetMacToIdle <= USED 0
;FILE lterrccsrplms.o
;FILE lterrccsrutils.o
;FILE lterrcdsds.o
LteRrcDsdsBackupFreqWhenSuspended <= USED 0
LteRrcDsdsCheckIfIsCellSearching <= USED 0
LteRrcDsdsForceSetCellReselecting <= USED 0
LteRrcDsdsGetIratReselectFromOtherRatJustOccurFlag <= USED 0
LteRrcDsdsGetSim2Activated <= USED 0
LteRrcDsdsProcessNcellMeasureForStrongScell <= USED 0
LteRrcDsdsSendErrcSuspendCnfWhenLeavingConnected <= USED 0
LteRrcDsdsSetSim2Activated <= USED 0
LteRrcDsdsSetSuspendCause <= USED 0
LteRrcDsdsTriggerUeEnterSuspendStateDirectly <= USED 0
;FILE lterrcete.o
LteEteFreeMobiFromEutranCmdNonCriticalExtension <= USED 0
LteEteHandleErrcActivateTestModeReq <= USED 0
LteEteHandleErrcDeactivateTestModeReq <= USED 0
LteEteHandleErrcLteModeReq <= USED 0
LteEteHandleUnitaryTestInit <= USED 0
LteEteSendEcphyEngInfoReq <= USED 0
LteEteSendEcphyStopEngInfoReq <= USED 0
LteRrcCheckCardNum <= USED 0
LteRrcCheckIfCsPagingOrImsPaging <= USED 0
LteRrcCopyAsn1Bits <= USED 0
LteRrcCopyLteCrsAssistanceInfo <= USED 0
LteRrcCopyLteMbsfnSubframeConfig <= USED 0
LteRrcGetTddFddMode <= USED 0
LteRrcHandleTimerExpiryTFastReturnGuard <= USED 0
LteRrcPhyConfigDedParaFitForNonCA <= USED 0
LteRrcReoderSignalSequence <= USED 0
LteRrcSendErrcDeactReq <= USED 0
LteRrcVarMemInit <= USED 0
;FILE lterrclogcxt.o
LteRrcHandleCellInfoLogConfig <= USED 0
LteRrcHandleStatisticsLogConfig <= USED 0
LteRrcUpdateChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateEstFailureStatistics <= USED 0
LteRrcUpdateEstStartStatistics <= USED 0
LteRrcUpdateEstSuccessStatistics <= USED 0
LteRrcUpdateNotChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateReconfCompleteStatistics <= USED 0
LteRrcUpdateReconfStartStatistics <= USED 0
LteRrcUpdateReestFailureStatistics <= USED 0
LteRrcUpdateReestStartStatistics <= USED 0
LteRrcUpdateReselFailureStatistics <= USED 0
LteRrcUpdateReselStartStatistics <= USED 0
LteRrcUpdateRlFailureStatistics <= USED 0
;FILE lterrcmcr.o
LteMcrChangeA3ConditionTimeToTrigger <= USED 0
LteMcrCheckIfNeedStartIratMeas <= USED 0
LteMcrCheckIfNeedTriggerA5 <= USED 0
LteMcrCheckIfNeedTriggerB2 <= USED 0
LteMcrCheckIfScellBad <= USED 0
LteMcrCheckIsCmccEsrvccImsStartForSpecialTac <= USED 0
LteMcrCheckMoreThan1DbForIrat <= USED 0
LteMcrCheckProximityEnteredForEutra <= USED 0
LteMcrConvertEcN0ToRxlev <= USED 0
LteMcrConvertRscpToRxlev <= USED 0
LteMcrInitYotaRxSib1Info <= USED 0
LteMcrIsBadQuality <= USED 0
LteMcrSetMinTimeToTriggerLeftInMs <= USED 0
;FILE lterrcsecurity.o
LteCerHandleSecurityConfigForHOFromUtra <= USED 0
LteCerUpdatesecurityConfigIOForHOFromUtra <= USED 0
;FILE lterrcsir.o
LteSirCopySib6V8h0Data <= USED 0
LteSirCopySib8Data <= USED 0
;FILE lterrctask.o
LteRrcTask1 <= USED 0
;FILE lterrctask2.o
LteRrcTask21 <= USED 0
;FILE lterrcutils.o
LteRrcCheckEnterConnected <= USED 0
LteRrcCheckIfCurrentTacMatched <= USED 0
LteRrcCsrGetOtherCardLteIsOos <= USED 0
LteRrcGetLogInfo <= USED 0
LteRrcGetLogInit <= USED 0
LteRrcGetSystemInformationBlock <= USED 0
LteRrcIsSelectionContextLte <= USED 0
;FILE lterrcwifi.o
;FILE lwip_api.o
lwip_debug_dumppbufs <= USED 0
lwip_get_adjust_pp_flag <= USED 0
lwip_get_debug_setting_by_name <= USED 0
lwip_get_default_nw6_ifname <= USED 0
lwip_get_default_nw_ifname <= USED 0
lwip_get_dns_api_cache_flag <= USED 0
lwip_get_dns_bind_port <= USED 0
lwip_get_dns_max_retries <= USED 0
lwip_get_dns_relay_server <= USED 0
lwip_get_dns_wait_tmr <= USED 0
lwip_get_ip_default_ttl <= USED 0
lwip_get_ip_default_ttl_in <= USED 0
lwip_get_ip_reass_maxage <= USED 0
lwip_get_ipnet_mcpy_shift <= USED 0
lwip_get_loopif_input_flag <= USED 0
lwip_get_nat_ttl_tcp_set <= USED 0
lwip_get_netif_by_hostname <= USED 0
lwip_get_netif_eth_ifname <= USED 0
lwip_get_netif_lte6_ifname <= USED 0
lwip_get_netif_pc_ifname <= USED 0
lwip_get_netif_wifi_ifname <= USED 0
lwip_get_nw_status <= USED 0
lwip_get_nw_status_ip6 <= USED 0
lwip_get_rndis_hotplug_flag <= USED 0
lwip_get_rs_backoff <= USED 0
lwip_get_send_ra_all_flag <= USED 0
lwip_get_tcp_backoff <= USED 0
lwip_get_tcp_msl_val <= USED 0
lwip_get_tcp_persist_backoff <= USED 0
lwip_get_tcp_rto_init <= USED 0
lwip_get_tcp_sync_backoff <= USED 0
lwip_get_wan_access_port <= USED 0
lwip_manul_adjust_pp <= USED 0
lwip_nwst_get_cutoff_data <= USED 0
lwip_nwst_get_reminder_data <= USED 0
lwip_nwst_set_callback <= USED 0
lwip_port_fwd_rule_add <= USED 0
lwip_print_tcpip_rate_all <= USED 0
lwip_print_tcpip_rate_default <= USED 0
lwip_reset_dns_relay_server <= USED 0
lwip_set_adjust_pp_flag <= USED 0
lwip_set_check_192_168_dongle <= USED 0
lwip_set_debug_setting <= USED 0
lwip_set_dhcpd_server_flag <= USED 0
lwip_set_dns_api_cache_flag <= USED 0
lwip_set_dns_relay_port <= USED 0
lwip_set_dns_relay_server <= USED 0
lwip_set_dns_wait_tmr <= USED 0
lwip_set_dns_wait_tmr_default <= USED 0
lwip_set_dnsr_hijack_flag <= USED 0
lwip_set_dongle_detect_cnt <= USED 0
lwip_set_ethip6_ns_pool_first <= USED 0
lwip_set_iot_test_mode <= USED 0
lwip_set_loopif_input_flag <= USED 0
lwip_set_mifi_flag <= USED 0
lwip_set_nat_ttl_tcp_set <= USED 0
lwip_set_ota_ip6addr_rsra_flag <= USED 0
lwip_set_ota_ip6addr_using_flag <= USED 0
lwip_set_reuseaddr_sp1_flag <= USED 0
lwip_set_rndis_hotplug_flag <= USED 0
lwip_set_rs_backoff <= USED 0
lwip_set_rs_backoff_default <= USED 0
lwip_set_rs_retry_counter <= USED 0
lwip_set_send_ra_all_flag <= USED 0
lwip_set_solinger_sp1_flag <= USED 0
lwip_set_tcp_ack_now_ <= USED 0
lwip_set_tcp_backoff <= USED 0
lwip_set_tcp_backoff_default <= USED 0
lwip_set_tcp_max_rtx <= USED 0
lwip_set_tcp_persist_backoff <= USED 0
lwip_set_tcp_persist_backoff_default <= USED 0
lwip_set_tcp_rto_init <= USED 0
lwip_set_tcp_rto_syn_factor <= USED 0
lwip_set_tcp_rto_update_flag <= USED 0
lwip_set_tcp_rto_update_value <= USED 0
lwip_set_tcp_syn_max_rtx <= USED 0
lwip_set_tcp_sync_backoff <= USED 0
lwip_set_tcp_sync_backoff_default <= USED 0
lwip_set_tcpip_rate <= USED 0
lwip_set_tcpip_rate_default <= USED 0
lwip_set_wan_access_port <= USED 0
;FILE lwip_atctl.o
lwip_atctl_ftp_help <= USED 0
lwip_atctl_ftp_process <= USED 0
lwip_atctl_fwdlist_query <= USED 0
lwip_atctl_ppset_process <= USED 0
lwip_atctl_ppset_query <= USED 0
lwip_atctl_rate_config <= USED 0
lwip_atctl_rate_query <= USED 0
lwip_atctl_utest_config <= USED 0
lwip_atctl_utest_query <= USED 0
;FILE lwip_customer.o
lwip_lte_require_check_defined <= USED 0
lwip_packet_to_modem <= USED 0
lwip_parameters_customer_defined <= USED 0
lwip_rate_control_buildup <= USED 0
;FILE lwip_init.o
;FILE lwip_minis.o
ip_flow_stats_er <= USED 0
ip_flow_stats_tx <= USED 0
lwip_get_dns_max_wait_time <= USED 0
lwip_get_ota_ip6addr_rsra_flag <= USED 0
lwip_get_ota_ip6addr_using_flag <= USED 0
lwip_get_tcp_max_rtx <= USED 0
lwip_get_tcp_rto_syn_factor <= USED 0
lwip_get_tcp_syn_max_rtx <= USED 0
lwip_get_wan_access_web_flag <= USED 0
lwip_lte_require_check <= USED 0
lwip_select_default_nw_ready <= USED 0
lwip_set_arp_mask_match_flag <= USED 0
lwip_set_dhcp6d_statefull_en <= USED 0
lwip_set_icmp_dont_frag_flag <= USED 0
lwip_set_mac_filter_flag <= USED 0
lwip_set_tcp_msl_val <= USED 0
lwip_set_wan_access_web_flag <= USED 0
nd6_rs_get_total_time <= USED 0
nd6_set_dns6 <= USED 0
net_pool_findall_by_nwif <= USED 0
netif_ppp_init <= USED 0
netif_ppp_remove <= USED 0
;FILE lwip_stats.o
_ip_flow_stats_er <= USED 0
_ip_flow_stats_tx <= USED 0
get_ip_flow_stats <= USED 0
netif_get_all_packets <= USED 0
netif_get_uap_packets <= USED 0
netif_get_usb_packets <= USED 0
netif_get_wifi_packets <= USED 0
;FILE lwip_sttest.o
lwip_sttest_delet_task_table <= USED 0
lwip_sttest_stmqtt_process <= USED 0
lwip_sttest_stwget_process <= USED 0
lwip_sttest_task1_entry <= USED 0
lwip_sttest_task2_entry <= USED 0
lwip_sttest_task3_entry <= USED 0
lwip_sttest_task4_entry <= USED 0
;FILE main.o
AIBAuxInit <= USED 0
Application_Initialize <= USED 0
DSPPhase1Init <= USED 0
DeleteInitPhase2Task <= USED 0
GetHslL1AcatLogFlag <= USED 0
GetIMLConfigStatus <= USED 0
GetIMLSettingFromNVM <= USED 0
InitSulogConfigToDSP <= USED 0
MSAInit <= USED 0
MSAPhase1Init <= USED 0
SET_DCDC_EN_HIGH <= USED 0
SetHSLPinMux <= USED 0
SetIMLSettingToNVM <= USED 0
boot_x1_from_ptcm <= USED 0
boot_x1_from_ptcm_new <= USED 0
bspSwResetReasonSet <= USED 0
copy_dsp_from_flash_to_psram <= USED 0
copy_rf_from_flash_to_psram <= USED 0
getAptFlagInMsaCfgNvm <= USED 0
nvm_rfs_sync_status <= USED 0
platformemptyTask_0 <= USED 0
platformemptyTask_1 <= USED 0
platformemptyTask_2 <= USED 0
setEehDumpDevType <= USED 0
setGLFeatureFlag <= USED 0
setStatisticValueInAcStatOptionNvm <= USED 0
;FILE mat_response.o
MATGetDataFromStr <= USED 0
MATGetStrFromData <= USED 0
_MATConfIndCB <= USED 0
flush_cfun4_rsp_cb <= USED 0
flush_cfun4_rsp_cb_sim2 <= USED 0
flush_cfun4_rsp_init <= USED 0
;FILE mat_token.o
MATCheckBitStreamToken <= USED 0
MATTokenDump <= USED 0
;FILE mcu_address_init.o
;FILE mcu_dsp_common.o
WebRtcNetEQ_DSP2MCUinterrupt <= USED 0
;FILE mcu_reset.o
;FILE md.o
mbedtls_md_clone <= USED 0
mbedtls_md_get_name <= USED 0
mbedtls_md_info_from_string <= USED 0
mbedtls_md_init_ctx <= USED 0
mbedtls_md_list <= USED 0
;FILE md5.o
mbedtls_md5 <= USED 0
mbedtls_md5_process <= USED 0
;FILE media_clk.o
;FILE media_format_type.o
_ZN14streamingmedia19transformToMimeTypeE15MediaFormatType <= USED 0
_ZN14streamingmedia23transformToVideoEncoderE15MediaFormatType <= USED 0
;FILE media_log.o
_Z23media_log_filter_removeN7android7String8E <= USED 0
__media_log_print <= USED 0
;FILE memleak.o
dbg_abort <= USED 0
dbg_catch_sigsegv <= USED 0
dbg_heap_dump <= USED 0
dbg_heap_dump_cmd <= USED 0
dbg_history_dump <= USED 0
dbg_mem_stat <= USED 0
dbg_zero_stat <= USED 0
;FILE memory_recorder.o
Sys_ims_memory_used <= USED 0
sys_set_force_dump_mem_use_once <= USED 0
;FILE mep.o
MEPPhase1Iinit <= USED 0
MEPPhase1Iinit_2 <= USED 0
MEP_UpdateSignature <= USED 0
MEP_UpdateToMRD <= USED 0
UDP_PutASL <= USED 0
UDP_PutASL_Callback <= USED 0
UdpInit <= USED 0
UdpInit_2 <= USED 0
;FILE min_max.o
max16 <= USED 0
max8 <= USED 0
min16 <= USED 0
min8 <= USED 0
;FILE mm.o
MmTask1 <= USED 0
;FILE mm_api.o
MM_AbortBandScan <= USED 0
MM_GetCfgRpmClr <= USED 0
MM_GetCfgRpmCounter <= USED 0
MM_GetCfgRpmPara <= USED 0
MM_GetCfgRpmSwitch <= USED 0
MM_GetIndStatus <= USED 0
MM_GetRpm <= USED 0
MM_GetTimeZoneReportOption <= USED 0
MM_SetCfgRpmClr <= USED 0
MM_SetCfgRpmPara <= USED 0
MM_SetCfgRpmSwitch <= USED 0
MM_SetWbCellLock <= USED 0
getSavedSignal <= USED 0
getSavedSysMode <= USED 0
getTotalSuppOpeNum <= USED 0
resetTotalSuppOpeNum <= USED 0
;FILE mm_api_mini.o
resetMmParas <= USED 0
;FILE mm_as.o
MmDualGrrChekDMmIsMmInDedicate <= USED 0
MmDualGrrChekMmIsDMmInDedicate <= USED 0
MmHasPendingPlmnSearch <= USED 0
;FILE mm_mmr.o
MmSendMmrFlushFlashEnd <= USED 0
MmSendMmrFlushFlashReq <= USED 0
;FILE mm_sim.o
;FILE mm_utils.o
MmDualIsDmmPendingMtCall <= USED 0
MmDualIsDmmPsSuspend <= USED 0
MmDualMmIsDMmLiveDedicate <= USED 0
MmGetCurrentMobileId <= USED 0
MmIsInCMCCNetWork <= USED 0
MmIsInCUCCNetWork <= USED 0
MmIsIratToLteAllowed <= USED 0
MmPendEmmEsmUnitDataReq <= USED 0
mmEmeiRead <= USED 0
nvm_flush_ps_release <= USED 0
nvm_flush_ps_req <= USED 0
;FILE modem_controller.o
modem_no_carrier_finish_timeout <= USED 0
modem_router <= USED 0
pppSetInProcessSizeMax <= USED 0
ppp_fallback_control_interval_ticks <= USED 0
ppp_handle_at <= USED 0
ppp_handle_pdp_params <= USED 0
ppp_update_iptype <= USED 0
send_creg <= USED 0
wait_for_crlf_timer_cb <= USED 0
;FILE modem_mgr.o
;FILE modem_platform.o
ppp_get_instant_statistics <= USED 0
ppp_get_statistics <= USED 0
ppp_get_statistics_rx <= USED 0
ppp_get_statistics_tx <= USED 0
ppp_in_active_stage <= USED 0
ppp_save_statistics <= USED 0
ppp_set_connect_flag <= USED 0
wait_for_statistics_timer_cb <= USED 0
;FILE modemlwip.o
ims_get_register_flag <= USED 0
ipnet_downlink_get_allmem <= USED 0
ipnet_downlink_get_usemem <= USED 0
ipnet_uplink_transfer_ind <= USED 0
lwip_downlink_is_reach_hwm <= USED 0
usb_uplink_is_reach_hwm <= USED 0
;FILE modemvolte.o
ModemCiPrimitiveCnfFileter <= USED 0
ModemDlIpFilterToLte <= USED 0
ModemIsVolteUplinkDataInQueue <= USED 0
ModemParserCidNeedToVolte <= USED 0
ModemQuiryBearTypeFormSecondID <= USED 0
ModemVolteRxDataPoll <= USED 0
ModemVolteRxDataRelease <= USED 0
ModemVolteTxPacketGet <= USED 0
ModemVolteTxPacketPut <= USED 0
ModemVolteTxPacketPutAll <= USED 0
VoLteTxProcessCallBackRegister <= USED 0
VoLteTxProcessDone <= USED 0
VoLteUpLinkPacketAlloc <= USED 0
VoLteUpLinkPacketFree <= USED 0
add_ipv4_filter_rule <= USED 0
add_ipv6_filter_rule <= USED 0
getVolteCid <= USED 0
isInVolteBuffer <= USED 0
remove_ip_filter_rule <= USED 0
;FILE mp3dec_api.o
mp3PlayStopWithName <= USED 0
mp3Stop <= USED 0
;FILE mp3dec_impl.o
mp3_decode_get_pcm <= USED 0
;FILE mp3reader.o
_ZN9Mp3Reader5closeEv <= USED 0
;FILE mpu.o
;FILE msg_api.o
MSG_CRSM_DeleteMsg <= USED 0
MSG_CRSM_ReadMsg <= USED 0
MSG_CRSM_WriteMsgWithIndex <= USED 0
MSG_NewMsgAck <= USED 0
MSG_RSTMemFull <= USED 0
libDecodeGsm7BitData <= USED 0
libEncodeGsm7BitData <= USED 0
parseCBM <= USED 0
resetMsgOperFlag <= USED 0
resetMsgParas <= USED 0
;FILE msg_api_mini.o
telMsgIsSmsDataModeOn <= USED 0
;FILE mult_r.o
;FILE multi_irat_common.o
multiIratGetModeReportSourceAppId <= USED 0
multiIratL1GetRAT <= USED 0
;FILE multipart.o
MultiPart_GetDiscardText <= USED 0
;FILE multipart_utils.o
multiPart_FormatDiscardText <= USED 0
;FILE mvUsbDevCh9.o
ch9Class <= USED 0
mvUsbCh9ProcessVendorRequest <= USED 0
;FILE mvUsbDevMain.o
_usb_device_deinit_endpoint <= USED 0
_usb_device_get_handle <= USED 0
_usb_device_get_max_endpoint <= USED 0
_usb_device_get_transfer_details <= USED 0
_usb_device_shutdown <= USED 0
_usb_device_trace_dtd_information <= USED 0
;FILE mvUsbDevRecv.o
;FILE mvUsbDevSend.o
;FILE mvUsbDevUtl.o
_usb_debug_get_flags <= USED 0
_usb_debug_init_trace_log <= USED 0
_usb_debug_print_trace_log <= USED 0
_usb_device_assert_resume <= USED 0
_usb_device_unstall_endpoint <= USED 0
_usb_dump_regs <= USED 0
_usb_ep_status <= USED 0
;FILE mvUsbHsDevCncl.o
;FILE mvUsbHsDevMain.o
_usb_dci_vusb20_deinit_endpoint <= USED 0
_usb_dci_vusb20_diag_tx_isr <= USED 0
_usb_dci_vusb20_get_transfer_details <= USED 0
_usb_dci_vusb20_remote_wakeup <= USED 0
_usb_dci_vusb20_shutdown <= USED 0
_usb_dci_vusb20_stop_transter <= USED 0
_usb_dci_vusb20_suspend_tx_transter <= USED 0
_usb_dci_vusb20_trace_dtd_information <= USED 0
;FILE mvUsbHsDevUtl.o
_usb_dci_vusb20_assert_resume <= USED 0
;FILE mvUsbLog.o
mvUsbLogGetInfo <= USED 0
mvUsbLogSaveToFileSystem <= USED 0
mvUsbLoggingUninit <= USED 0
;FILE mvUsbMemory.o
;FILE mvUsbModem.o
mvUsbModemClearDummyFlag_remotekwakeup <= USED 0
mvUsbNotifySerialState_DCD <= USED 0
mvUsbNotifySerialState_RI <= USED 0
mvUsbSetModemParameters <= USED 0
;FILE mvUsbNet.o
MbimConnectionSpeedChangeNotification <= USED 0
MbimNetworkConnectionNotification <= USED 0
MbimSendRspAvailableNotification <= USED 0
mvUsbCIDRespFromMbim <= USED 0
mvUsbNetCtrlHISR <= USED 0
mvUsbNetCtrlTxLISR <= USED 0
mvUsbNetFreeTxBuffer <= USED 0
mvUsbNetMacCmp <= USED 0
mvUsbNetParasInit <= USED 0
mvUsbNetRemoveHdr <= USED 0
mvUsbNetReqTask <= USED 0
mvUsbNetResetParameters <= USED 0
mvUsbNetResetRingBuffer <= USED 0
mvUsbNetResetRspQ <= USED 0
mvUsbNetRxHISR <= USED 0
mvUsbNetRxLISR <= USED 0
mvUsbNetTxDequeueAndTransfer <= USED 0
mvUsbNetTxHISR <= USED 0
mvUsbNetTxLISR <= USED 0
mvUsbQueueRXPacketTask <= USED 0
mvUsbRingbufAlloc <= USED 0
mvUsbSetNetType <= USED 0
;FILE nas_cfg.o
;FILE nat_gre.o
;FILE nat_pptp.o
pptp_info_tmr <= USED 0
;FILE nd6.o
_nd6_rs_get_total_time <= USED 0
_nd6_set_dns6 <= USED 0
nd6_get_prefix_length <= USED 0
ns_ip6_pool_get_linkaddr <= USED 0
reset_netif_ipv6_flg_all <= USED 0
reset_netif_ipv6_flg_part <= USED 0
;FILE netIntfListener.o
NetIf_RemoveRouteEx <= USED 0
NetIntf_StartListenOn <= USED 0
NetIntf_StopListenOn <= USED 0
;FILE net_bridge.o
lwip_set_mac_nwif_table <= USED 0
net_pool_fetch_head <= USED 0
net_pool_fetch_size <= USED 0
;FILE net_pkt.o
;FILE net_sockets.o
mbedtls_net_accept <= USED 0
mbedtls_net_bind <= USED 0
mbedtls_net_poll <= USED 0
mbedtls_net_recv_timeout <= USED 0
mbedtls_net_set_block <= USED 0
mbedtls_net_set_nonblock <= USED 0
mbedtls_net_usleep <= USED 0
;FILE netbuf.o
netbuf_alloc <= USED 0
netbuf_chain <= USED 0
netbuf_data <= USED 0
netbuf_first <= USED 0
netbuf_new <= USED 0
netbuf_next <= USED 0
;FILE netdb.o
lwip_get_dns_ttl <= USED 0
lwip_gethostbyname_r <= USED 0
lwip_gethostbyname_r_with_netif <= USED 0
lwip_gethostbyname_r_with_pcid <= USED 0
lwip_gethostbyname_with_pcid <= USED 0
lwip_set_dns_ttl <= USED 0
;FILE neteq_rtp.o
;FILE netif.o
_lwip_set_netif_tune <= USED 0
lwip_build_netif_tune_ifname <= USED 0
lwip_get_netif_pdp_if <= USED 0
lwip_set_netif_tune <= USED 0
netif_check_nw_status <= USED 0
netif_check_nw_status_ip6 <= USED 0
netif_create_ip6_linklocal_address <= USED 0
netif_find_by_cid <= USED 0
netif_find_by_name <= USED 0
netif_find_by_scid <= USED 0
netif_find_by_scid_dsim <= USED 0
netif_get_eth_if <= USED 0
netif_get_status <= USED 0
netif_get_status_by_cid <= USED 0
netif_local_if_exist <= USED 0
netif_nw_router_check <= USED 0
netif_pc_get_mac <= USED 0
netif_ppp_get_status <= USED 0
netif_ppp_set_status <= USED 0
netif_remove <= USED 0
netif_remove_bypass <= USED 0
netif_remove_config <= USED 0
netif_remove_config_ip6 <= USED 0
netif_remove_default_nw_ppp <= USED 0
netif_set_bypass <= USED 0
netif_set_default_nw_ppp <= USED 0
netif_set_default_nw_status <= USED 0
netif_set_default_nw_status_ip6 <= USED 0
netif_set_lan <= USED 0
netif_set_link_down <= USED 0
netif_set_mac <= USED 0
netif_set_remove_callback <= USED 0
netif_set_wan <= USED 0
netif_share_nw_exist <= USED 0
netif_tune_buf_bind <= USED 0
netif_tune_buf_free <= USED 0
netif_tune_input <= USED 0
netif_tune_output_ipv4 <= USED 0
netif_tune_output_ipv6 <= USED 0
netif_uap_get_mac <= USED 0
netif_uap_get_status <= USED 0
netif_uap_set_status <= USED 0
netif_wifi_status <= USED 0
;FILE netif_pc.o
mvUsbNetGetPacket <= USED 0
netifapi_set_pc_macaddr <= USED 0
;FILE netif_ppp.o
_netif_ppp_init <= USED 0
_netif_ppp_remove <= USED 0
netif_ppp_reset_ip <= USED 0
pppnet_input_bypass <= USED 0
;FILE netif_td.o
_lwip_set_pdpinfo <= USED 0
lte_construct_data_for_lte <= USED 0
lte_dl_mem_free_for_wifi <= USED 0
lte_low_output_list <= USED 0
lte_low_output_statis <= USED 0
lte_set_ipv4_info <= USED 0
lte_set_ipv6_info <= USED 0
lte_set_temp_info <= USED 0
lte_ul_buf_alloc <= USED 0
lwip_set_scid_table_dsim <= USED 0
netif_get_default_nw_ip6addr <= USED 0
netif_pdp_output <= USED 0
netif_pdp_output_ipv6 <= USED 0
netif_td_send_data <= USED 0
td_get_packet <= USED 0
td_get_rx_speed <= USED 0
td_get_tx_speed <= USED 0
;FILE netif_wifi_uap.o
crane_wifi_to_lwip <= USED 0
netif_recv_from_wifi <= USED 0
netifapi_set_wifi_macaddr <= USED 0
;FILE netifapi.o
netifapi_netif_set_addr <= USED 0
netifapi_netif_set_mac <= USED 0
;FILE netifconfig.o
_Z16EnableInfterfaceb <= USED 0
_Z20addInferfaceBearerIdi <= USED 0
_Z23removeInferfaceBearerIdi <= USED 0
_Z24net_add_ipv4_filter_rulehjtjt <= USED 0
_Z24net_add_ipv6_filter_rulehPhtS_t <= USED 0
_Z25net_remove_ip_filter_rulei <= USED 0
;FILE network_selection.o
;FILE ntp.o
GetNtpResult <= USED 0
NTPTask <= USED 0
cm_sync_ntp_time <= USED 0
create_timeval <= USED 0
current_time <= USED 0
ntp_client <= USED 0
read_packet <= USED 0
sync_ntp <= USED 0
unpack_ntp <= USED 0
write_packet <= USED 0
;FILE nv_property.o
ims_api_psram_GetImsEnable <= USED 0
;FILE oid.o
mbedtls_oid_get_attr_short_name <= USED 0
mbedtls_oid_get_certificate_policies <= USED 0
mbedtls_oid_get_cipher_alg <= USED 0
mbedtls_oid_get_extended_key_usage <= USED 0
mbedtls_oid_get_md_alg <= USED 0
mbedtls_oid_get_md_hmac <= USED 0
mbedtls_oid_get_numeric_string <= USED 0
mbedtls_oid_get_oid_by_ec_grp <= USED 0
mbedtls_oid_get_oid_by_pk_alg <= USED 0
mbedtls_oid_get_oid_by_sig_alg <= USED 0
mbedtls_oid_get_sig_alg_desc <= USED 0
;FILE ol_ltp.o
;FILE opencpu_uart.o
;FILE osa_common_init.o
OSAMailboxQCreate <= USED 0
OSAMemPoolCreateExt <= USED 0
OSASemaphoreCreateExt <= USED 0
OsaGetMaxThreadCount <= USED 0
OsaGetVersion <= USED 0
;FILE osa_common_run.o
;FILE osa_mem.o
OsaMemAllocAgain <= USED 0
OsaMemGetFirstPoolRef <= USED 0
OsaMemGetUserParam <= USED 0
OsaMemPoolsCheckValidity <= USED 0
OsaMemSetUserParam <= USED 0
OsaMem_InitPools <= USED 0
;FILE osa_net_if.o
OSANetif_GetIPv6Info <= USED 0
OSA_Arp_Add <= USED 0
OSA_Arp_Remove <= USED 0
OSA_NetIF_Register_Cb <= USED 0
OSA_NetIf_Details_Get <= USED 0
OSA_NetIf_Details_Set <= USED 0
OSA_NetIf_Disable <= USED 0
OSA_NetIf_Down <= USED 0
OSA_NetIf_Enumerate <= USED 0
OSA_NetIf_Get2AdaptName <= USED 0
OSA_NetIf_Get_IPv4_Address <= USED 0
OSA_NetIf_Init <= USED 0
OSA_NetIf_Is_Interface_Up <= USED 0
OSA_NetIf_Set_IPv4_Address <= USED 0
OSA_NetIf_Set_IPv6_Address <= USED 0
OSA_NetIf_Set_MTU <= USED 0
OSA_NetIf_Shudown <= USED 0
OSA_NetIf_StatusToStr <= USED 0
OSA_NetIf_Status_Get <= USED 0
OSA_NetIf_Up <= USED 0
OSA_Netif_Get_IPv6_Address <= USED 0
OSA_Route_Add <= USED 0
OSA_Route_Remove <= USED 0
;FILE osa_tx_init.o
OsaCriticalSectionDelete <= USED 0
OsaHISRGetPriority <= USED 0
OsaInit <= USED 0
OsaMailboxQCreate <= USED 0
OsaMailboxQDelete <= USED 0
OsaRun <= USED 0
OsaTaskStackMagic <= USED 0
;FILE osa_tx_run.o
GetOvershootTicks <= USED 0
OsaContextLockExt <= USED 0
OsaContextRestoreExt <= USED 0
OsaControlInterrupts <= USED 0
OsaMailboxQRecv <= USED 0
OsaMailboxQSend <= USED 0
OsaSemaphoreReleaseExt <= USED 0
OsaTick <= USED 0
OsaTickSuspend <= USED 0
OsaTickUpdate <= USED 0
Osa_Init <= USED 0
ResetOvershootTicks <= USED 0
;FILE osa_tx_utils.o
OSAHISRGetAppParam1 <= USED 0
OSAHISRGetEntry <= USED 0
OSAHISRGetName <= USED 0
OSAHISRGetStackSize <= USED 0
OSAHISRGetStackStart <= USED 0
OSAHISRList <= USED 0
OSAHISRSetAppParam1 <= USED 0
OSAPartitionInUse <= USED 0
OSAPartitionPoolGetAllocated <= USED 0
OSATaskGetEntry <= USED 0
OSATaskGetEntryParam <= USED 0
OSATaskGetStackSize <= USED 0
OSATaskGetSysParam2 <= USED 0
OSATaskList <= USED 0
OSATaskSetSysParam2 <= USED 0
OsaGetThreadListHead <= USED 0
OsaHISRGetCurrentRef <= USED 0
OsaListAllCreatedMutexs <= USED 0
OsaThreadList <= USED 0
;FILE osa_utils.o
;FILE p_ol_wgh.o
;FILE packet_buffer.o
WebRtcNetEQ_PacketBufferExtract <= USED 0
WebRtcNetEQ_PacketBufferFindLowestTimestamp <= USED 0
WebRtcNetEQ_PacketBufferGetDuration <= USED 0
;FILE pap.o
;FILE pb_api.o
PB_CRSM_DeleteEntry <= USED 0
PB_CRSM_ReadOneEntry <= USED 0
PB_CRSM_WriteEntry <= USED 0
PB_SQueryRead <= USED 0
PB_SQueryWrite <= USED 0
PB_SWriteEntry <= USED 0
decode_alpha_tag <= USED 0
encode_alpha_tag <= USED 0
encode_character <= USED 0
libGetSubaddrInfo <= USED 0
resetPbParas <= USED 0
;FILE pb_api_mini.o
;FILE pbuf.o
lwip_wifi_in_buf_alloc <= USED 0
lwip_wifi_in_buf_free <= USED 0
lwip_wifi_in_buf_pmsg <= USED 0
pbuf_add_header <= USED 0
pbuf_coalesce <= USED 0
pbuf_dechain <= USED 0
pbuf_fill_chksum <= USED 0
pbuf_get_at <= USED 0
pbuf_memcmp <= USED 0
pbuf_memfind <= USED 0
pbuf_strstr <= USED 0
pbuf_take <= USED 0
;FILE pcpsha1ca.o
;FILE pcscf_discovery.o
;FILE pdcpgki.o
;FILE pem.o
;FILE ph_disp.o
;FILE ping.o
api_lwip_do_ping <= USED 0
api_lwip_do_ping_ex <= USED 0
;FILE pitch_fr.o
;FILE pitch_ol.o
_Z15Lag_max_wrapperP9vadState1PiPssssssS2_iS1_ <= USED 0
;FILE pk.o
mbedtls_pk_check_pair <= USED 0
mbedtls_pk_debug <= USED 0
mbedtls_pk_get_name <= USED 0
mbedtls_pk_setup_rsa_alt <= USED 0
;FILE pkparse.o
mbedtls_pk_parse_public_key <= USED 0
;FILE platform.o
PlatformCSDEnable <= USED 0
PlatformChargeIsEnable <= USED 0
PlatformGetI2CType <= USED 0
PlatformI2CInit <= USED 0
PlatformIsLtgVersion <= USED 0
PlatformIsNezhaC <= USED 0
PlatformIsSDKVersion <= USED 0
PlatformIsTPMifi <= USED 0
PlatformIsWukong <= USED 0
PlatformKeypadIsEnable <= USED 0
PlatformOledIsEnable <= USED 0
PlatformRTCIsEnable <= USED 0
PlatformSetCustomData <= USED 0
PlatformSetPMICType <= USED 0
PlatformWapiIsEnable <= USED 0
PlatformWebIsEnable <= USED 0
PlatformWifiIsEnable <= USED 0
Platform_sdio_config_pin <= USED 0
mbedtls_platform_set_calloc_free <= USED 0
mbedtls_platform_setup <= USED 0
mbedtls_platform_teardown <= USED 0
;FILE platform_intf.o
;FILE platform_nvm.o
PlatformNvm_CreateAciCfgFile <= USED 0
PlatformNvm_Init <= USED 0
;FILE platform_pmu.o
;FILE platform_shim_api.o
CsdQueryTestStatus <= USED 0
CsdTurnOffTest <= USED 0
CsdTurnOnTest <= USED 0
EraseFlash <= USED 0
GetCurrentCompTime <= USED 0
GetFlashLayoutConfig <= USED 0
GetLocalTime <= USED 0
GetWiFiType <= USED 0
GetWifiStatus <= USED 0
Get_backtime_frompsm <= USED 0
Giga_Disable4BytesMode <= USED 0
Giga_Enable4BytesMode <= USED 0
IsGsmRatForMIFI <= USED 0
Is_dedicateAPN <= USED 0
LteConstructSnDataNodeEForMifi <= USED 0
NingboKeypadBackLightDisable <= USED 0
NingboKeypadBackLightEnable <= USED 0
NingboLcdBackLightCtrl <= USED 0
NingboLcdBackLightDisable <= USED 0
NingboLcdBackLightEnable <= USED 0
NingboTorchBackLightCtrl <= USED 0
NingboTorchLightDisable <= USED 0
NingboTorchLightEnable <= USED 0
NingboVibratorCtrl <= USED 0
NingboVibratorDisable <= USED 0
NingboVibratorEnable <= USED 0
Ningbo_ENABLE_BATID_MEAS <= USED 0
Ningbo_ENABLE_BATTEMP_MEAS <= USED 0
Ningbo_Get_Meas_Factor <= USED 0
Ningbo_LDO_Set_Enable <= USED 0
Ningbo_LDO_Set_VOUT <= USED 0
Ningbo_Ldo_11_set <= USED 0
Ningbo_Ldo_12_set <= USED 0
Ningbo_Ldo_12_set_1_8 <= USED 0
Ningbo_Ldo_12_set_2_8 <= USED 0
Ningbo_Ldo_1_set <= USED 0
Ningbo_Ldo_1_set_2_8 <= USED 0
OsaTimerExcludedUnRegister <= USED 0
PM812_BATTERY_BIND_INTC <= USED 0
PM812_BATTERY_INTC_ENABLE <= USED 0
PM812_BATTERY_STATUS <= USED 0
PM812_CHARGER_BIND_INTC <= USED 0
PM812_CHARGER_INTC_ENABLE <= USED 0
PM812_ENABLE_BAT_DET <= USED 0
Rdisk_FlashRead <= USED 0
ReadFlash <= USED 0
SaveCalDataFromMfg <= USED 0
SulogOffControlForUsbRemove <= USED 0
UIRefresh_Throughput_Change <= USED 0
Update_RTC_Time <= USED 0
VgRtfdpTask <= USED 0
VgUfdpTask <= USED 0
WIFI_Sendto_Wapi <= USED 0
WIFI_Sendto_Wps <= USED 0
WriteFlash <= USED 0
asrbt_main <= USED 0
bt_wakelock_status <= USED 0
emac_start_xmit <= USED 0
embms_data_input <= USED 0
get_d2_nvm_flush <= USED 0
get_download_flag_for_tigx <= USED 0
get_uart2_baudrate_for_tigx <= USED 0
init_sgLed <= USED 0
isAutoApn <= USED 0
is_aes_cipher <= USED 0
pm813_cc_current_set <= USED 0
pm813_cccv_timer_set <= USED 0
pm813_charge_termination_current_set <= USED 0
pm813_charger_is_usb <= USED 0
pm813_charger_switch <= USED 0
pm813_fault_clear <= USED 0
pm813_get_Icharge_meas_cur_mA <= USED 0
pm813_get_Vcharge_meas_vol_mv <= USED 0
pm813_get_batid_meas_vol_mv <= USED 0
pm813_get_battemp_meas_vol_mv <= USED 0
pm813_get_battery_status <= USED 0
pm813_get_battery_voltage_withoutCharger <= USED 0
pm813_get_charger_FSM_state <= USED 0
pm813_if_charge_full <= USED 0
pm813_if_charge_in_cv_mode <= USED 0
pm813_keypadbacklight_status_get <= USED 0
pm813_lcdbacklight_status_get <= USED 0
pm813_measured_current_means_charging <= USED 0
pm813_precharge_current_set <= USED 0
pm813_precharge_timer_set <= USED 0
pm813_set_max_cc_current <= USED 0
pm813_torchlight_status_get <= USED 0
pm813_trickle_timer_set <= USED 0
pm813_vbat_set <= USED 0
pm813_vibrator_status_get <= USED 0
pm813s_mppt_restart <= USED 0
record_flush_write <= USED 0
set_download_flag_for_tigx <= USED 0
set_uart2_baudrate_for_tigx <= USED 0
set_uart_selfadapt <= USED 0
td_send_packet <= USED 0
uap_packet_free <= USED 0
usb_net_init <= USED 0
usb_net_rx <= USED 0
usb_net_tx <= USED 0
vgRlpSetRlpInitiateXidNeg <= USED 0
wan_delete_device <= USED 0
watchdog_reset <= USED 0
woal_uap_do_ioctl <= USED 0
;FILE platform_util.o
;FILE plms.o
PlmsCheckIfFgPlmnOngoing <= USED 0
PlmsCheckIsChina <= USED 0
PlmsConvertMibBW <= USED 0
PlmsPrintCheckSearchInfoDb <= USED 0
PlmsTask1 <= USED 0
;FILE plmsdb.o
PlmsDbInitInterferenceDetection <= USED 0
PlmsDbIsPscInUmtsFindCellCnf <= USED 0
PlmsDbStoreJammingDetectionParams <= USED 0
PlmsStoreSeqNum <= USED 0
;FILE plmsfnd.o
PlmsFndCalcInterferedCarriersNum <= USED 0
PlmsFndExcludeEarfcnFromSearch <= USED 0
;FILE plmsscn.o
PlmsGetOperator <= USED 0
PlmsLogResultTable <= USED 0
PlmsScnAllocationWbScoreResultTable <= USED 0
PlmsScnCheckIfArfcnBelongToRequestPlmn <= USED 0
PlmsScnDetermineL1ForFGRssi <= USED 0
;FILE plmssi.o
;FILE pm.o
;FILE pm_debug.o
pmLogInit <= USED 0
;FILE pmic.o
Camera_Avdd_Power_set <= USED 0
Camera_Dvdd_Power_set <= USED 0
Camera_IOvdd_Power_set <= USED 0
I2CDeviceCheckStatus <= USED 0
I2CRecordDeviceStatus <= USED 0
PM812SetLDO3OffOnSleep <= USED 0
PM812_32K_OUT2_Enable <= USED 0
PM812_APT_enable <= USED 0
PM812_BATTERY_IS_DETECTED <= USED 0
PM812_BD_DISABLE <= USED 0
PM812_BD_ENABLE <= USED 0
PM812_BIAS_DISABLE <= USED 0
PM812_BIAS_ENABLE <= USED 0
PM812_BIAS_OUT_OFF <= USED 0
PM812_BIAS_OUT_ON <= USED 0
PM812_BUCK5_Enable <= USED 0
PM812_GET_LONKEY_PRESS_TIME <= USED 0
PM812_GET_POWER_DOWN_REASON <= USED 0
PM812_GET_POWER_DOWN_REASON2 <= USED 0
PM812_GET_POWER_UP_REASON <= USED 0
PM812_GET_VCHG_LOW_TH <= USED 0
PM812_GET_VCHG_UPP_TH <= USED 0
PM812_GPADC_NONSTOP_TRIG_DISABLE <= USED 0
PM812_GPADC_NONSTOP_TRIG_ENABLE <= USED 0
PM812_GPADC_READ_VOL_MEAS <= USED 0
PM812_GPADC_SW_TRIG <= USED 0
PM812_INT_CALLBACK_REGISTER <= USED 0
PM812_INT_DISABLE <= USED 0
PM812_INT_ENABLE <= USED 0
PM812_LONKEY_ENABLE_GET <= USED 0
PM812_Ldo_10_set <= USED 0
PM812_Ldo_10_set_1_8 <= USED 0
PM812_Ldo_10_set_3_0 <= USED 0
PM812_Ldo_11_set <= USED 0
PM812_Ldo_11_set_2_8 <= USED 0
PM812_Ldo_12_set_2_8 <= USED 0
PM812_Ldo_12_set_3_0 <= USED 0
PM812_Ldo_13_set_2_8 <= USED 0
PM812_Ldo_15_set <= USED 0
PM812_Ldo_15_set_2_8 <= USED 0
PM812_Ldo_16_set <= USED 0
PM812_Ldo_16_set_2_8 <= USED 0
PM812_Ldo_17_set <= USED 0
PM812_Ldo_17_set_2_8 <= USED 0
PM812_Ldo_18_set <= USED 0
PM812_Ldo_18_set_1_8 <= USED 0
PM812_Ldo_19_Config <= USED 0
PM812_Ldo_3_set <= USED 0
PM812_Ldo_3_set_2_8 <= USED 0
PM812_Ldo_4_set <= USED 0
PM812_Ldo_4_set_1_8 <= USED 0
PM812_Ldo_4_set_3_0 <= USED 0
PM812_Ldo_5_set <= USED 0
PM812_Ldo_5_set_3_1 <= USED 0
PM812_Ldo_6_set <= USED 0
PM812_Ldo_6_set_1_8 <= USED 0
PM812_Ldo_6_set_2_8 <= USED 0
PM812_Ldo_8_set <= USED 0
PM812_Ldo_8_set_2_8 <= USED 0
PM812_Ldo_9_set <= USED 0
PM812_Ldo_9_set_1_8 <= USED 0
PM812_MEAS_DISABLE <= USED 0
PM812_MEAS_ENABLE <= USED 0
PM812_PowerSave_Config <= USED 0
PM812_PwmSetLevel <= USED 0
PM812_REG_DUMP <= USED 0
PM812_RTC_SET_CONTROL_REG <= USED 0
PM812_RTC_USE_XO <= USED 0
PM812_SET_VCHG_LOW_TH <= USED 0
PM812_SET_VCHG_UPP_TH <= USED 0
PM812_SET_WD_TIMEOUT_ACT <= USED 0
PM812_VBUCK1_CFG <= USED 0
PM812_VBUCK2_CFG <= USED 0
PM812_VBUS_IS_AVAILABLE <= USED 0
PM812_VLDO7_SWITCH <= USED 0
PM812_WD_ENABLE <= USED 0
PM812_WD_MODE <= USED 0
PM812_WD_RESET <= USED 0
PM812_bk4clkfll_set_2m <= USED 0
PM812_vbuck1_set_fpwm <= USED 0
PM812_vbuck3_set_1_25 <= USED 0
PM812_vbuck4_set_1_85 <= USED 0
PM812_vbuck4_set_2_00 <= USED 0
PM812_vbuck4_set_fpwm <= USED 0
PMCGetGPADCValue <= USED 0
PMCNotifyEventBind <= USED 0
PMCWriteRegister <= USED 0
PMIC_IS_PM813S_A0 <= USED 0
PMIC_IS_PM813_A2 <= USED 0
PMIC_IS_PM813_A3 <= USED 0
PMIC_Phase1Init <= USED 0
PMIC_PowerDown <= USED 0
PMIC_READ_REG_AUDIO <= USED 0
PMIC_READ_REG_TEST <= USED 0
PMIC_RTC_GET_EXPIR_1_ENABLE_STATE <= USED 0
PMIC_WRITE_REG_AUDIO <= USED 0
PMIC_WRITE_REG_TEST <= USED 0
PlatformVcoreConfigForDro <= USED 0
PlatformVcoreConfigHigh <= USED 0
PlatformVcoreConfigLow <= USED 0
PlatformVcoreConfigTop <= USED 0
SysRestartReasonGetGlobal <= USED 0
isLcdEverInitedUponBooting <= USED 0
isSysRestartByAlarm <= USED 0
isSysRestartByCharging <= USED 0
isSysRestartByError <= USED 0
isSysRestartByNormal <= USED 0
pmic_lcdbacklight_current_status_get <= USED 0
pmic_lcdbacklight_level_get <= USED 0
pmic_set_sys_restart_reason <= USED 0
pmic_user_defined_flags_get <= USED 0
pmic_user_defined_flags_set <= USED 0
pmic_userdata_get_app_lcd_ever_configed <= USED 0
pmic_userdata_set_app_lcd_ever_configed <= USED 0
set_hp1n2 <= USED 0
ustica_I2CEnableclockandPin <= USED 0
ustica_I2CInit <= USED 0
ustica_USB_shutdown <= USED 0
ustica_USB_turnOn <= USED 0
ustica_vbuck1_0_set <= USED 0
;FILE pmic_exton.o
;FILE pmic_onkey.o
OnKeyPoweroffDone <= USED 0
PM812_LONKEY_PRESS_TIME_SET <= USED 0
PM812_onkey_int_test_callback <= USED 0
Poweroff_disable_Int <= USED 0
;FILE pmic_rtc.o
PMIC_RTC_IS_ALARM_POWERUP <= USED 0
PMIC_RTC_IS_SYS_RTC_PEER_SYNCED <= USED 0
PMIC_RTC_tm_to_str <= USED 0
pmic_rtc_INIT_FLAG_SET <= USED 0
pmic_rtc_offset_storage_type_detect <= USED 0
pmic_rtc_print_tm <= USED 0
pmic_rtc_setting_dump <= USED 0
pmic_userdata_unset_app_rtc_ever_configed <= USED 0
;FILE pmic_wdt.o
;FILE pmu_stub.o
PMUDSPReset <= USED 0
;FILE poly1305.o
mbedtls_poly1305_mac <= USED 0
;FILE post_pro.o
;FILE pow2.o
;FILE ppp.o
PppDeinitStack <= USED 0
PppInitStack <= USED 0
PppMessageReq <= USED 0
PppRelayMessageFromComm <= USED 0
PppResetStack <= USED 0
PppSetCid <= USED 0
PppUpdateIpParams <= USED 0
;FILE ppp_platform.o
ppp_create_msg_queue <= USED 0
ppp_create_task <= USED 0
ppp_delete_msg_queue <= USED 0
ppp_delete_timer <= USED 0
ppp_init_mutex <= USED 0
ppp_lock_mutex <= USED 0
ppp_send_msg <= USED 0
ppp_set_trace <= USED 0
ppp_unlock_mutex <= USED 0
;FILE pre_big.o
;FILE pre_proc.o
;FILE pred_lt.o
;FILE preemph.o
;FILE prm2bits.o
;FILE prm_hrbl.o
PRMResourceStatusGet <= USED 0
PRMSWReset <= USED 0
PRMServiceFreqGet <= USED 0
PRMServiceFreqSet <= USED 0
prmFreqRetCode <= USED 0
prmRetCode <= USED 0
;FILE process_dialogmsg.o
API_SIP_DM_IsSubPresent <= USED 0
API_SIP_DM_SUBS_INCR <= USED 0
API_SIP_DM_SUBS_MINUS <= USED 0
sipDM_GetDialogInState <= USED 0
;FILE property.o
PROP_FindKeyById <= USED 0
PROP_GetProperty <= USED 0
;FILE ps_api.o
PS_GetCapsVoiceMode <= USED 0
PS_GetTFTCapsREQ <= USED 0
PS_SetPsPlusPaging <= USED 0
addToList <= USED 0
atcmdSimStatusChanged <= USED 0
clearErrorIndFlagAll <= USED 0
deactive_ps_connection <= USED 0
getCeregStatus <= USED 0
getCgregStatus <= USED 0
getPSNwReg <= USED 0
getQueryTftCtxInProcess <= USED 0
parseAddr <= USED 0
removeNodeByCid <= USED 0
resetCidList <= USED 0
resetPsParas <= USED 0
searchListByCid <= USED 0
sendDefineDefaultPdpContext <= USED 0
setupNetworkCbs <= USED 0
showPDP <= USED 0
subnetmask_str2len <= USED 0
telGetDefaultPdpApn <= USED 0
telGetPdpCtxAuth <= USED 0
telGetPdpCtxAuthDS <= USED 0
telGetPdpInfo <= USED 0
;FILE ps_init.o
ipcTriggerInit <= USED 0
psKiOsMaximumSleep <= USED 0
psKiOsTick <= USED 0
psNvmAccessIs_ReducedOnlyCfun0Cfun1 <= USED 0
;FILE ps_itcm.o
;FILE ps_nasnoncache.o
AbNonCacheCheckisRplmnCampOn <= USED 0
AbNonCacheContextIsPsmReCovery <= USED 0
AbNonCacheContextIsSilentReCovery <= USED 0
AbNonCacheGetRecovryFlag <= USED 0
AbNonCacheSetRecovryFlag <= USED 0
MmNonCacheGetRecovryState <= USED 0
MmNonCacheSetRecovryState <= USED 0
NasNonCacheSimNetworkDataContextStore <= USED 0
NasNonCacheSimNetworkDataTrace <= USED 0
;FILE ps_nvm.o
psInitPsmContextNvm <= USED 0
psNvmAciGkiIsWorking <= USED 0
psNvmGetIMSNWReportInfo <= USED 0
psNvmReadIMSNWReportInfo <= USED 0
psNvmSetIMSNWReportNvmDefaultParams <= USED 0
psVendorPrintf <= USED 0
;FILE ps_psm.o
NasNonCacheContexInitPsmContex <= USED 0
PsmErrcInitContext <= USED 0
PsmInitAllContext <= USED 0
PsmInitPsmContext <= USED 0
PsmNasUsimInitContext <= USED 0
getPsPsmConextLen <= USED 0
saveContext <= USED 0
saveContextForPowerOffOnKey <= USED 0
systemHotResetInit <= USED 0
;FILE psm.o
psm_close <= USED 0
psm_get_int <= USED 0
psm_get_namespace <= USED 0
psm_get_wrapper_expose <= USED 0
psm_pop_subspace <= USED 0
psm_safe_set <= USED 0
psm_set_int <= USED 0
psm_set_wrapper_expose <= USED 0
;FILE psm_wrapper.o
clear_need_ascii_to_html <= USED 0
psm_commit_other__ <= USED 0
psm_flush <= USED 0
psm_get_version <= USED 0
psm_get_wrapper_with_variable_name <= USED 0
psm_match__ <= USED 0
psm_set_version <= USED 0
psmfile_get_wrapper <= USED 0
psmfile_set_wrapper <= USED 0
set_need_ascii_to_html <= USED 0
;FILE pstfilt.o
;FILE ptable.o
ptable_dump <= USED 0
ptable_init <= USED 0
;FILE pvmp3_alias_reduction.o
;FILE pvmp3_crc.o
;FILE pvmp3_dct_16.o
;FILE pvmp3_dct_6.o
;FILE pvmp3_dct_9.o
;FILE pvmp3_decode_header.o
;FILE pvmp3_decode_huff_cw.o
;FILE pvmp3_dequantize_sample.o
;FILE pvmp3_equalizer.o
;FILE pvmp3_framedecoder.o
;FILE pvmp3_get_main_data_size.o
;FILE pvmp3_get_scale_factors.o
;FILE pvmp3_get_side_info.o
;FILE pvmp3_getbits.o
;FILE pvmp3_huffman_decoding.o
;FILE pvmp3_huffman_parsing.o
;FILE pvmp3_imdct_synth.o
;FILE pvmp3_mdct_18.o
;FILE pvmp3_mdct_6.o
;FILE pvmp3_mpeg2_get_scale_data.o
;FILE pvmp3_mpeg2_get_scale_factors.o
;FILE pvmp3_mpeg2_stereo_proc.o
;FILE pvmp3_normalize.o
;FILE pvmp3_poly_phase_synthesis.o
;FILE pvmp3_polyphase_filter_window.o
;FILE pvmp3_reorder.o
;FILE pvmp3_seek_synch.o
pvmp3_frame_synch <= USED 0
;FILE pvmp3_stereo_proc.o
;FILE q_gain_c.o
;FILE q_gain_p.o
;FILE q_plsf.o
;FILE q_plsf_3.o
_Z15Test_Vq_subvec3PsPKsS_siPi <= USED 0
_Z15Test_Vq_subvec4PsPKsS_sPi <= USED 0
;FILE q_plsf_5.o
;FILE qgain475.o
;FILE qgain795.o
;FILE qmgr.o
qBlockGet <= USED 0
qBlockPut <= USED 0
qDelete <= USED 0
qGetRef <= USED 0
qRoom <= USED 0
;FILE qspi_core.o
buf_dump <= USED 0
flash_erase_nvm <= USED 0
flash_init_apn <= USED 0
flash_init_apn_ex <= USED 0
flash_init_factory <= USED 0
flash_init_fota <= USED 0
flash_init_gpsfw <= USED 0
flash_init_gpspvt <= USED 0
flash_init_nvm <= USED 0
flash_partion_init <= USED 0
flash_read_nvm <= USED 0
flash_write_nvm <= USED 0
qspi_nor_erase_all <= USED 0
qspi_rx_mode_name <= USED 0
qspi_tx_mode_name <= USED 0
spi_nor_do_read_id <= USED 0
spi_nor_do_read_uid <= USED 0
;FILE qspi_dma.o
_dma_m2m_xfer <= USED 0
dma_clr_irq <= USED 0
dma_config_desc <= USED 0
dma_config_descriptor <= USED 0
dma_disconnect_irq_handler <= USED 0
dma_enable <= USED 0
dma_get_des_num <= USED 0
dma_read_status <= USED 0
dma_reg_printf <= USED 0
dma_set_dcsr <= USED 0
dma_set_des <= USED 0
dma_stop_irq_dis <= USED 0
dma_wait_done <= USED 0
test_dma_complete_handler <= USED 0
;FILE qspi_host.o
qspi_enable_sw_handle_xip <= USED 0
qspi_wait_bus_ready <= USED 0
;FILE qspi_nor.o
get_spi_flash_chip <= USED 0
spi_nor_init2 <= USED 0
spi_nor_resume_task_xip <= USED 0
spi_nor_suspend_task_xip <= USED 0
;FILE qua_gain.o
;FILE raw.o
raw_bind_netif <= USED 0
raw_disconnect <= USED 0
raw_new_ip6 <= USED 0
;FILE recin.o
;FILE reorder.o
;FILE residu.o
;FILE ripc.o
RIPCPhase1Init <= USED 0
ripc_interrupt_clear <= USED 0
ripc_interrupt_set <= USED 0
;FILE rm.o
CloseAllClk <= USED 0
RMBeforeIntDisRegister <= USED 0
RMD2PrepareWithFastClk <= USED 0
RMIsResourceFree <= USED 0
RMIsResourceMulti <= USED 0
RMPhase1Init <= USED 0
RMSWReset <= USED 0
RMServiceFreqGet <= USED 0
RMServiceFreqSelect <= USED 0
RMwuEnabled <= USED 0
TWSIClockOnOff <= USED 0
XIRQClockOnOff <= USED 0
;FILE rm_debug.o
;FILE rm_memRetain.o
RMD2RecoverINTC <= USED 0
;FILE rndis.o
RndisMsgParser <= USED 0
RndisParamInit <= USED 0
Rndis_indicate_status_msg <= USED 0
Rndis_init_response <= USED 0
Rndis_keepalive_response <= USED 0
Rndis_merge_mult_packet <= USED 0
Rndis_query_gen_resp <= USED 0
Rndis_query_response <= USED 0
Rndis_remove_hdr <= USED 0
Rndis_reset_response <= USED 0
Rndis_set_gen_resp <= USED 0
Rndis_set_response <= USED 0
;FILE rohc_add_cid.o
;FILE rohc_comp.o
rohc_comp_deliver_feedback2 <= USED 0
rohc_comp_disable_profile <= USED 0
rohc_comp_disable_profiles <= USED 0
rohc_comp_enable_profile <= USED 0
rohc_comp_enable_profiles <= USED 0
rohc_comp_force_contexts_reinit <= USED 0
rohc_comp_get_cid_type <= USED 0
rohc_comp_get_general_info <= USED 0
rohc_comp_get_last_packet_info2 <= USED 0
rohc_comp_get_max_cid <= USED 0
rohc_comp_get_mrru <= USED 0
rohc_comp_get_segment2 <= USED 0
rohc_comp_get_state_descr <= USED 0
rohc_comp_pad <= USED 0
rohc_comp_profile_enabled <= USED 0
rohc_comp_reinit_context <= USED 0
rohc_comp_set_features <= USED 0
rohc_comp_set_mrru <= USED 0
rohc_comp_set_traces_cb2 <= USED 0
;FILE rohc_comp_rfc3095.o
;FILE rohc_decomp.o
rohc_decomp_disable_profile <= USED 0
rohc_decomp_disable_profiles <= USED 0
rohc_decomp_enable_profile <= USED 0
rohc_decomp_enable_profiles <= USED 0
rohc_decomp_get_cid_type <= USED 0
rohc_decomp_get_context_info <= USED 0
rohc_decomp_get_general_info <= USED 0
rohc_decomp_get_last_packet_info <= USED 0
rohc_decomp_get_max_cid <= USED 0
rohc_decomp_get_mrru <= USED 0
rohc_decomp_get_prtt <= USED 0
rohc_decomp_get_rate_limits <= USED 0
rohc_decomp_get_state_descr <= USED 0
rohc_decomp_set_features <= USED 0
rohc_decomp_set_mrru <= USED 0
rohc_decomp_set_traces_cb2 <= USED 0
;FILE rohc_decomp_detect_packet.o
;FILE rohc_decomp_rfc3095.o
;FILE rohc_list.o
;FILE rohc_packets.o
rohc_get_ext_descr <= USED 0
rohc_get_packet_descr <= USED 0
rohc_get_packet_type <= USED 0
;FILE rohc_profiles.o
rohc_get_profile_descr <= USED 0
rohc_profile_get_other_version <= USED 0
rohc_profile_is_rohcv1 <= USED 0
;FILE rohc_traces_internal.o
;FILE rohc_utils.o
;FILE root.o
dummyRootFunction <= USED 0
;FILE round.o
;FILE rrcdsutils.o
RrDsCheckEnterSameOperatorModeSubstituted <= USED 0
RrDsCheckWorkOnSameSuitableRatSubstituted <= USED 0
RrDsGetIratDsAbortSearchCnfSendingSubstituted <= USED 0
RrDsGetSameOperatorSubstituted <= USED 0
RrDsSetIratReselectingSubstituted <= USED 0
RrDsSetSameOperatorSubstituted <= USED 0
rrDsChangePagingDrxLength <= USED 0
rrDsGetGsmPagingDrxLength <= USED 0
rrDsGetUmtsPagingDrxLength <= USED 0
rrDsSendIratDsAbortGsmPsReq <= USED 0
rrDsSendIratDsCampOnReq <= USED 0
rrDsSendIratDsPhyConfigFinishInd <= USED 0
rrDsSendIratDsStopIratReq <= USED 0
rrDsSendIratDsSuspendCnfWithIgnore <= USED 0
rrDsSendIratDsVoiceBadInd <= USED 0
rrDsSetBtLteCoExist <= USED 0
rrDsSetWifiLteCoExist <= USED 0
;FILE rrcomdb.o
RrComDbGetManuallyTriggered <= USED 0
RrComDbGetMncLengthKnown <= USED 0
RrComDbGetPlmsSimId <= USED 0
RrComDbGetSimPresent <= USED 0
RrComDbSetManuallySelectedPlmn <= USED 0
RrComdbCheckIsInChina <= USED 0
;FILE rrutility.o
IsCtccPlmn <= USED 0
IsCuccPlmn <= USED 0
IsDeutscheTelekomInRequestedPlmnList <= USED 0
IsProximusPlmn <= USED 0
IsRedirectionFailureHandlingEnable <= USED 0
IsSoftbankPlmn <= USED 0
MustSearchBandWhenRedirectFromLTE <= USED 0
RrIsTestSimGcfMode <= USED 0
;FILE rsa.o
mbedtls_rsa_copy <= USED 0
mbedtls_rsa_export <= USED 0
mbedtls_rsa_export_crt <= USED 0
mbedtls_rsa_export_raw <= USED 0
mbedtls_rsa_import <= USED 0
mbedtls_rsa_set_padding <= USED 0
;FILE rsa_internal.o
;FILE s10_8pf.o
;FILE sac_api.o
;FILE sac_cc.o
;FILE sac_cfg.o
;FILE sac_dat.o
sacGetCurrentCid <= USED 0
sacGetCurrentNsapi <= USED 0
;FILE sac_dev.o
sacCiDevSetLpmEnabledFunc <= USED 0
sacDevGetSimCfunState <= USED 0
sacDevMinFuncMode <= USED 0
sacDevSetDipOption <= USED 0
setSacDevLpmFlag <= USED 0
;FILE sac_libs.o
sacShAdnDigitsToBcd <= USED 0
sacShBcdToDigits <= USED 0
sacShCbEncodeGsm7BitData <= USED 0
sacShCheckSpecPendingCiReq <= USED 0
sacShCheckWaitCiReq <= USED 0
sacShConvertIpToLong <= USED 0
sacShDecodeUcs2HexData <= USED 0
sacShDelayCiReq <= USED 0
sacShDequeue <= USED 0
sacShEncodeUcs2HexData <= USED 0
sacShGetInfoFromPrimitiveId <= USED 0
sacShGetOpShHandle <= USED 0
sacShGetPrevFsmState <= USED 0
sacShKeepCurSignal <= USED 0
sacShReadNvm <= USED 0
sacShReset <= USED 0
sacShSendCiAccessDeniedCnf <= USED 0
sacShSendCiAccessDeniedCnfWithInfo <= USED 0
sacShSendCiHasNoSupportCnfWithInfo <= USED 0
sacShStoreUserVal <= USED 0
sacShWriteNvm <= USED 0
;FILE sac_mm.o
SacGetArfcnFromNvm <= USED 0
SacInsertArfcnNvm <= USED 0
SacUpdateAllArfcnNvm <= USED 0
sacMmGetCurrentNetworkMode <= USED 0
sacMmIsTestNetwork <= USED 0
sacMmResetRegMode <= USED 0
;FILE sac_msg.o
SacMsgGetCurrentMtMsgConfigFunc <= USED 0
;FILE sac_pb.o
;FILE sac_ps.o
sacPsGetEutranAct <= USED 0
sacPsGetPdpCtxInfo <= USED 0
sacPsGetPsState <= USED 0
sacPsGetSim4GQciValue <= USED 0
sacPsLteDefaultApnDefined <= USED 0
sacPsProcessApexMmBandInd <= USED 0
sacPsSetForcedReportNwRegInd <= USED 0
;FILE sac_queues.o
sacSgQueueReadFirstSignal <= USED 0
sacSgQueueRemoveFirstSignal <= USED 0
;FILE sac_ref.o
;FILE sac_shell.o
sacShSetKeepControlMask <= USED 0
sacShSgHasNeededControl <= USED 0
;FILE sac_shell_engine.o
VgCiTask1 <= USED 0
;FILE sac_sim.o
getSacCfun1Flag <= USED 0
isTestSimFromSac <= USED 0
sacSimIsSimReady <= USED 0
setSacCfun1Flag <= USED 0
;FILE sac_ss.o
;FILE sac_trace.o
sacDataTrace <= USED 0
;FILE scaler_rotation.o
ire_ctrl_cfg <= USED 0
ire_ctrl_cfg_v2 <= USED 0
ire_set_addr <= USED 0
ire_trigger <= USED 0
offline_irq_deinit <= USED 0
offline_irq_init <= USED 0
offline_power_off <= USED 0
offline_power_on <= USED 0
offline_s_reset <= USED 0
scl_ctrl_cfg <= USED 0
scl_ire_regs_dump <= USED 0
scl_set_addr <= USED 0
scl_trigger <= USED 0
;FILE scc.o
Scc_CallConference <= USED 0
Scc_GetCaps <= USED 0
Scc_GetConfig <= USED 0
Scc_GetFirstAccess <= USED 0
Scc_GetNextAccess <= USED 0
Scc_GetProperty <= USED 0
Scc_Media_Cancel <= USED 0
Scc_Media_CreateChannel <= USED 0
Scc_Media_DeleteChannel <= USED 0
Scc_Media_GetIoInterface <= USED 0
Scc_Media_Recv <= USED 0
Scc_Media_Send <= USED 0
Scc_QueryAccessStatus <= USED 0
Scc_SetConfig <= USED 0
Scc_SetProperty <= USED 0
;FILE scc_access_sm.o
scc_AccessSM_RestartAllAccessOnCondition <= USED 0
scc_AccessSM_SetNetworkOperator <= USED 0
;FILE scc_access_utils.o
scc_GetAccessCtx <= USED 0
;FILE scc_call_sm.o
;FILE scc_call_utils.o
scc_GetAltAccess <= USED 0
scc_isForcedCsEcall <= USED 0
;FILE scc_cs_intf.o
;FILE scc_pref_eval.o
;FILE scc_ps_intf.o
scc_IMS_ModifyPresService <= USED 0
scc_IMS_RefreshPresService <= USED 0
scc_IMS_StartPresService <= USED 0
scc_UpdateDialog <= USED 0
scc_ps_is_mo_call_continue_reject_503_action_meet <= USED 0
;FILE scc_sms.o
;FILE scc_utils.o
scc_DeleteIMSCapabilities <= USED 0
scc_FreeScratchBuf <= USED 0
scc_IsEmergencyURN <= USED 0
;FILE sdcard.o
DeleteSdcardInitTimerFunc <= USED 0
SD_CARD_Insert_Remove_Task <= USED 0
SD_ResetCMD_Task <= USED 0
dump_sd_lisr <= USED 0
get_log_sd_free_space <= USED 0
get_log_sd_unused <= USED 0
get_sdcard_power_gpio <= USED 0
sdcard_burstsize_config <= USED 0
sdcard_clk_off <= USED 0
sdcard_get_errflag <= USED 0
sdcard_get_mode <= USED 0
sdcard_reinit <= USED 0
sdcard_set_mode <= USED 0
set_sdcard_power_gpio <= USED 0
;FILE sdp.o
;FILE sdp_base_grammer.o
;FILE sdp_formatter.o
format_acap_attribute_fields <= USED 0
format_acfg_attribute_fields <= USED 0
format_attrib <= USED 0
format_attrib_a <= USED 0
format_attrib_c <= USED 0
format_attrib_d <= USED 0
format_attrib_e <= USED 0
format_attrib_g <= USED 0
format_attrib_i <= USED 0
format_attrib_l <= USED 0
format_attrib_m <= USED 0
format_attrib_n <= USED 0
format_attrib_o <= USED 0
format_attrib_p <= USED 0
format_attrib_q <= USED 0
format_attrib_r <= USED 0
format_attrib_s <= USED 0
format_attrib_t <= USED 0
format_connection_field <= USED 0
format_email_fields <= USED 0
format_fmtp_attribute_fields <= USED 0
format_framesize_attribute_field <= USED 0
format_group_attribute_fields <= USED 0
format_image_attribute_fields <= USED 0
format_information_field <= USED 0
format_key_field <= USED 0
format_pcfg_attribute_fields <= USED 0
format_phone_fields <= USED 0
format_property_attrib <= USED 0
format_repeat_interval_fields <= USED 0
format_rtpmap_attribute_fields <= USED 0
format_tcap_attribute_fields <= USED 0
format_time_zone_fields <= USED 0
format_uri_field <= USED 0
format_valid_atttib_value <= USED 0
sdp_format_precond_confirmstatus <= USED 0
sdp_format_precond_currentstatus <= USED 0
sdp_format_precond_desiredstatus <= USED 0
sdp_validate_precond_directiontag <= USED 0
sdp_validate_precond_statustype <= USED 0
sdp_validate_precond_strengthtag <= USED 0
validate_acap <= USED 0
validate_acfg <= USED 0
validate_bandwidth <= USED 0
validate_byte_string <= USED 0
validate_creq <= USED 0
validate_csup <= USED 0
validate_fmtp <= USED 0
validate_group <= USED 0
validate_pcfg <= USED 0
validate_rtpmap <= USED 0
validate_tcap <= USED 0
validate_time_field <= USED 0
;FILE sdp_parser_utils.o
parse_IP6addr <= USED 0
;FILE sdp_util.o
SDP_IsMediaTypePresent <= USED 0
transfer_buffer <= USED 0
;FILE sdvl.o
;FILE set_fs.o
;FILE set_sign.o
;FILE set_zero.o
;FILE sha1.o
mbedtls_sha1 <= USED 0
mbedtls_sha1_process <= USED 0
;FILE sha256.o
mbedtls_sha256_finish <= USED 0
mbedtls_sha256_process <= USED 0
mbedtls_sha256_starts <= USED 0
mbedtls_sha256_update <= USED 0
;FILE sha512.o
mbedtls_sha512 <= USED 0
mbedtls_sha512_finish <= USED 0
mbedtls_sha512_process <= USED 0
mbedtls_sha512_starts <= USED 0
mbedtls_sha512_update <= USED 0
;FILE shr.o
;FILE shr_r.o
;FILE sid_sync.o
sid_sync_reset <= USED 0
sid_sync_set_handover_debt <= USED 0
;FILE signal_mcu.o
WebRtcNetEQ_SignalMcu <= USED 0
;FILE simPin.o
assert_cnt_flag_init <= USED 0
;FILE sim_api.o
isTestCard <= USED 0
libConvertNumToHexString <= USED 0
;FILE sim_api_mini.o
checkSIMRet <= USED 0
getSimType <= USED 0
resetSimParas <= USED 0
telSimRemoveSuppress <= USED 0
;FILE simatdec.o
SimatDecodeAddress <= USED 0
SimatDecodeItem <= USED 0
SimatDecodeItemList <= USED 0
SimatDecodeMenuItems <= USED 0
SimatDecodeResponseLength <= USED 0
SimatDecodeSmsTpdu <= USED 0
SimatDecodeTextAttributeList <= USED 0
SimatDecodeTextString <= USED 0
SimatDecodeToneType <= USED 0
;FILE simatenc.o
SimEncodeTerminalProfile <= USED 0
SimatEncodeAlphaId <= USED 0
SimatEncodeAtCommandResponse <= USED 0
SimatEncodeBearerCap <= USED 0
SimatEncodeResult2Info <= USED 0
SimatEncodeTextString <= USED 0
SimatEncodeUssdString <= USED 0
UsimEncodeTerminalProfile <= USED 0
;FILE simdec.o
SimDecodePhase <= USED 0
SimDecodeSst <= USED 0
USIMOemFlagClear <= USED 0
USIMOemFlagGet <= USED 0
USIMOemFlagSet <= USED 0
;FILE simenc.o
SimEncodeAaem <= USED 0
SimEncodeAccessClass <= USED 0
SimEncodeAdnFdn <= USED 0
SimEncodeCps <= USED 0
SimEncodeDck <= USED 0
SimEncodeElp <= USED 0
SimEncodeLp <= USED 0
;FILE simproc.o
SimManagerTask1 <= USED 0
SimManagerTaskExitRoutine <= USED 0
;FILE simproc2.o
SimManagerTask21 <= USED 0
SimManagerTask2ExitRoutine <= USED 0
;FILE simsig.o
SimSendL1siSwitchReq <= USED 0
;FILE singleToneDect.o
L_mls <= USED 0
;FILE sipResolver.o
SIPResolver_GetConfig <= USED 0
;FILE sipUaRefer.o
;FILE sipUaReferDialog.o
SipUAS_UpdateReferDialog <= USED 0
;FILE sipUaReferFsmFunc.o
;FILE sipUaUtils.o
API_SIP_UA_FreeOtherHeader <= USED 0
FreeResponseParameters <= USED 0
SIP_UA_RemoveIMSSpecificHdrs <= USED 0
sipUAC_AddDiversionHeader <= USED 0
sipUA_CloneFeatureParam <= USED 0
sipUA_SetPMediaAuthorizationHdr <= USED 0
;FILE sipUacAppReq.o
SipUACSendInviteSessTimer <= USED 0
SipUACSendUpdateSessTimer <= USED 0
;FILE sipUacStackResp.o
;FILE sipUasAppResp.o
SipUASSendInvite5XX <= USED 0
SipUASSendPrack4XXConfirmed <= USED 0
SipUASSendPrack4XXEarly <= USED 0
;FILE sipUasStackReq.o
SIP_UAS_ProcessRefer <= USED 0
SIP_UAS_ProcessSubscribe <= USED 0
;FILE sipUseragent.o
SIPUAC_UnSubscribe <= USED 0
SIPUA_DeleteUser <= USED 0
SIPUA_GetFqdn <= USED 0
;FILE sipUseragentReg.o
;FILE sipUseragentRegFsmFunc.o
SipUaGotChallengeDeRegisterNotifyEn <= USED 0
SipUaGotChallengeReRegisterNotifyEn <= USED 0
;FILE sip_auth.o
;FILE sip_formatter.o
;FILE sip_os_utils.o
;FILE sip_transceiver.o
SIPTransceiver_GetConfig <= USED 0
SIP_Get_Received <= USED 0
;FILE sipen.o
;FILE sipenfsmfunc.o
SipEnTerminatingNotifyStack <= USED 0
;FILE siptransaction.o
SIPTransaction_SendRequest <= USED 0
sipTrxn_GenerateViaBranch <= USED 0
;FILE siptransactioninvclifsm.o
;FILE siptransactioninvclifsmfunc.o
;FILE siptransactioninvsrvfsm.o
;FILE siptransactioninvsrvfsmfunc.o
;FILE siptransactionlist.o
SipTransactionListPrint <= USED 0
;FILE siptransactionnoninvclifsm.o
;FILE siptransactionnoninvclifsmfunc.o
;FILE siptransactionnoninvsrvfsm.o
;FILE siptransactionnoninvsrvfsmfunc.o
;FILE sipuri.o
SIP_GetDisplayUriName <= USED 0
SIP_GetTelUriParamsVerstatType <= USED 0
;FILE siputil.o
SIP_CopyContentEncoding <= USED 0
SIP_CopyContentLanguage <= USED 0
SIP_CopyContentType <= USED 0
;FILE smcmprot.o
SmcmTask1 <= USED 0
SmcmTask_21 <= USED 0
;FILE smencdec.o
;FILE smip_common.o
;FILE smip_storage.o
smip_IsSMSStorageRequired <= USED 0
smip_IsSMSStored <= USED 0
smip_IsStatusReportStored <= USED 0
smip_IsVoiceMail <= USED 0
;FILE smmain.o
GpSmTask1 <= USED 0
;FILE smmain2.o
GpSmTask21 <= USED 0
;FILE smrdwr.o
;FILE smrlprot.o
SmrlTask1 <= USED 0
SmrlTask_21 <= USED 0
;FILE smtlprot.o
SmtlTask1 <= USED 0
SmtlTaskExitRoutine <= USED 0
SmtlTask_21 <= USED 0
SmtlTask_2ExitRoutine <= USED 0
;FILE snow_3g.o
MULxPOW <= USED 0
;FILE sockets.o
lwip_close_socket <= USED 0
lwip_close_tcppcb <= USED 0
;FILE sp_dec.o
Speech_Decode_Frame_reset <= USED 0
;FILE sp_enc.o
Speech_Encode_Frame_First <= USED 0
Speech_Encode_Frame_reset <= USED 0
;FILE split_and_insert.o
;FILE spreproc.o
;FILE spstproc.o
;FILE sqrt_l.o
;FILE ss_api.o
SS_LocationVerificationRsp <= USED 0
resetSsParas <= USED 0
;FILE ss_api_mini.o
;FILE ssl_ciphersuites.o
mbedtls_ssl_ciphersuite_from_string <= USED 0
mbedtls_ssl_get_ciphersuite_id <= USED 0
mbedtls_ssl_get_ciphersuite_name <= USED 0
;FILE ssl_cli.o
;FILE ssl_http_client.o
;FILE ssl_srv.o
mbedtls_ssl_conf_dtls_cookies <= USED 0
mbedtls_ssl_set_client_transport_id <= USED 0
;FILE ssl_tls.o
mbedtls_ssl_conf_cert_profile <= USED 0
mbedtls_ssl_conf_cert_req_ca_list <= USED 0
mbedtls_ssl_conf_ciphersuites_for_version <= USED 0
mbedtls_ssl_conf_curves <= USED 0
mbedtls_ssl_conf_dh_param <= USED 0
mbedtls_ssl_conf_dh_param_ctx <= USED 0
mbedtls_ssl_conf_dhm_min_bitlen <= USED 0
mbedtls_ssl_conf_dtls_anti_replay <= USED 0
mbedtls_ssl_conf_endpoint <= USED 0
mbedtls_ssl_conf_handshake_timeout <= USED 0
mbedtls_ssl_conf_legacy_renegotiation <= USED 0
mbedtls_ssl_conf_psk_cb <= USED 0
mbedtls_ssl_conf_renegotiation <= USED 0
mbedtls_ssl_conf_renegotiation_enforced <= USED 0
mbedtls_ssl_conf_renegotiation_period <= USED 0
mbedtls_ssl_conf_session_cache <= USED 0
mbedtls_ssl_conf_sig_hashes <= USED 0
mbedtls_ssl_conf_transport <= USED 0
mbedtls_ssl_conf_verify <= USED 0
mbedtls_ssl_get_ciphersuite <= USED 0
mbedtls_ssl_get_peer_cert <= USED 0
mbedtls_ssl_get_session <= USED 0
mbedtls_ssl_get_version <= USED 0
mbedtls_ssl_send_fatal_handshake_failure <= USED 0
mbedtls_ssl_session_copy <= USED 0
mbedtls_ssl_session_reset <= USED 0
mbedtls_ssl_set_datagram_packing <= USED 0
mbedtls_ssl_set_hs_psk <= USED 0
mbedtls_ssl_set_mtu <= USED 0
mbedtls_ssl_set_read_buf_hook <= USED 0
mbedtls_ssl_set_session <= USED 0
mbedtls_ssl_set_timer_cb <= USED 0
mbedtls_ssl_set_verify <= USED 0
mbedtls_ssl_tls_prf <= USED 0
;FILE stream_db.o
WebRtcNetEQ_StreamRemove <= USED 0
;FILE streamingmedia.o
_Z17media_service_runv <= USED 0
streamingmedia_init <= USED 0
;FILE stubs.o
L1FrGetUsedBbcTdsIfCount <= USED 0
;FILE sub.o
;FILE sulog.o
SulogDmaCopy <= USED 0
SulogOffControlForUsbRemoveInternal <= USED 0
;FILE supp_service.o
CB_Free <= USED 0
CDIV_Free <= USED 0
SuppService_CB_Group_Payload <= USED 0
SuppService_CB_MapToCBOption <= USED 0
SuppService_CB_Payload <= USED 0
SuppService_Enable_or_Disable_CB <= USED 0
SuppService_Enable_or_Disable_CB_Payload <= USED 0
SuppService_Set_CB <= USED 0
SuppService_Set_CB_Group <= USED 0
Xdmc_Parse_Supp_CDIV_Status <= USED 0
;FILE syn_filt.o
;FILE sys_arch.o
;FILE sysapi.o
SysDebug_DumpVector_Print <= USED 0
SysDebug_Flush <= USED 0
SysDebug_GetLogName <= USED 0
SysDebug_LogBuf_Cleanup <= USED 0
SysSet_logLevel <= USED 0
Sys_IsInit <= USED 0
Sys_SmartBuf_IncrRefImpl <= USED 0
sys_printnl_buf <= USED 0
;FILE sysbuf.o
SysBuffer_Bufcpy <= USED 0
SysBuffer_CopyToMem <= USED 0
SysVector2Buffer_Set <= USED 0
;FILE syscache.o
;FILE syscrypt.o
SysCrypt_DigestGetHashLength <= USED 0
;FILE sysevent.o
SysEvent_Reset <= USED 0
;FILE sysio.o
SysGenericIo_Cancel <= USED 0
SysGenericIo_Create <= USED 0
SysGenericIo_Delete <= USED 0
SysGenericIo_GetBuf <= USED 0
SysGenericIo_GetFlags <= USED 0
SysGenericIo_GetIoInterface <= USED 0
SysGenericIo_GetVector <= USED 0
SysGenericIo_Recv <= USED 0
SysGenericIo_Register <= USED 0
SysGenericIo_Reset <= USED 0
SysGenericIo_Send <= USED 0
SysGenericIo_SendMessage <= USED 0
SysGenericIo_SetIoResult <= USED 0
SysGenericIo_SetIoState <= USED 0
SysIo_SetErrorState <= USED 0
SysLoopbackIo_Create <= USED 0
SysLoopbackIo_Delete <= USED 0
SysLoopbackIo_GetIoInterface <= USED 0
;FILE syslist.o
SysList_Cycle <= USED 0
;FILE sysmod.o
SysModule_CleanupThread <= USED 0
SysModule_Dump <= USED 0
SysObject_Dump <= USED 0
my_SysModule_ThreadIDMatch <= USED 0
;FILE sysport.o
SysTimer_Time_To_Ticks <= USED 0
;FILE sysqueue.o
;FILE syssocket.o
SysGetSockName <= USED 0
SysGetSockopt <= USED 0
SysInet_pton4 <= USED 0
SysInet_pton6 <= USED 0
Sys_MapErrCode <= USED 0
;FILE systhread.o
;FILE systimer.o
SysTimer_SetLocalTimer <= USED 0
;FILE sysutil.o
SysUtil_MapEventCode <= USED 0
;FILE sysutils.o
;FILE syswait.o
checkObj <= USED 0
;FILE tavor_packages.o
plValTSInit <= USED 0
;FILE tbl.o
ConvertGSM7bitToAscii8bit_iso8859 <= USED 0
;FILE tcp.o
tcp_bind_netif <= USED 0
tcp_debug_print_pcbs <= USED 0
tcp_fasttmr <= USED 0
tcp_listen_pcb_exist <= USED 0
tcp_listen_pcbs_dump <= USED 0
tcp_new_ip6 <= USED 0
tcp_process_pcb_exist <= USED 0
tcp_setprio <= USED 0
tcp_slowtmr <= USED 0
tcp_tmr <= USED 0
;FILE tcp_in.o
tcp_get_scale_opt <= USED 0
tcp_get_tcp_urgp <= USED 0
;FILE tcp_out.o
tcp_keepalive <= USED 0
tcp_zero_window_probe <= USED 0
;FILE tcpip.o
lwip_mq_fetch <= USED 0
lwip_mq_left_query <= USED 0
lwip_mq_size <= USED 0
lwip_set_rx_ims_rate <= USED 0
tcpip_callbackmsg_delete <= USED 0
tcpip_callbackmsg_new <= USED 0
tcpip_input_eth_check <= USED 0
tcpip_timeout <= USED 0
tcpip_trycallback <= USED 0
tcpip_untimeout <= USED 0
;FILE telatci.o
CiInitCallBack <= USED 0
atRespStr_Delay <= USED 0
createIndForProxy <= USED 0
;FILE telcc.o
ciSyncAudio <= USED 0
;FILE telcmux.o
miGmr <= USED 0
moduleInfo <= USED 0
;FILE telcmux_mini.o
fota_firmware_pkgDwl <= USED 0
;FILE telcontroller.o
Switch_Modem_State <= USED 0
ciTestTimeout <= USED 0
getChannelInfo <= USED 0
getChsetType <= USED 0
tcCreateMSGQ <= USED 0
tcOpenDevice <= USED 0
tcOpenExtSerialPort <= USED 0
;FILE teldat.o
Get_nw_info_for_at <= USED 0
ciCSQEX <= USED 0
ciGpCGSink <= USED 0
ciNETDMSG <= USED 0
initDataPPP <= USED 0
initDirectIP <= USED 0
sendSinkData <= USED 0
;FILE teldat_mini.o
Set_sATP_Mode <= USED 0
insert_node_into_PS_sink_Queue <= USED 0
search_handlelist_sequence <= USED 0
;FILE teldbg.o
TimerAssertHandle <= USED 0
VolSLTHandler <= USED 0
VolSLTMsgHandler <= USED 0
dbg_dump_buffer <= USED 0
telGetLwipCtrlMode <= USED 0
;FILE teldev.o
GripNotifyAP <= USED 0
ciCGSN <= USED 0
ciMyDownload <= USED 0
ciRSSI <= USED 0
ciWBAMR <= USED 0
playMP3 <= USED 0
;FILE teldev_mini.o
;FILE telmm.o
ciWbCellLock <= USED 0
;FILE telmm_mini.o
;FILE telmsg.o
ciLockSMSStatus <= USED 0
;FILE telpb.o
ciSWritePB <= USED 0
;FILE telps.o
Encode4GBitrate <= USED 0
ciPSAttachWithCause <= USED 0
ciPsPlusPaging <= USED 0
ciTELCGCONT <= USED 0
;FILE telps_mini.o
getRegOptionPersist <= USED 0
;FILE telsim.o
ciGetCardMode <= USED 0
;FILE telsim_mini.o
;FILE telss.o
;FILE telutl.o
GenerateAddressType <= USED 0
HexStringToInt <= USED 0
StringToInt <= USED 0
addDoubleQuotatiaon <= USED 0
displayHexValue <= USED 0
dump_string <= USED 0
hex_to_bin <= USED 0
isDialNumber <= USED 0
str2hex32 <= USED 0
string_tok_nexthexint <= USED 0
string_tok_nextulong <= USED 0
string_tok_start <= USED 0
telUtlTranslateUnpackedGsm7ToText <= USED 0
translateUnpackedGsm7ToText <= USED 0
;FILE testprog.o
module_set_var <= USED 0
psm_show_list <= USED 0
psm_test <= USED 0
;FILE tft.o
tft_get_ip_info <= USED 0
tft_route_from_default <= USED 0
tft_route_from_default_ip6 <= USED 0
;FILE tftcore.o
ModemGetCidByTft <= USED 0
SearchTFTNodeToFindCid <= USED 0
TftAddNewItem <= USED 0
tft_delete_tft <= USED 0
;FILE tick_manager.o
TickManagerIsSuspendContext <= USED 0
TickPhase1Init <= USED 0
;FILE tim.o
SendLteActTestModeCmp <= USED 0
SendLteCloseTestModeCmp <= USED 0
SendLteDeactTestModeCmp <= USED 0
SendLteOpenTestModeCmp <= USED 0
TimTask1 <= USED 0
TimTask_21 <= USED 0
;FILE timeM.o
;FILE timer.o
AccTimerCreate <= USED 0
AccTimerStart <= USED 0
AccTimerStartEx <= USED 0
disable_all_interrupts <= USED 0
getTime_us <= USED 0
restore_all_interrupts <= USED 0
timerDidOSTimerExpire <= USED 0
timerEnableClock <= USED 0
timerPhase1Init <= USED 0
timerReschedule <= USED 0
timerResume <= USED 0
timerVersionGet <= USED 0
;FILE timers.o
lwip_1s_cb <= USED 0
lwip_60s_cb <= USED 0
lwip_timer_1s <= USED 0
lwip_timer_60s <= USED 0
lwip_timer_init <= USED 0
tcp_tmr_timer <= USED 0
tcpip_timer_sleep_long <= USED 0
tcpip_timer_sleep_short <= USED 0
;FILE ton_stab.o
;FILE tsip_common.o
;FILE tsip_free.o
SIP_FreeAuthenticationInfo <= USED 0
SIP_FreeContentLanguage <= USED 0
SIP_FreeDigestChallenge <= USED 0
;FILE tsip_parser.o
cpytodelim <= USED 0
mem_AddMemNode <= USED 0
mem_AllocMemNode <= USED 0
mem_DelMemNode <= USED 0
mem_DumpMemUsedInfo <= USED 0
mem_FindMemNode <= USED 0
;FILE tsip_parser_uri.o
;FILE tx_block_allocate.o
;FILE tx_block_pool_cleanup.o
;FILE tx_block_pool_create.o
;FILE tx_block_pool_prioritize.o
;FILE tx_block_release.o
;FILE tx_event_flags_cleanup.o
;FILE tx_event_flags_create.o
;FILE tx_event_flags_delete.o
;FILE tx_event_flags_get.o
;FILE tx_event_flags_set.o
;FILE tx_hisr.o
;FILE tx_initialize_high_level.o
_tx_initialize_high_level <= USED 0
;FILE tx_initialize_kernel_enter.o
_tx_initialize_kernel_enter <= USED 0
tx_application_define <= USED 0
;FILE tx_queue_cleanup.o
;FILE tx_queue_create.o
;FILE tx_queue_delete.o
;FILE tx_queue_front_send.o
;FILE tx_queue_prioritize.o
;FILE tx_queue_receive.o
;FILE tx_queue_send.o
;FILE tx_semaphore_cleanup.o
;FILE tx_semaphore_create.o
;FILE tx_semaphore_delete.o
;FILE tx_semaphore_get.o
;FILE tx_semaphore_prioritize.o
;FILE tx_semaphore_put.o
;FILE tx_thread_create.o
;FILE tx_thread_delete.o
;FILE tx_thread_info_get.o
;FILE tx_thread_initialize.o
_tx_thread_initialize <= USED 0
tx_current_verison <= USED 0
;FILE tx_thread_priority_change.o
;FILE tx_thread_relinquish.o
;FILE tx_thread_reset.o
;FILE tx_thread_resume.o
;FILE tx_thread_shell_entry.o
;FILE tx_thread_suspend.o
;FILE tx_thread_system_preempt_check.o
;FILE tx_thread_system_resume.o
;FILE tx_thread_system_suspend.o
;FILE tx_thread_terminate.o
;FILE tx_thread_time_slice.o
_tx_thread_time_slice <= USED 0
;FILE tx_thread_timeout.o
;FILE tx_time_set.o
_tx_time_set <= USED 0
;FILE tx_timer_activate.o
;FILE tx_timer_change.o
;FILE tx_timer_create.o
;FILE tx_timer_deactivate.o
;FILE tx_timer_delete.o
;FILE tx_timer_expiration_process.o
_tx_timer_expiration_process <= USED 0
;FILE tx_timer_info_get.o
_tx_timer_info_get <= USED 0
_tx_timer_nearest_count <= USED 0
_tx_timer_nearest_count_ <= USED 0
;FILE tx_timer_initialize.o
_tx_timer_initialize <= USED 0
;FILE tx_timer_system_activate.o
_tx_timer_system_teleport <= USED 0
;FILE tx_timer_system_deactivate.o
;FILE tx_timer_thread_entry.o
_tx_timer_thread_register_hook <= USED 0
;FILE uac_dialogmgr.o
;FILE uas_dialogmgr.o
;FILE udc_driver.o
UDCDriverActivateHardware <= USED 0
UDCDriverClearEndpointInterrupt <= USED 0
UDCDriverClearEventInterrupt <= USED 0
UDCDriverConfigureEndpoints <= USED 0
UDCDriverDatabaseReset <= USED 0
UDCDriverDeactivateHardware <= USED 0
UDCDriverDisableEndpointInterrupt <= USED 0
UDCDriverDisableEventInterrupt <= USED 0
UDCDriverEnableEndpointInterrupt <= USED 0
UDCDriverEnableEventInterrupt <= USED 0
UDCDriverEndpointActivate <= USED 0
UDCDriverEndpointClearReceiveStatus <= USED 0
UDCDriverEndpointClearStall <= USED 0
UDCDriverEndpointDeactivate <= USED 0
UDCDriverEndpointFlush <= USED 0
UDCDriverEndpointSetupIN <= USED 0
UDCDriverEndpointSetupINMultiTransmitWithDma <= USED 0
UDCDriverEndpointSetupOUT <= USED 0
UDCDriverEndpointStall <= USED 0
UDCDriverForceHostResume <= USED 0
UDCDriverGetCurrentConfigurationSettings <= USED 0
UDCDriverIsDeviceControllerEnabled <= USED 0
UDCDriverIsHostEnabledRemoteWakeup <= USED 0
UDCDriverPhase1Init <= USED 0
UDCDriverPhase2Init <= USED 0
UDCDriverReadFromFifo <= USED 0
UDCDriverResume <= USED 0
UDCDriverSuspend <= USED 0
UDCDriverWriteToFifo <= USED 0
UDCPadActivate <= USED 0
;FILE udp.o
udp_bind_netif <= USED 0
udp_send <= USED 0
;FILE ui_os_flag.o
;FILE ui_os_message.o
UOS_GetMessageQueueSize <= USED 0
UOS_IsEventAvailable <= USED 0
UOS_MsgQAvailableSize <= USED 0
UOS_SendEvent <= USED 0
UOS_WaitEvent <= USED 0
;FILE ui_os_mutex.o
UOS_TryTakeMutex <= USED 0
;FILE ui_os_semaphore.o
UOS_ReleaseSemaphore <= USED 0
;FILE ui_os_task.o
UOS_ChangeCoreFreqTo624M <= USED 0
UOS_DelayTaskRunning <= USED 0
UOS_DisableFrequencyConversion <= USED 0
UOS_EnableFrequencyConversion <= USED 0
UOS_GetCurrentStackInfo <= USED 0
UOS_GetTaskRef <= USED 0
UOS_InUrgentSection <= USED 0
UOS_SleepMs <= USED 0
UOS_SleepSeconds <= USED 0
;FILE ui_os_timer.o
UOS_KillFunctionTimer <= USED 0
UOS_KillTimerEX <= USED 0
UOS_PeriodTimerHandleRsp <= USED 0
UOS_StartFunctionTimer_periodic <= USED 0
UOS_StartFunctionTimer_single <= USED 0
UOS_StartTimerEX <= USED 0
UOS_get_FunctionTimer <= USED 0
;FILE umm_psms.o
MmSearchUmmConnections <= USED 0
UmmReleasePsmsConnections <= USED 0
;FILE umm_sim.o
;FILE usb1_device.o
USB1DeviceCableDetectionNotify <= USED 0
USB1DeviceEP0InterruptHandler <= USED 0
USB1DeviceEP0ProcessSetupCmd <= USED 0
USB1DeviceEP0ProcessSetupState <= USED 0
USB1DeviceEP0TransferCompleted <= USED 0
USB1DeviceEndpointAbort <= USED 0
USB1DeviceEndpointClose <= USED 0
USB1DeviceEndpointGetHWCfg <= USED 0
USB1DeviceEndpointMultiTransmit <= USED 0
USB1DeviceEndpointOpen <= USED 0
USB1DeviceEndpointReceive <= USED 0
USB1DeviceEndpointReceiveCompleted <= USED 0
USB1DeviceEndpointReceiveCompletedExt <= USED 0
USB1DeviceEndpointStall <= USED 0
USB1DeviceEndpointTransmit <= USED 0
USB1DeviceEventNotify <= USED 0
USB1DeviceIsControllerEnabled <= USED 0
USB1DeviceMultiTransmitNotifyFn <= USED 0
USB1DevicePhase1Init <= USED 0
USB1DevicePhase2Init <= USED 0
USB1DeviceReceiveNotifyFn <= USED 0
USB1DeviceRegister <= USED 0
USB1DeviceRegisterPatchTempFunc <= USED 0
USB1DeviceTransmitNotifyFn <= USED 0
USB1DeviceVendorClassResponse <= USED 0
;FILE usb2_device.o
Check_AP2CP_Conflict <= USED 0
ClearResumePending <= USED 0
EnablePhy28 <= USED 0
GPIOED_LISR <= USED 0
GPIO_InitWakeup <= USED 0
GetResumePending <= USED 0
GetUsbDDRLock <= USED 0
Get_GPIO_WakeupLevel <= USED 0
OSCR0IntervalInMicro_self <= USED 0
PauseMsec <= USED 0
SendUsbRemoteWakeup <= USED 0
SetUsbDDRLock <= USED 0
SetUsbPmLock <= USED 0
USB2DeviceDisableUsbInterrupt <= USED 0
USB2DevicePhase1Init <= USED 0
USB2GetDTDEntry <= USED 0
USB2SuspendHISR <= USED 0
USB2UnPlugHISR <= USED 0
USB_GPIO_WK <= USED 0
USB_WAKEUP_CALLBACK_REGISTER <= USED 0
_usb_cancel_all_transfer <= USED 0
checkUSBconnection <= USED 0
usbCheckConnectHisr <= USED 0
usbDeviceEndpoint_init <= USED 0
usbWakeUpHisr <= USED 0
usb_get_dtd_nums <= USED 0
;FILE usb_descriptor.o
USB2ConfigureCdromDescriptor <= USED 0
USB2IsCdromOnlyDescriptor <= USED 0
USB2MgrDeviceUnplugPlug_SD <= USED 0
USB2MgrDeviceUnplugPlug_WebDav <= USED 0
USB2MgrMassStorageEnable <= USED 0
USB2ReEnumerate <= USED 0
USBIsConnected <= USED 0
get_current_usb_app_mask <= USED 0
;FILE usb_device.o
USBCableDetectionNotify <= USED 0
USBDeviceGetUSBSpeedInUse <= USED 0
USBDeviceGetUSBVersionInUse <= USED 0
USBDevicePhase1Init <= USED 0
USBDeviceRegister <= USED 0
USBDeviceRegisterPatchTempFunc <= USED 0
USBDeviceSetDescriptors <= USED 0
USBDeviceStatusGet <= USED 0
;FILE usb_init.o
mvUsbCheckProdctionMode <= USED 0
mvUsbGetRndisbitrate <= USED 0
;FILE usbcomdv.o
usbCommDevInitialise <= USED 0
usbCommDevMaxOutTransfer <= USED 0
;FILE usbcomnotify.o
usbCommNotifyConfigure <= USED 0
usbCommNotifyInitialise <= USED 0
usbCommNotifyTxSerialState <= USED 0
;FILE usbmgrttpal.o
USBMgrTTPALGetFromQ <= USED 0
USBMgrTTPALUpdateEnabledApps <= USED 0
UsbGetRequestID <= USED 0
UsbMgrTTPALInit <= USED 0
UsbMgrTTPALPhysical2logicalEP <= USED 0
UsbMgrTTPALRegister <= USED 0
UsbMgrTTPALSetMuxUART <= USED 0
UsbMgrTTPALSetMuxUART_INT <= USED 0
UsbMgrTTPALSetMuxUSB <= USED 0
UsbMgrTTPALSetMuxUSB_INT <= USED 0
usbAppifDefaultRxBufferStructure <= USED 0
usbAppifDynamicSelectionValid <= USED 0
usbAppifFlushRxBuffer <= USED 0
usbAppifFlushTransmitRequestQueue <= USED 0
usbAppifGetDefaultConfiguration <= USED 0
usbAppifInitRxBuffer <= USED 0
usbAppifInitTxQueue <= USED 0
usbAppifPauseRxFlow <= USED 0
usbAppifRequestReceiveBufferFreeSpace <= USED 0
usbAppifRequestReceiveData <= USED 0
usbAppifSelectDynamicConfiguration <= USED 0
usbAppifStallInEndpoint <= USED 0
usbAppifStallOutEndpoint <= USED 0
usbHandleError <= USED 0
usbTargetTask1 <= USED 0
;FILE usbnet.o
ModeVolteDLParseCidToPacketType <= USED 0
usbnet_allocmem_extend <= USED 0
usbnet_putmem <= USED 0
;FILE usim.o
GetUSIMDDRLock <= USED 0
TestUsimGetStatus <= USED 0
USIMATRGet <= USED 0
USIMPhase1Init <= USED 0
USIMRxFIFO_Clear <= USED 0
USIMStatusGet <= USED 0
USIMVersionGet <= USED 0
USIMWarmReset <= USED 0
USIM_DetectFoundByHW <= USED 0
get__USIMDLstates_string <= USED 0
get_sim_swap_flag <= USED 0
;FILE usim_d2.o
;FILE usim_dl.o
;FILE usim_hw.o
USIM_HW_RXRead_Dummy <= USED 0
USIM_HW_init <= USED 0
;FILE usim_transport.o
;FILE usimcfg.o
;FILE usimcmd.o
SimUiccExecuteGetChallengeCommand <= USED 0
SimUiccExecuteIncreaseCommand <= USED 0
SimUiccExecuteRunGsmAlgCommand <= USED 0
;FILE usimdec.o
SimUiccDecodeAasRecord <= USED 0
SimUiccDecodeAasRecordNumber <= USED 0
SimUiccDecodeCallDuration <= USED 0
SimUiccDecodeCallInfo <= USED 0
;FILE usimemu.o
SimEmuCloseImageFile <= USED 0
SimEmuSaveImage <= USED 0
SimemuSendCardRemoveInd <= USED 0
;FILE usimenc.o
SimUiccEncodeAas <= USED 0
SimUiccEncodeAct <= USED 0
SimUiccEncodeGas <= USED 0
SimUiccEncodeLi <= USED 0
SimUiccEncodeStartHfn <= USED 0
;FILE usiminit.o
;FILE usimmmreq.o
;FILE usimreqp1.o
SimUiccIsReadPlmnMore <= USED 0
;FILE usimreqp2.o
SimUiccSimEmuGenAccessReq <= USED 0
;FILE usimreqp3.o
SimUiccDeleteGasFromGrp <= USED 0
SimUiccDeleteGasReq <= USED 0
SimUiccDeleteGrpReq <= USED 0
SimUiccHiddenKeyFunctionReq <= USED 0
SimUiccListPbcReq <= USED 0
SimUiccReadHiddenKeyReq <= USED 0
SimUiccReadPbUidReq <= USED 0
SimUiccReadPbrRecReq <= USED 0
SimUiccWriteGasReq <= USED 0
;FILE usimrqp2p.o
;FILE usimstate.o
SetSimManagerExternalTasks <= USED 0
SimSendGLSwapFlag <= USED 0
;FILE usimutil.o
SimUiccDeleteExt1Chain <= USED 0
SimUiccReadPhonebookSynchronisationCounter <= USED 0
SimUiccSetHomezoneDir <= USED 0
;FILE ut_mcc_mnc.o
utmmPlIsIndianMcc <= USED 0
utmmPlPlmnsMatch <= USED 0
;FILE ut_sm.o
SmReadSemiOctetData <= USED 0
SmWriteSemiOctetData <= USED 0
utsmConvertOctetsToChars <= USED 0
utsmConvertUCS2ArrayToInt8Array <= USED 0
utsmGetHeaderLengthLen <= USED 0
utsmGetUserMessage <= USED 0
utsmGetUserMsgCharLen <= USED 0
utsmReadAddressData <= USED 0
utsmWriteAddressData <= USED 0
;FILE utbitfnc.o
;FILE utebmdyn.o
utEbmmAllocMemory <= USED 0
utEbmmFreeAllTaskMemory <= USED 0
utEbmmFreeMemory <= USED 0
;FILE util.o
util_del_var <= USED 0
;FILE utilities.o
DbgArrayPrintf <= USED 0
DbgEnableUart <= USED 0
DbgInitFifo <= USED 0
DbgInitUart <= USED 0
DbgLogTask <= USED 0
DbgPrintf_sim <= USED 0
DbgShow <= USED 0
DelayInMilliSecond <= USED 0
GetCraneLVbatNocali <= USED 0
GetGpioGroup <= USED 0
GetGpioLvl <= USED 0
GetTimer0CNT <= USED 0
LogFlush <= USED 0
RPCFuncRegister <= USED 0
ReadCCNT_with_PP <= USED 0
Read_Crane_Flash_UID <= USED 0
SetCraneLAdcWork <= USED 0
SetGpioLvl <= USED 0
Timer0IntervalInMicro <= USED 0
Timer0_Switch <= USED 0
cp_clear_gpio_ed <= USED 0
cp_gpio_init <= USED 0
cp_init_gpio_ed <= USED 0
date_to_utc <= USED 0
dump_stack_info <= USED 0
dump_task_info <= USED 0
get_cranel_adc_for_tsc <= USED 0
hsic_pm_gpio_setting <= USED 0
isChip_3601S <= USED 0
isChip_3621S <= USED 0
isInMMICodeRange <= USED 0
keypadConfigure <= USED 0
keypadNotifyEventBind <= USED 0
log_sdcard_set_partition <= USED 0
nvm_printf <= USED 0
recordPreExcepInfo <= USED 0
record_pure_resume_context <= USED 0
record_pure_suspend_context <= USED 0
rti_SendQueue_record <= USED 0
rti_SetEvent_record <= USED 0
rti_dump_ipc_hisr <= USED 0
rti_dump_ipc_lisr <= USED 0
rti_get_eehandler_magic <= USED 0
rti_icu_switch_to <= USED 0
rti_init_mode <= USED 0
rti_pm_sleep_state <= USED 0
rti_pm_sleep_step <= USED 0
rti_pm_sleep_time <= USED 0
rti_printf <= USED 0
rti_record_aam_status <= USED 0
rti_record_line <= USED 0
rti_set_CPMnewPowerState <= USED 0
rti_set_wakeupBits <= USED 0
rti_switch_check_data <= USED 0
rti_user_stamp <= USED 0
rti_user_value <= USED 0
sdcard_fat_fs_is_ready <= USED 0
seagull_uart_init_diag <= USED 0
sw_jtag_cmd <= USED 0
switch_to <= USED 0
timerTCRconfigureForSync <= USED 0
timerTMRsetForSync <= USED 0
uart_dump <= USED 0
;FILE utils_c.o
GetCpuFamily <= USED 0
;FILE utl_trmsg.o
;FILE utlconvert.o
;FILE utllinkedList.o
utlDoGetTailNode <= USED 0
utlPutHeadList <= USED 0
utlPutTailList <= USED 0
;FILE utlsemaphore.o
utlAcquireSharedAccess <= USED 0
utlInitSemaphore_queue <= USED 0
utlReleaseSharedAccess <= USED 0
;FILE utltime.o
utlCurrentTime <= USED 0
utlFormatDate <= USED 0
utlFormatDateTime <= USED 0
utlFormatTime <= USED 0
utlPause <= USED 0
;FILE utltimer.o
utlIsTimerRunning <= USED 0
utlQueryTimer <= USED 0
utlStartAbsoluteTimer <= USED 0
;FILE utltrace.o
utlAddTraceCriteria <= USED 0
utlDeleteTraceCriteria <= USED 0
utlDumpTracePegs <= USED 0
utlSetProcessName <= USED 0
;FILE utper.o
PerDecEndOfBitAlignedDataWithLength <= USED 0
PerDecSignedInt32 <= USED 0
PerDecStartOfBitAlignedDataWithLength <= USED 0
PerDecVariableOctetStringWithoutLength <= USED 0
PerEncConstrainedLengthWithoutMax <= USED 0
PerEncEndOfBitAlignedDataWithLength <= USED 0
PerEncEndOfBitAlignedDataWithLengthCap <= USED 0
PerEncSignedInt16 <= USED 0
PerEncSignedInt32 <= USED 0
PerEncStartOfBitAlignedDataWithLength <= USED 0
PerEncStartOfBitAlignedDataWithLengthCap <= USED 0
PerEncUnconstrainedBitString <= USED 0
PerEncVariableOctetString <= USED 0
PerEncVariableOctetStringWithoutMax <= USED 0
PerReturnLteCapExceed <= USED 0
PerReturnLteCapExceed1 <= USED 0
PerReturnLteCapExceed2 <= USED 0
PerReturnLteCapExceed2Initialized <= USED 0
PerSetLteCapExceed <= USED 0
PerSetLteCapExceed1 <= USED 0
PerSetLteCapExceed2 <= USED 0
PerSetLteCapExceedInit <= USED 0
PerSkipUnconstrainedBitString <= USED 0
;FILE utte_cfg.o
TeGetDataId <= USED 0
TeGetDataSubId <= USED 0
UtteCloseCheck <= USED 0
UtteOpenCheck <= USED 0
;FILE utte_fnc.o
TeClose <= USED 0
TeDestroySignal <= USED 0
TeFlush <= USED 0
TeOpen <= USED 0
TeReceiveSignalPoll <= USED 0
TeWaitForSignal <= USED 0
;FILE utuc_fnc.o
mapUcs2ToChar <= USED 0
utByteAlignedEGsm7BitToUcs2 <= USED 0
utByteAlignedGsm7BitToChar <= USED 0
utDoubleOctetToUcs2 <= USED 0
utGetEGsmLengthOfUcs2 <= USED 0
utIsUcs2StringAllEGsm <= USED 0
utLengthAlphaIdToUcs2 <= USED 0
utSmsUcs2ToByteAlignedEGsm7Bit <= USED 0
utUcs2StringToDebug <= USED 0
utUcs2ToAlphaId <= USED 0
utUcs2ToByteAlignedEGsm7Bit <= USED 0
utUcs2ToChar <= USED 0
utUcs2ToDoubleOctet <= USED 0
;FILE vad1.o
;FILE version.o
GetSeagullFWVersionAndDate <= USED 0
GetUUID <= USED 0
;FILE vgmxusbnull.o
VgMuxUsbNullTask1 <= USED 0
usbCommDevAppInitialise <= USED 0
;FILE vpath_ctrl.o
CraneCodecADCOnOff_internal <= USED 0
vpathCTMControl <= USED 0
vpathCodecClockSet <= USED 0
vpathDebugCmd <= USED 0
vpathDmaIntInd <= USED 0
vpathGSSPControl <= USED 0
vpathGSSPRead <= USED 0
vpathIsPcmSelfInvoked <= USED 0
vpathKWSControl <= USED 0
vpathPlayStream <= USED 0
vpathRecordStream <= USED 0
vpathRxbControl <= USED 0
vpathSetSelfInvocation <= USED 0
vpathVadDumpControl <= USED 0
vpathVoiceControl <= USED 0
;FILE vpath_data.o
;FILE vpath_handler.o
IMSBindRxFrame <= USED 0
amrBindRxPcmVoLTE <= USED 0
amrBindRxRequest <= USED 0
handleAudioIslandCompanderReport <= USED 0
handleAudioIslandRxAMR <= USED 0
handleAudioIslandTxAMR <= USED 0
;FILE vpath_mgr.o
audioGsmResumeCB_L1G <= USED 0
audioGsmResumeCB_internal <= USED 0
audioGsmSuspendCB_L1G <= USED 0
audioGsmSuspendCB_internal <= USED 0
audioGsmTestStartCB <= USED 0
audioGsmTestStopCB <= USED 0
vpathOsResourcesCreate <= USED 0
;FILE wamr_rate.o
IMSSourceRateControl <= USED 0
;FILE wamr_rx.o
;FILE wamr_services.o
TxPocAMRGet <= USED 0
amrBindConnectionClose <= USED 0
amrBindConnectionEstablish <= USED 0
audioVoiceHandleTaskNotify <= USED 0
ctmNegotiationReportBind <= USED 0
getGlobalDTMFCode <= USED 0
voiceDataGetDataPointer <= USED 0
voiceDataNotifyDataReceived <= USED 0
voiceDataNotifyDataSent <= USED 0
voiceDataSendChannelAck <= USED 0
;FILE wamr_tx.o
IMSBindTxFrame <= USED 0
amrBindTxBitstreamFrameVoLTE <= USED 0
amrBindTxFrame <= USED 0
;FILE watchdog.o
PMIC_WD_TIMER_SET_DYNAMIC <= USED 0
watchdogIndicationClear <= USED 0
;FILE webrtc_neteq.o
WebRtcNetEQ_CodecDbAdd <= USED 0
WebRtcNetEQ_CodecDbGetCodecInfo <= USED 0
WebRtcNetEQ_CodecDbGetSizeInfo <= USED 0
WebRtcNetEQ_CodecDbRemove <= USED 0
WebRtcNetEQ_CodecDbReset <= USED 0
WebRtcNetEQ_ContextDestroy <= USED 0
WebRtcNetEQ_GetErrorCode <= USED 0
WebRtcNetEQ_GetErrorName <= USED 0
WebRtcNetEQ_SetExtraDelay <= USED 0
WebRtcNetEQ_StreamDBReset <= USED 0
WebRtcNetEQ_StreamDbRemove <= USED 0
WebRtcNetEQ_strncpy <= USED 0
;FILE weight_a.o
;FILE wificontroll.o
FakeMM2WifiStartScanReqExt3 <= USED 0
;FILE wifispy.o
;FILE wmf_to_ets.o
;FILE x509.o
mbedtls_x509_dn_gets <= USED 0
mbedtls_x509_get_alg_null <= USED 0
mbedtls_x509_key_size_helper <= USED 0
mbedtls_x509_serial_gets <= USED 0
mbedtls_x509_sig_alg_gets <= USED 0
;FILE x509_crt.o
mbedtls_x509_crt_info <= USED 0
mbedtls_x509_crt_parse_der_nocopy <= USED 0
mbedtls_x509_crt_verify <= USED 0
mbedtls_x509_crt_verify_with_profile <= USED 0
mbedtls_x509_subjectid_gets <= USED 0
;FILE xcapclient.o
;FILE xdmc_api.o
Xdmc_Close <= USED 0
;FILE xdmc_common.o
Xdmc_Generate_CmdIdentifier <= USED 0
Xdmc_Generate_Listname <= USED 0
Xdmc_Get_Header <= USED 0
allocXdmcData <= USED 0
xdmc_random <= USED 0
;FILE xdmc_core.o
Xdmc_CleanNc <= USED 0
Xdmc_Init_DNS <= USED 0
;FILE xdmc_dns.o
;FILE xdmc_selector.o
addDocSelectorStr <= USED 0
;FILE xllp_dmac.o
XllpDmacClearDmaInterrupt <= USED 0
XllpDmacDescriptorFetch <= USED 0
XllpDmacDisableEORInterrupt <= USED 0
XllpDmacEnableEORInterrupt <= USED 0
XllpDmacEnableStopIrqInterrupt <= USED 0
configDescriptor <= USED 0
loadDescriptor <= USED 0
;FILE xmlConfInfoParser.o
;FILE xmlConfRecipientListParser.o
XmlCRList_ParseDoc <= USED 0
;FILE xrabmmain.o
GpLlc2Task <= USED 0
GpLlc2Task1 <= USED 0
GpLlcTask <= USED 0
GpLlcTask1 <= USED 0
UpDlbg2Task1 <= USED 0
UpDlbgTask1 <= USED 0
;FILE xrabmutil.o
XRabmUpdatePdpState <= USED 0
;FILE xscale_stubs.o
ACDAccessoryDetectInitialise <= USED 0
ACDAccessoryDetectStatus <= USED 0
ACDBind <= USED 0
PMUPhase2Init <= USED 0
bspBTClockReqEnable <= USED 0
bspShutDown <= USED 0
;FILE xsmmain.o
;FILE ymodem.o
