﻿/*********************************************************
*  @file    bsp_network.h
*  @brief   A7680C的网络查询、设定，以网络任务的形式单独体现。
*  Copyright (c) 2023 TNEGEN.
*  All rights reserved.
*  created by Delei 2022/12/30
********************************************************/
#ifndef __BSP_NETWORK_H__
#define __BSP_NETWORK_H__



#define SIMCARD_URC_RECIVE_TIME_OUT 3000
#define STOP_NETWORK_MSG_ID         101
#define REBOOT_NETWORK_MSG_ID       102

#define ICCID_BUF_LEN               64

#define NTP_ADDR1                    "ntp3.aliyun.com"
#define NTP_ADDR2                    "ntp.ntsc.ac.cn"
#define NTP_ADDR3                    "ntp.tencent.com"
#define NTP_ADDR4                    "time.edu.cn"
#define NTP_ADDR5                    "ntp.aliyun.com"

void bsp_network_config(void *arg);
osStatus_t get_rssi(char *pRSSI);
osStatus_t get_rsrp_rsrq_sinr(char *pRSRP, char *pRSRQ, char *pSINR);
void cm_get_iccid(void);
uint64_t GetProAbsSubSecond(void);
void getmodeminfo();

#endif
