/*
 * Copyright (C) 2019-2030 tengen Group Holding Limited
 *@file memory_gw.h: tengen smart gateway memory define
 * */
#ifndef _MEMORY_GW_H_
#define _MEMORY_GW_H_

#include "topic_manage.h"

#define MAX_REGNUM      (512)

//三元组烧写或者读取时使用的状态
typedef enum e_STATE
{
    IDLE = 0,
    READIOTINFO,
    WRITEOK,
    WRITEERR,
    CRCERR,
    READDEVICEINFO,
    READDEVICEVER,
    UPGRADE,
    TEST_PCB,     //PCB测试
    TEST_OVERALL, //整机测试
    GET_TEST_RES, //获取测试结果
    PASSTHOUGH_MOD//透传模式
}IOT_STATE;

#define NVS_KEY_BKNUM_CONFIG "break_num"
#define NVS_KEY_BKTHREE_ELEM "bk_elem"
#define NVS_KEY_GWTHREE_ELEM "gw_elem"

#define NVS_KEY_GWATTR_ON  "attr_on"
#define NVS_KEY_GWATTR_OFF  "attr_off"
#define NVS_KEY_GWATTR_onBeginTime  "on_begin"
#define NVS_KEY_GWATTR_onEndTime  "on_end"
#define NVS_KEY_GWATTR_offBeginTime  "off_begin"
#define NVS_KEY_GWATTR_offEndTime  "off_end"
#define DEFAULT_ON_CTRL_ENABLE 1
#define DEFAULT_OFF_CTRL_ENABLE 1
//break time control enable
#define NVS_KEY_BK_CTL  "bk_en"

//变化阈值(默认值)
#define MIN_ALARM_INTERVAL 60     //filter the same alarm with in 10 seconds
#define DEAD_BAND_U 5             //V
#define DEAD_BAND_I 2             //A
#define DEAD_BAND_LEAKI 5         //mA
#define DEAD_BAND_ENERGY 0.1      //kWh
#define DEAD_BAND_POWER 0.4       //kW
#define DEAD_BAND_TEMPRATURE 1    //oc


//define local times
typedef struct
{
    int hours;
    int minutes;
    int seconds;
}str_TIME;


typedef struct
{
	str_TIME onBeginTime;
	str_TIME onEndTime;
	str_TIME offBeginTime;
	str_TIME offEndTime;
	uint8_t onCtrlEnable;
	uint8_t offCtrlEnable;
} __packed str_GW_ATTR;

struct Tag_8BIT_8
{
    uint8_t  NULL_bit63:1;
    uint8_t  NULL_bit62:1;
    uint8_t  NULL_bit61:1;
    uint8_t  NULL_bit60:1;
    uint8_t  NULL_bit59:1;
    uint8_t  NULL_bit58:1;
    uint8_t  NULL_bit57:1;
    uint8_t  NULL_bit56:1;
};
struct Tag_8BIT_7
{
    uint8_t  NULL_bit55:1;
    uint8_t  NULL_bit54:1;
    uint8_t  NULL_bit53:1;
    uint8_t  NULL_bit52:1;
    uint8_t  NULL_bit51:1;
    uint8_t  NULL_bit50:1;
    uint8_t  NULL_bit49:1;
    uint8_t  NULL_bit48:1;
};
struct Tag_8BIT_6
{
    uint8_t  NULL_bit47:1;
    uint8_t  NULL_bit46:1;
    uint8_t  NULL_bit45:1;
    uint8_t  NULL_bit44:1;
    uint8_t  NULL_bit43:1;
    uint8_t  NULL_bit42:1;
    uint8_t  NULL_bit41:1;
    uint8_t  NULL_bit40:1;
};
struct Tag_8BIT_5
{
    uint8_t  NULL_bit39:1;
    uint8_t  NULL_bit38:1;
    uint8_t  NULL_bit37:1;
    uint8_t  NULL_bit36:1;
    uint8_t  NULL_bit35:1;
    uint8_t  NULL_bit34:1;
    uint8_t  NULL_bit33:1;
    uint8_t  NULL_bit32:1;
};
struct Tag_8BIT_4
{
    uint8_t NULL_bit31:1;
    uint8_t SC_TRIP_bit30:1;
    uint8_t OVER_CURRENT_LIMITS_bit29:1;
    uint8_t OVER_CURRENT_LIMITS_C_bit28:1;
    uint8_t OVER_CURRENT_LIMITS_B_bit27:1;
    uint8_t OVER_CURRENT_LIMITS_A_bit26:1;
    uint8_t OVER_POWER_bit25:1;
    uint8_t OVER_POWER_C_bit24:1;
};
struct Tag_8BIT_3
{
    uint8_t OVER_POWER_B_bit23:1;
    uint8_t OVER_POWER_A_bit22:1;
    uint8_t UNDER_VOLTAGE_bit21:1;
    uint8_t UNDER_VOLTAGE_C_bit20:1;
    uint8_t UNDER_VOLTAGE_B_bit19:1;
    uint8_t UNDER_VOLTAGE_A_bit18:1;
    uint8_t OVER_VOLTAGE_bit17:1;
    uint8_t OVER_VOLTAGE_C_bit16:1;
};

struct Tag_8BIT_2
{
    uint8_t OVER_VOLTAGE_B_bit15:1;
    uint8_t OVER_VOLTAGE_A_bit14:1;
    uint8_t OVER_LOAD_bit13:1;
    uint8_t OVER_LOAD_N_bit12:1;
    uint8_t OVER_LOAD_C_bit11:1;
    uint8_t OVER_LOAD_B_bit10:1;
    uint8_t OVER_LOAD_A_bit9:1;
    uint8_t OVER_HEAT_TRIP_bit8:1;
};

struct Tag_8BIT_1
{
    uint8_t OVER_HEAT_N_bit7:1;
    uint8_t OVER_HEAT_C_bit6:1;
    uint8_t OVER_HEAT_B_bit5:1;
    uint8_t OVER_HEAT_A_bit4:1;
    uint8_t FIRE_MAKE_bit3:3;//
    uint8_t LOCKED_ROTOR_bit2:1;//
    uint8_t SURGE_bit1:1;
    uint8_t LEAK_ELEC_bit0:1;
};

struct tag_ALARM
{
    struct Tag_8BIT_8 byte8;//other alarm judge by gateway
    struct Tag_8BIT_7 byte7;//other alarm judge by gateway
    struct Tag_8BIT_6 byte6;//other alarm judge by gateway
    struct Tag_8BIT_5 byte5;//other alarm judge by gateway
    struct Tag_8BIT_4 byte4;//alarm4 generated by breaker
    struct Tag_8BIT_3 byte3;//alarm3 generated by breaker
    struct Tag_8BIT_2 byte2;//alarm2 generated by breaker
    struct Tag_8BIT_1 byte1;//alarm1 generated by breaker
};

struct AlarmEvent
{
	//TODO:当前系统时间戳 ms
	uint64_t timestamp;
	uint16_t Status;
	uint16_t timeSpan; // 距离告警发生的时间 单位:秒
	uint16_t iVal; 
};



typedef struct
{
    uint8_t ucDIVal; //break status 1:on,0:off
    uint8_t ucCloudDIVal; //break status 1:on,0:off

    uint8_t onRes;//微断合闸原因
    uint8_t onResCloud;
    uint8_t offRes;//微断分闸原因
    uint8_t offResCloud;

    int iOrgRemainCurrent;//leak current

    float fRemainCurrent;
    float fCloudRemainCurrent;
    float fleakwarn;

    struct tag_ALARM currentALarm;
    struct AlarmEvent alarmEvent[5];


    uint32_t iKwhAll;
    float fKwhAll;//=iKwhAll/1000,都是表示总电量
    float fCloudKwhAll;

	uint16_t powerDirection; // 功率方向
    int iOrgPsum;
    float fPsum;
    float fCloudPsum;
    //phase A
    uint32_t iIa;
    float fIa;
    float fCloudIa;

    int iTempPha;
    float  fTempPha;
    float  fCloudTempPha;

    int iOrgUa;
    float fUa;
    float iCloudUa;

    int iDemandIa;//not used
    float fDemandIa;

    uint32_t iKwhPha;
    float fKwhPha; //=iKwhPha/1000
    float fCloudKwhPha;

    int iOrgPhaP;//A相功率
    float fPhaP;
    float fCloudPhaP;
    //phase B
    uint32_t iIb;
    float fIb;
    float fCloudIb;

    int iTempPhb;
    float  fTempPhb;
    float  fCloudTempPhb;

    int iOrgUb;
    float fUb;
    float iCloudUb;

    int iDemandIb;//not used
    float fDemandIb;

    uint32_t iKwhPhb;
    float fKwhPhb;
    float fCloudKwhPhb;

    int iOrgPhbP;
    float fPhbP;
    float fCloudPhbP;
    //phase C
    uint32_t iIc;
    float fIc;
    float fCloudIc;

    int iTempPhc;
    float  fTempPhc;
    float  fCloudTempPhc;

    int iOrgUc;
    float fUc;
    float iCloudUc;

    int iDemandIc;//not used
    float fDemandIc;

    uint32_t iKwhPhc;
    float fKwhPhc;
    float fCloudKwhPhc;

    int iOrgPhcP;
    float fPhcP;
    float fCloudPhcP;
    //phase N
    uint32_t iIn;
    float fIn;
    float fCloudIn;

    int iTempPhn;
    float  fTempPhn;
    float  fCloudTempPhn;
    

	  int	 ifreq;	//交流电压频率
	  float  ffreq;
	  int  	 iCloudfreq;

	  uint16_t iPFa;		//A相功率因素
	  uint16_t iCloudPFa;
	  uint16_t iPFb; 		//B相功率因素
	  uint16_t iCloudPFb;
	  uint16_t iPFc; 		//C相功率因素
	  uint16_t iCloudPFc;
	  uint16_t iPFsum; 	//总功率因素
      uint16_t iCloudPFsum;
      uint8_t Lock;		//闭锁功能
     uint8_t CloudLock;

    bool latestOffFlag; //remote control flag
    bool remoteCtrlEn;    //远程分合闸使能
    bool NoErrFlag;       //当前微断没有告警的标志
    int errcode;          //用于记录已经处理过的告警序号
    uint8_t error[8];     //用于记录已经上送过的告警字

  
    uint8_t openPhaseStatus;//缺相状态
    uint8_t iCloudOpenPhaseStatus;//云端缺相状态
}str_BKDATA;

typedef enum
{
	E_BK_1P = 0,//
	E_BK_1P_RES,
	E_BK_2P,
	E_BK_2P_LEAK,//
	E_BK_3P,//
	E_BK_3P_LEAK,
	E_BK_4P,
	E_BK_4P_LEAK,//
	E_BK_MCCB
}E_BKTYPE_OLD;

//分闸原因表
typedef enum
{
    SUB_OFF_MAN_SC = 1,   //小类-手动/短路分闸
    SUB_OFF_BTN,          //小类-按钮分闸
    SUB_OFF_YK,           //小类-遥控分闸
    SUB_OFF_TIM,          //小类-定时控制分闸
    SUB_OFF_PERIOD,       //小类-时段控制分闸
    SUB_OFF_OVER_TEMP,    //小类-过温保护分闸
    SUB_OFF_STA_PHA,      //小类-缺相保护分闸
    SUB_OFF_OVER_RCR,     //小类-过额定电流保护分闸
    SUB_OFF_OVER_SCR,     //小类-过设定电流保护分闸
    SUB_OFF_OVER_SPW,     //小类-过设定功率保护分闸
    SUB_OFF_LOCK,         //小类-闭锁打开导致分闸
    SUB_OFF_OVER_V1,      //小类-第1次过压保护分闸
    SUB_OFF_OVER_V2,      //小类-第2次过压保护分闸
    SUB_OFF_OVER_V3,      //小类-第3次过压保护分闸
    SUB_OFF_OVER_V4,      //小类-第4次过压保护分闸
    SUB_OFF_LESS_V1,      //小类-第1次欠压保护分闸
    SUB_OFF_LESS_V2,      //小类-第2次欠压保护分闸
    SUB_OFF_LESS_V3,      //小类-第3次欠压保护分闸
    SUB_OFF_LESS_V4,      //小类-第4次欠压保护分闸
    SUB_OFF_LEAK_I1,      //小类-第1次漏电保护分闸
    SUB_OFF_LEAK_I2,      //小类-第2次漏电保护分闸
    SUB_OFF_LEAK_I3,      //小类-第3次漏电保护分闸
    SUB_OFF_LEAK_I4,      //小类-第4次漏电保护分闸
    SUB_OFF_LEAK_I5,      //小类-第5次漏电保护分闸
    SUB_OFF_LEAK_I6,      //小类-第6次漏电保护分闸

    BIG_OFF_MAN_SC = 101, //大类-手动/短路分闸
    BIG_OFF_BTN,          //大类-按钮分闸
    BIG_OFF_YK,           //大类-遥控分闸
    BIG_OFF_TIM_P,        //大类-定时控制分闸
    BIG_OFF_TIM,          //大类-时段控制分闸
    BIG_OFF_FAIL,         //大类-故障分闸
    BIG_OFF_LOCK,         //大类-闭锁分闸
    BIG_OFF_OVER_V,       //大类-过压分闸
    BIG_OFF_LESS_V,       //大类-欠压分闸
    BIG_OFF_LEAK_I        //大类-漏电分闸
}ACT_OFF_RES;

//合闸原因表
typedef enum
{
    SUB_ON_MAN = 1,      //小类-手动合闸
    SUB_ON_BTN,          //小类-按钮合闸
    SUB_ON_YK,           //小类-遥控合闸
    SUB_ON_TIM,          //小类-定时控制合闸
    SUB_ON_PERIOD,       //小类-时段控制合闸
    SUB_ON_OVER_V1,      //小类-第1次过压保护合闸
    SUB_ON_OVER_V2,      //小类-第2次过压保护合闸
    SUB_ON_OVER_V3,      //小类-第3次过压保护合闸
    SUB_ON_LESS_V1,      //小类-第1次欠压保护合闸
    SUB_ON_LESS_V2,      //小类-第2次欠压保护合闸
    SUB_ON_LESS_V3,      //小类-第3次欠压保护合闸
    SUB_ON_LEAK_I1,      //小类-第1次漏电保护合闸
    SUB_ON_LEAK_I2,      //小类-第2次漏电保护合闸
    SUB_ON_LEAK_I3,      //小类-第3次漏电保护合闸
    SUB_ON_LEAK_I4,      //小类-第4次漏电保护合闸
    SUB_ON_LEAK_I5,      //小类-第5次漏电保护合闸

    BIG_ON_MAN_SC = 201, //大类-手动/短路合闸
    BIG_ON_BTN,          //大类-按钮合闸
    BIG_ON_YK,           //大类-遥控合闸
    BIG_ON_TIM_P,        //大类-定时控制合闸
    BIG_ON_TIM,          //大类-时段控制合闸
    BIG_ON_OVER_V,       //大类-过压合闸
    BIG_ON_LESS_V,       //大类-欠压合闸
    BIG_ON_LEAK_I        //大类-漏电合闸
}ACT_ON_RES;
// breaker type
typedef struct
{
    uint8_t PNum;               //微断的P数
    uint8_t bAdcType;           //AC:0,DC:1
    uint8_t inlineMod;          //上进下出：0；下进上出：1
    uint8_t secLeveAlarmEn;     //是否支持二级阈值的标志位
    uint8_t leakageRecloseEn;   //是否支持漏电重合闸功能的标志位
    uint8_t overVolRecloseEn;   //是否支持过欠压重合闸功能的标志位
} E_BKTYPE;

typedef struct {
    // 设备信息
//read for user,read and write for factory FE 06
	uint16_t deviceType;
    uint32_t manufacturer;
	
    E_BKTYPE type;//break type
	E_BKTYPE eCloudtype;
	uint32_t breakId;//break ID
	uint8_t alarmLevel; //2ji
    uint8_t alarmLevelPow;//1:使能二级功率阈值，0：无二级功率阈值
    uint8_t NoalarmEventFlag;//1:无告警事件记录寄存器,0:有告警事件记录寄存器
    
	char pruductName[64];//product name
	uint32_t productDate;//product date
	int iHighU;//high voltage Limit
	int iCloudHighU;
	int iLowU;//low voltage Limit
	int iCloudLowU;
	uint32_t iHighLeakI;//high leak current
	uint32_t mMaxDemandI;//max demand current
    uint32_t mMaxLeakI;//最大一级漏电值

	float fRatioUa; //V
	float fRatioUb; //V
	float fRatioUc; //V
	float fRatioIa; //A
	float fRatioIb; //A
	float fRatioIc; //A
	float fRatioIn; //A
	float fRatioLeakI; //mA
	float fRatioPower; //oC
	float fRatioKwh; //kWh
	int iRatePsum;//rated Power

	//read and write for user, xx 06
	uint32_t iMeterAddr;//address
	uint32_t iCloudMeterAddr;
	uint16_t instruction;//分合闸指令

	float fMaxTemprature;//max temprature
	float fCloudMaxTemprature;

	int iRatedI;//rated current
	int iCloudRatedI;

	int iProtectState;//protect function state
	int iCloudProtectState;

    int iCurrentLimit;//current limits
    int iCloudCurrentLimit;

    int iPowerLimit;//power limits
    int iCloudPowerLimit;
	
	
    //modbus para
	TASKTYPE modbus_status;//modbus status type
	int modbus_failedCount;//modbus communicating failed count,not used yet
	int modbus_runFlag;//modbus failed,微断状态在线(1)还是离线(0)
	int iCloud_runFlag;//iCloud failed,微断云端在线(1)还是离线(0)
	bool bOnline_update;//online update firmware
    bool bTime_ctrl_flag;
	bool bAdcType;		//AD/DC break
	uint8_t ver[2];		//ver[0]:硬件版本，ver[1]:软件版本
	uint8_t ucTime_ctrl_en;//bit0:onEnable,bit1:offEnable
	int fault_count;//fault control on count for locking break control
	bool fault_lockflag;//fault lock flag
	int fault_last_date;//save last fault date
	int st_begin_ontime;//start time


    bool dmp_en; // 备电使能
    bool iCloud_dmp_en;

    float dmp_tm; // 备电时长,单位小时
    float fCloud_dmp_tm;
    bool dmp_have_off_flag;


    bool dmp_break_off_flag;
    bool dmp_break_on_flag;

	//二级告警参数
	int tHighU;		  //过压二级阈值
	int iCloudtHighU; //过压二级阈值
	int tLowU;		  //欠压二级阈值
	int iCloudtLowU;  //欠压二级阈值
	int tIa;		  //A相电流二级阈值
	int iCloudtIa;	  //A相电流二级阈值
	int tIb;		  //B相电流二级阈值
	int iCloudtIb;	  //B相电流二级阈值
	int tIc;		  //C相电流二级阈值
	int iCloudtIc;	  //C相电流二级阈值
	int tIn;		  //N相电流二级阈值
	int iCloudtIn;	  //N相电流二级阈值
	int tLeakI;		  //漏电流二级阈值
	int iCloudtLeakI; //漏电流二级阈值
	int tTemp;		  //温度二级阈值
	int iCloudtTemp;  //温度二级阈值

	/*******重合闸***注意：只有2P和4P且使能位使能，才支持该功能********/
	uint8_t  LeakageRecloseEnable;
	uint16_t intvTime1;
	uint16_t CloudintvTime1;
	uint16_t intvTime2;
	uint16_t CloudintvTime2;
	uint16_t intvTime3;
	uint16_t CloudintvTime3;
	uint16_t intvTime4;
	uint16_t CloudintvTime4;
	uint16_t intvTime5;
	uint16_t CloudintvTime5;
	/*******重合闸 end**********/
    //漏电可调功能使能
    uint8_t  leakEn;
    uint8_t  CloudLeakEn;

	int tPwa;   //A相功率二级阈值(定制功能)
	int iCloudtPwa;
	int tPwb;	//B相功率二级阈值(定制功能)
	int iCloudtPwb;
	int tPwc;   //C相功率二级阈值(定制功能)
	int iCloudtPwc;
    
     //一级阈值(定制功能)
    int cIa;         //A相电流一级阈值(定制功能)
    int iCloudcIa;
    int cIb;         //B相电流一级阈值(定制功能)
    int iCloudcIb;
    int cIc;         //C相电流一级阈值(定制功能)
    int iCloudcIc;
    int cPwa;        //A相功率一级阈值(定制功能)
    int iCloudcPwa;
    int cPwb;        //B相功率一级阈值(定制功能)
    int iCloudcPwb;
    int cPwc;        //C相功率一级阈值(定制功能)
    int iCloudcPwc;
    int cHighUa;     //A相过压一级阈值(定制功能)
    int iCloudcHighUa;
    int cHighUb;     //B相过压一级阈值(定制功能)
    int iCloudcHighUb;
    int cHighUc;     //C相过压一级阈值(定制功能)
    int iCloudcHighUc;
    int cLowUa;      //A相欠压一级阈值(定制功能)
    int iCloudcLowUa;
    int cLowUb;      //B相欠压一级阈值(定制功能)
    int iCloudcLowUb;
    int cLowUc;      //C相欠压一级阈值(定制功能)
    int iCloudcLowUc;
    int cTa;         //A相过温一级阈值(定制功能)
    int iCloudcTa;
    int cTb;         //B相过温一级阈值(定制功能)
    int iCloudcTb;
    int cTc;         //C相过温一级阈值(定制功能)
    int iCloudcTc;
    int cTn;         //N相过温一级阈值(定制功能)
    int iCloudcTn;

} str_BKPARA;

typedef struct
{
    //add deadband for changed send to cloud
	float DeadBandU;		   //V
	float DeadBandI;		   //A
	float DeadBandLeakI;	   //mA
	float DeadBandT; 		   //oC
	float DeadBandKwh;		   //kWh
	float DeadBandPower;	   //kW
	int   SendCircleTime;	   //数据周期上报周期，单位s,默认300s = 5min
	int   SendChangeTime;	   //数据变化上报周期，单位s，默认1S
	int   SendScanTime;		   //数据查询周期，单位s，默认1S
    
} str_deadband;
typedef struct
{
    str_BKPARA para;
    str_BKDATA data;
}str_BK_ELEM;

typedef struct {
   uint32_t regCount;     //寄存器个数
    uint8_t reg[2];    //寄存器地址
    uint8_t regval[MAX_REGNUM];    //寄存器val
	uint8_t addr;    //breaker addr
}S_WRITE_PARA;

typedef struct
{
	TASKTYPE e_TaskType;
	uint8_t ucYkVal;	//yk
	int iParaVal;//set para,change high temprature,rated current,current limits,power limits
	int iAddress;
	uint8_t func;
	uint16_t regAddr;    
	S_WRITE_PARA writeVal;
	uint8_t nSimpleLock;//timeout 2000ms
} str_TASKINFO;

//rtata memory
typedef struct {
    str_BK_ELEM m_BKData[MAX_CB_NUM + 1]; //keep breaker data from address 1-MAX_CB_NUM
    int m_currbknum;
	str_GW_ATTR m_gw_attr;
} str_GW_MEM;


typedef struct
{
   uint8_t ucHighReg;
   uint8_t ucLowReg;
   uint8_t iRegHighNum;
   uint8_t iRegLowNum;
}REG_PARA;
//rtdata operation
void initMemory(void);
void SetOneBkVal(int address,str_BKDATA *pBKVal);
str_BKDATA * GetOneBkVal(int address);
void SetOneBkParaVal(int address,str_BKPARA *pBKVal);
str_BKPARA * GetOneBkParaVal(int address);
void SetOneBkType(int address,E_BKTYPE bkType);
E_BKTYPE  GetOneBkType(int address);
//check change yc
bool ChangedUa(const int address);
bool ChangedUb(const int address);
bool ChangedUc(const int address);

bool ChangedIa(const int address);
bool ChangedIb(const int address);
bool ChangedIc(const int address);
bool ChangedIn(const int address);

bool ChangedPhaP(const int address);
bool ChangedPhbP(const int address);
bool ChangedPhcP(const int address);
bool ChangedSumP(const int address);

bool ChangedTempPha(const int address);
bool ChangedTempPhb(const int address);
bool ChangedTempPhc(const int address);
bool ChangedTempPhn(const int address);

bool ChangedPFa(const int address);
bool ChangedPFb(const int address);
bool ChangedPFc(const int address);
bool ChangedPFsum(const int address);

bool ChangedKwhPha(const int address);
bool ChangedKwhPhb(const int address);
bool ChangedKwhPhc(const int address);
bool ChangedKwhSum(const int address);

bool ChangedLeakI(const int address);
bool ChangedLock(const int address);
//check yx changed
bool ChangedYx(const int address);
bool ChangedOpenPhaseStatus(const int address);
bool ChangeOnRes(const int address);
bool ChangeOffRes(const int address);
//check para changed
bool ChangedProtectState(const int address);
bool ChangedMaxTemprature(const int address);

bool ChangedCurrentLimit(const int address);
bool ChangedPowerLimit(const int address);

// bool ChangedCurrentLimitA(const int address);
// bool ChangedCurrentLimitB(const int address);
// bool ChangedCurrentLimitC(const int address);
// bool ChangedPowerLimitA(const int address);
// bool ChangedPowerLimitB(const int address);
// bool ChangedPowerLimitC(const int address);
bool ChangedAddr(const int address);
bool ChangedBkType(const int address);

bool ChangedHighU(const int address);
bool ChangedLowU(const int address);


// bool ChangedHighUa(const int address);
// bool ChangedHighUb(const int address);
// bool ChangedHighUc(const int address);
// bool ChangedLowUa(const int address);
// bool ChangedLowUb(const int address);
// bool ChangedLowUc(const int address);

bool ChangedtHighU(const int address);
bool ChangedtLowU(const int address);
bool ChangedtIa(const int address);
bool ChangedtIb(const int address);
bool ChangedtIc(const int address);
bool ChangedtIn(const int address);
bool ChangedtIn(const int address);
bool ChangedtLeakI(const int address);
bool ChangedtTemp(const int address);
//检查漏电重合时间时候变化
bool ChangedintvTime1(const int address);
bool ChangedintvTime2(const int address);
bool ChangedintvTime3(const int address);
bool ChangedintvTime4(const int address);
bool ChangedintvTime5(const int address);
bool ChangedLeakEn(const int address);

bool ChangedtPwa(const int address);
bool ChangedtPwb(const int address);
bool ChangedtPwc(const int address);
bool ChangedCIa(const int address);
bool ChangedCIb(const int address);
bool ChangedCIc(const int address);
bool ChangedCPwa(const int address);
bool ChangedCPwb(const int address);
bool ChangedCPwc(const int address);
bool ChangedCHighUa(const int address);
bool ChangedCHighUb(const int address);
bool ChangedCHighUc(const int address);
bool ChangedCLowUa(const int address);
bool ChangedCLowUb(const int address);
bool ChangedCLowUc(const int address);
bool ChangedCTa(const int address);
bool ChangedCTb(const int address);
bool ChangedCTc(const int address);
bool ChangedCTn(const int address);

//check state
bool ChangedState(const int address);

bool ChangedDmpEn(const int address);
bool ChangedDmpTm(const int address);
//task manager
typedef struct
{
    str_TASKINFO m_BKTask[MAX_TASK_NUM];
    uint16_t dTaskWriteP;
    uint16_t dTaskReadP;
}str_AllTASKMGR;

//task operation
void TaskLock(uint8_t* uclock);
void TaskUnLock(uint8_t* uclock);
void InitTask(void);
bool HaveNewTask(void);
void AddOneTaskRecord(str_TASKINFO *task);
str_TASKINFO * GetOneTask(void);
int GetReadableTaskLen(void);

// linkkit cloud task
typedef enum
{
    E_SEND_NULL = -1,
    E_SEND_DISPATCHING = 0,
    E_SEND_EVENT,//告警事件
    E_SEND_ADDSUBDEV,
    E_SEND_LOGIN,
    E_SEND_LOGOUT,
    E_AWSS_REPORT_RESET,
    E_SEND_ALLPARA,
    E_SEND_MB_REP
}E_LINKTASK;


typedef struct
{
   bool b_DispatchFailed;
   int n_DispatchNum;
}str_DISPATCHINFO;

typedef struct
{
    struct tag_ALARM alarm_words;
    int iBkAddress;
}str_ALARMINFO;

typedef struct
{
    E_LINKTASK tasktype;
    str_DISPATCHINFO  dispResult;
    str_ALARMINFO  oneAlarm;
//    uint64_t time_gen_sec;//alarm generated time
    uint8_t nSimpleLock;//timeout 2000ms
    int nMeterAddr;
    char modbus_rep[64];
} str_LINKTASKINFO;
//linkkit task manager
typedef struct
{
    str_LINKTASKINFO m_LinkTask[MAX_LINK_TASK_NUM];
    uint16_t dTaskWriteP;
    uint16_t dTaskReadP;
}str_AllLINKTASKMGR;



void AddAwssReportReset(void);//clear cloud disbind user and device information
void AddOneCloudYk(int address,int val);
void AddOneCloudTransmit(uint8_t address, uint8_t func,uint16_t regAddr, uint16_t val);
void AddOneCloudPeriod(int address, int val);
void AddOneCloudClk(int address, int val);
void AddOneCloudLeakCheck(int address,int val);
void AddOneCloudLock(int address, int val);
void AddChangeHighTemprature(int address,int val);
void AddReadAlarmEvent(int address);
void AddChangeRatedCurrent(int address,int val);
void AddUpdateFirmware(int address,int val);
void AddEnableProtected(int address,int val);
void AddChangeCurrentLimits(int address, int val);
void AddChangePowerLimits(int address, int val);
// void AddChangeCurrentLimitA(int address,int val);
// void AddChangeCurrentLimitB(int address,int val);
// void AddChangeCurrentLimitC(int address,int val);
// void AddChangePowerLimitA(int address,int val);
// void AddChangePowerLimitB(int address,int val);
// void AddChangePowerLimitC(int address,int val);
void SetLock(int address, int val);
void AddChangeThresholdPara(int address, int val,uint16_t Type);
void AddExitModbusTask(void);
void AddAutoDispatch(void);
void InitLinkTask(void);

bool HaveNewLinkTask(void);
void AddOneLinkTaskRecord(str_LINKTASKINFO *task);
str_LINKTASKINFO * GetOneLinkTask(void);
int GetReadableLinkTaskLen(void);

int GetCurrentBkBum(void);
void SetCurrentBkBum(const int num);
//esp_err_t write_nvs_val(char *key,int *pVal);
//esp_err_t read_nvs_val(char *key,int *pVal);



//gateway attribute set/get
void setGwOnBeginTime(str_TIME *pTime);
void setGwOffBeginTime(str_TIME *pTime);
void setGwOnEndTime(str_TIME *pTime);
void setGwOffEndTime(str_TIME *pTime);
// void setGwOnFlag(uint8_t flag);
void setGwOffFlag(uint8_t flag);
void initBreakFaultPara(int address);

//break time control attrbute
void SetOneBkCtrlVal(int address,int onparaflag,uint8_t ucVal);
uint8_t  GetOneBkCtrlVal(int address);
//get addr
int GetOneBkaddr(uint32_t id);

#endif
