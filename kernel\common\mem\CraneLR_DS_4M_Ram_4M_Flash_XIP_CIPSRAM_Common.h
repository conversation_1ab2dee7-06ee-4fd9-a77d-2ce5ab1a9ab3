#ifndef __CONFIG_H__
#define __CONFIG_H__

#define QSPI_BASE_ADDR  (0x80017000)
#define DDR_RO_SIZE     (0x00600000)
#define PSRAM_BASE_ADDR (0x7E000000)
#define PSRAM_SIZE      (0x00400000)
#define PSRAM_END_MARK  (PSRAM_BASE_ADDR + PSRAM_SIZE)

//PSRAM_BLOCK_1
	//[REGN] DDR_DSP_RO
	//[DESC] DSP image load region
	//[SIZE] 1.5M
	#define MSA_BASE_ADDR   (PSRAM_BASE_ADDR)
	#if (defined(CRANELRH_SUPPORT))
	#define MSA_SIZE        (0x00040800)   //258K
	#else
	#if defined(REDUCE_SULOG_IRBUF_DSP)
		#define MSA_SIZE        (0x00040800)	//258K
		#else
		#define MSA_SIZE        (0x00052000)	//328K
	#endif
	#endif

	//[REGN] DDR_HEAP
	//[DESC] Os HEAP region
	//[SIZE] 1.2M
    #define DDR_HEAP_BASE_ADDR (MSA_BASE_ADDR + MSA_SIZE)
	#if (defined(CRANELRH_SUPPORT))
		#define DDR_HEAP_SIZE      (0x001D1800 - 0x40000) //256KB for OC
	#else
		#if defined(REDUCE_SULOG_IRBUF_DSP)
		#define DDR_HEAP_SIZE      (0x001D1800 - 0x40000) //256KB for OC
		#else
		#define DDR_HEAP_SIZE      (0x001C0000 - 0x40000) //256KB for OC
		#endif
	#endif

	#define CM_APP_REGION   DDR_HEAP_BASE_ADDR + DDR_HEAP_SIZE //opencpu app load addr
	#define CM_APP_SIZE     0x40000

    //[REGN] DDR_RW_DATA
	//[DESC] RW data
	//[SIZE] 1.2M
    #define DDR_RW_DATA_BASE_ADDR  (CM_APP_REGION + CM_APP_SIZE)
    #define DDR_RW_DATA_SIZE       (0x000F6000)

	//[REGN] PS_CODE_IN_PSRAM
	//[DESC] effective essential code running on PSRAM
	//[SIZE] 320K
	#define PS_CODE_IN_PSRAM_BASE  (DDR_RW_DATA_BASE_ADDR + DDR_RW_DATA_SIZE)
	#define PS_CODE_IN_PSRAM_SIZE  (0x00058000)//0x00048000





//PSRAM_BLOCK_2
	//[REGN] NONCACHE_REGION
	//[DESC] ALL NONCACHE regions
	//[SIZE] 1M
	#define DDR_NONCACHE_AREA_TOTAL_SIZE     (0x000A0000)
	#define DDR_NONCACHE_AREA_TOTAL_START    (PSRAM_BASE_ADDR + PSRAM_SIZE - DDR_NONCACHE_AREA_TOTAL_SIZE)
	#define DDR_NONCACHE_PS_UL_BASE_ADDR     (DDR_NONCACHE_AREA_TOTAL_START)
	#if defined(TPT_UL)
    #define DDR_NONCACHE_PS_UL_SIZE          (0x00014000 + 0x00010000) //80k + 64k
    #else
    #define DDR_NONCACHE_PS_UL_SIZE          (0x00014000) //80k
    #endif
	#define DDR_NONCACHE_PS_DL_BASE_ADDR     (DDR_NONCACHE_PS_UL_BASE_ADDR + DDR_NONCACHE_PS_UL_SIZE)
	#define DDR_NONCACHE_PS_DL_SIZE          (0x00014000) //(0x00014000)
	#define PS_NONCACHE_DATA_BASE_ADDR       (DDR_NONCACHE_PS_DL_BASE_ADDR + DDR_NONCACHE_PS_DL_SIZE)
	#define PS_NONCACHE_DATA_SIZE            (0x0004E000)
	#define DDR_NONCACHE_BASE_ADDR           (PS_NONCACHE_DATA_BASE_ADDR + PS_NONCACHE_DATA_SIZE)
	#define DDR_NONCACHE_SIZE                (0x00001000)
	#define D2_VECT_BASE_ADDR                (DDR_NONCACHE_BASE_ADDR + DDR_NONCACHE_SIZE)
	#define D2_VECT_SIZE                     (0x00000100)
	#define DDR_PRIVATE_RW_AREA_BASE_ADDR    (D2_VECT_BASE_ADDR + D2_VECT_SIZE)
	#define DDR_PRIVATE_RW_AREA_SIZE         (0x00001C00 - D2_VECT_SIZE)
	#define DDR_NONCACHE_PTABLE_BASE_ADDR    (DDR_PRIVATE_RW_AREA_BASE_ADDR + DDR_PRIVATE_RW_AREA_SIZE)
	#define DDR_NONCACHE_PTABLE_SIZE         (0x00001000)
	#define DDR_NONCACHE_PROP_BASE_ADDR      (DDR_NONCACHE_PTABLE_BASE_ADDR + DDR_NONCACHE_PTABLE_SIZE)
	#define DDR_NONCACHE_PROP_SIZE           (0x00001000)
	#define DDR_NONCACHE_USB_BASE_ADDR       (DDR_NONCACHE_PROP_BASE_ADDR + DDR_NONCACHE_PROP_SIZE)
	#define DDR_NONCACHE_USB_SIZE            (DDR_NONCACHE_AREA_TOTAL_SIZE - (DDR_NONCACHE_USB_BASE_ADDR - DDR_NONCACHE_AREA_TOTAL_START))


#define ARBEL_DDR_BASE_ADDR     QSPI_BASE_ADDR
#define DDR_RW_BASE_ADDR        PSRAM_BASE_ADDR
#define DDR_RO_INIT_CODE_SIZE   (0x00001200)
#define DTCM_BASE_ADDR          (0xB0020000)
#define DTCM_SIZE               (0x00010000)

#define SQU_USB_POOL_ADDR       (0xD1001000)
#define SQU_USB_POOL_SIZE       (0x00002000)
#define SQU_USB_DQH_ADDR        (0xD1004800)
#define SQU_USB_DQH_SIZE        (0x00000800)
#define SQU_LTEBMBLOCK_BUF_ADDR (0xD1007000)
#define SQU_LTEBMBLOCK_BUF_SIZE (0x00001000)

#endif
