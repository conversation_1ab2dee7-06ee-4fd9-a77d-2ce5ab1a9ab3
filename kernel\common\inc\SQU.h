/******************************************************************************
 *
 * Name:    SQU.h
 * Project: Marvell Registers
 * Purpose: Marvell Registers
 *
 ******************************************************************************/

/******************************************************************************
 *
 *  (C)Copyright 2005 - 2014 Marvell. All Rights Reserved.
 *  
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF MARVELL.
 *  The copyright notice above does not evidence any actual or intended 
 *  publication of such source code.
 *  This Module contains Proprietary Information of Marvell and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the 
 *  licensees of Marvell.
 *  Such users have the right to use, modify, and incorporate this code into 
 *  products for purposes authorized by the license agreement provided they 
 *  include this notice and the associated copyright notice with any such
 *  product. 
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

/******************************************************************************
 *
 * This file was automatically generated by reg.pl using  * SQU.csv
 *
 ******************************************************************************/

/******************************************************************************
 *
 * History:
 *
 ********* PLEASE INSERT THE CVS HISTORY OF THE PREVIOUS VERSION HERE. *********
 *******************************************************************************/

#ifndef __INC_SQU_H
#define __INC_SQU_H


 
typedef struct
{
    __IO uint32_t   CTRL_0;                         /* 0x0000 32 bit    SQU Control 0
                                             *          Register
                                             */
    __IO uint32_t   RESERVED0[1];
    __IO uint32_t   CTRL_1;                         /* 0x0008 32 bit    SQU Control 1
                                             *          Register
                                             */
    __IO uint32_t   RESERVED1[1];
    __IO uint32_t   FMBIST_CTRL_0;                  /* 0x0010 32 bit    SQU FMBIST
                                             *          Control 0 Register
                                             */
    __IO uint32_t   RESERVED2[1];
    __IO uint32_t   FMBIST_CTRL_1;                  /* 0x0018 32 bit    SQU FMBIST
                                             *          Control 1 Register
                                             */
    __IO uint32_t   RESERVED3[1];
    __IO uint32_t   FMBIST_STATUS_0;                /* 0x0020 32 bit    SQU FMBIST
                                             *          Status 0 Register
                                             */
    __IO uint32_t   RESERVED4[1];
    __IO uint32_t   RSVD;                           /* 0x0028 32 bit    Reserved Register */
    __IO uint32_t   RESERVED5[1];
    __IO uint32_t   CTRL_2;                         /* 0x0030 32 bit    SQU Control 2
                                             *          Register
                                             */
    __IO uint32_t   RESERVED6[1];
    __IO uint32_t   FMBIST_CTRL_2;                  /* 0x0038 32 bit    SQU fmbist wport
                                             *          contl2 Register
                                             */
    __IO uint32_t   RESERVED7[1];
    __IO uint32_t   PERF_COUNT_CNTRL;                   /* 0x0040 32 bit    Performance
                                             *          Counter Control
                                             *          Register
                                             */
    __IO uint32_t   RESERVED8[1];
    __IO uint32_t   PERF_COUNT_S1;                      /* 0x0048 32 bit    Performance
                                             *          Count S1 Registers
                                             */
    __IO uint32_t   RESERVED9[1];
    __IO uint32_t   PERF_COUNT_S4;                      /* 0x0050 32 bit    Performance
                                             *          Count S4 Registers
                                             */
    __IO uint32_t   RESERVED10[1];
    __IO uint32_t   PERF_COUNT_S8;                      /* 0x0058 32 bit    Performance
                                             *          Count S8 Registers
                                             */
    __IO uint32_t   RESERVED11[489];
    __IO uint32_t   CHAN_BYTE_CNT[2];                /* 0x0800 32 bit    Channel Byte
                                             *          Count Register
                                             */
    __IO uint32_t   RESERVED12[2];
    __IO uint32_t   CHAN_SRC_ADDR[2];                /* 0x0810 32 bit    Channel 0 Source
                                             *          Address Register
                                             */
    __IO uint32_t   RESERVED13[2];
    __IO uint32_t   CHAN_DEST_ADDR[2];               /* 0x0820 32 bit    Channel 0
                                             *          Destination Address
                                             *          Register
                                             */
    __IO uint32_t   RESERVED14[2];
    __IO uint32_t   CHAN_NEXT_DESC_PTR[2];           /* 0x0830 32 bit    Channel 0 Next
                                             *          Descriptor Pointer
                                             *          Register
                                             */
    __IO uint32_t   RESERVED15[2];
    __IO uint32_t   CHAN_CTRL[2];                    /* 0x0840 32 bit    Channel 0
                                             *          Control Register
                                             */
    __IO uint32_t   RESERVED16[6];
    __IO uint32_t   CHAN_PRI;                       /* 0x0860 32 bit    Channel Priority
                                             *          Register
                                             */
    __IO uint32_t   CHANNEL_ID_FILTER;                  /* 0x0864 32 bit    Channel ID FILTER */
    __IO uint32_t   RESERVED17[2];
    __IO uint32_t   CHAN_CURR_DESC_PTR[2];               /* 0x0870 32 bit    Channel 0
                                             *          Current Descriptor
                                             *          Pointer Register
                                             */
    __IO uint32_t   RESERVED21[2];
    __IO uint32_t   CHAN_INT_MASK[2];                /* 0x0880 32 bit    Channel 0
                                             *          Interrupt Mask
                                             *          Register
                                             */
    __IO uint32_t   RESERVED18[2];
    __IO uint32_t   CHAN_RSR[2];                         /* 0x0890 32 bit    Channel 0 Reset
                                             *          Select Register
                                             */
    __IO uint32_t   RESERVED20[2];
    __IO uint32_t   CHAN_INT_STATUS[2];              /* 0x08A0 32 bit    Channel 0
                                             *          Interrupt Status
                                             *          Register
                                             */
}SQU_TypeDef;


#define SQU_BASE    0xD42A0000
#define SQU ((SQU_TypeDef*)SQU_BASE)


/*
 *
 *  THE BIT DEFINES
 *
 */
/*  SQU_CTRL_0                  0x0000  SQU Control 0 Register */
#define SQU_CTRL_0_RTC_BK0_MSK              SHIFT30(0x3)    /* RTC BK 0 */
#define SQU_CTRL_0_RTC_BK0_BASE             30
#define SQU_CTRL_0_WTC_BK0_MSK              SHIFT28(0x3)    /* WTC BK 0 */
#define SQU_CTRL_0_WTC_BK0_BASE             28
#define SQU_CTRL_0_PDWN_BK0                 BIT_27          /* Power Down BK 0 */
/*      Bit(s) SQU_CTRL_0_RSRV_ reserved */
#define SQU_CTRL_0_CAMENABLE                BIT_24          /* Camera Enable */
#define SQU_CTRL_0_FIXPRIORITY              BIT_23          /* Fix Priority */
/*      Bit(s) SQU_CTRL_0_RSRV_ reserved */
#define SQU_CTRL_0_CAMOVE                   BIT_16          /* Camera Override */
#define SQU_CTRL_0_RTC_BROM_MSK             SHIFT14(0x3)    /* RTC BROM */
#define SQU_CTRL_0_RTC_BROM_BASE            14
#define SQU_CTRL_0_RTC_REF_BROM_MSK         SHIFT12(0x3)    /* RTC REF BROM */
#define SQU_CTRL_0_RTC_REF_BROM_BASE        12
#define SQU_CTRL_0_PDWN_BROM                BIT_11          /* Power Down Brom */
/*      Bit(s) SQU_CTRL_0_RSRV_ reserved */
#define SQU_CTRL_0_APB_CLK_DIV_MSK          SHIFT2(0x7)     /* APB_CLK_DIV */
#define SQU_CTRL_0_APB_CLK_DIV_BASE         2
#define SQU_CTRL_0_ROM_WAIT_CYCLE_MSK       SHIFT0(0x3)     /* ROM Wait Cycle */
#define SQU_CTRL_0_ROM_WAIT_CYCLE_BASE      0

/*  SQU_CTRL_1                  0x0008  SQU Control 1 Register */
/*      Bit(s) SQU_CTRL_1_RSRV_ reserved */
#define SQU_CTRL_1_RTC_BK1_MSK          SHIFT14(0x3)    /* RTC BK 1 */
#define SQU_CTRL_1_RTC_BK1_BASE         14
#define SQU_CTRL_1_WTC_BK1_MSK          SHIFT12(0x3)    /* WTC BK 1 */
#define SQU_CTRL_1_WTC_BK1_BASE         12
#define SQU_CTRL_1_PDWN_BK1             BIT_11          /* Power Down BK 1 */
#define SQU_CTRL_1_CLKEN_BK1            BIT_10          /* Clock Enable BK 1 */
#define SQU_CTRL_1_FASTACCESS BK1       BIT_9           /* SW_fast_BK_1 */
#define SQU_CTRL_1_CAMENABLE1           BIT_8           /* Camera Enable 1 */
#define SQU_CTRL_1_FIXPRIORITY1         BIT_7           /* Fix Priority 1 */
/*      Bit(s) SQU_CTRL_1_RSRV_ reserved */

/*  SQU_FMBIST_CTRL_0           0x0010  SQU FMBIST Control 0 Register */
/* SQU FMBIST Software Cam Access Enable */
#define SQU_FMBIST_CTRL_0_SQU_FMBIST_SW_CAM_ACC_EN      BIT_31          
/*      Bit(s) SQU_FMBIST_CTRL_0_RSRV_ reserved */
/* OTP_SQU_MUX_SEL */
#define SQU_FMBIST_CTRL_0_OTP_SQU_MUX_SEL_MSK           SHIFT28(0x3)    
#define SQU_FMBIST_CTRL_0_OTP_SQU_MUX_SEL_BASE          28
/* FUSEW Clear */
#define SQU_FMBIST_CTRL_0_FUSEW_CLEAR                   BIT_27          
/* Clock Divider Value */
#define SQU_FMBIST_CTRL_0_CLK_DIV_VAL_MSK               SHIFT24(0x7)    
#define SQU_FMBIST_CTRL_0_CLK_DIV_VAL_BASE              24
/*      Bit(s) SQU_FMBIST_CTRL_0_RSRV_ reserved */
/* Read Fuse 5 */
#define SQU_FMBIST_CTRL_0_READ_FUSE5                    BIT_21          
/* Read Fuse 4 */
#define SQU_FMBIST_CTRL_0_READ_FUSE4                    BIT_20          
/* Read Fuse 3 */
#define SQU_FMBIST_CTRL_0_READ_FUSE3                    BIT_19          
/* Read Fuse 2 */
#define SQU_FMBIST_CTRL_0_READ_FUSE2                    BIT_18          
/* Read Fuse 1 */
#define SQU_FMBIST_CTRL_0_READ_FUSE1                    BIT_17          
/* Read Fuse 0 */
#define SQU_FMBIST_CTRL_0_READ_FUSE0                    BIT_16          
/*      Bit(s) SQU_FMBIST_CTRL_0_RSRV_ reserved */

/*  SQU_FMBIST_CTRL_1           0x0018  SQU FMBIST Control 1 Register */
/* SQU FMBIST Software Control */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_SW_CONTROL         BIT_31          
/* FUSEW Start */
#define SQU_FMBIST_CTRL_1_FUSEW_START                   BIT_30          
/* ATE SQU Mode */
#define SQU_FMBIST_CTRL_1_ATE_SQU_MODE                  BIT_29          
/* Pipe Mode */
#define SQU_FMBIST_CTRL_1_PIPE_MODE                     BIT_28          
/* Ack High Power */
#define SQU_FMBIST_CTRL_1_ACK_HI_PWR                    BIT_27          
/* Ack Low Power */
#define SQU_FMBIST_CTRL_1_ACK_LO_PWR                    BIT_26          
/*      Bit(s) SQU_FMBIST_CTRL_1_RSRV_ reserved */
/* SQU FMBIST Register PMU Enable */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_REG_PMU_EN         BIT_22          
/* SQU FMBIST Retention */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_RETENTION          BIT_21          
/* SQU FMBIST 14n */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_14N                BIT_20          
/* SQU FMBIST Force Error */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_FORCE_ERROR        BIT_19          
/* SQU FMBIST Clear Logger */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_CLEAR_LOGGER       BIT_18          
/* SQU FMBIST Clear */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_CLEAR              BIT_17          
/* SQU FMBIST Start */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_START              BIT_16          
/*      Bit(s) SQU_FMBIST_CTRL_1_RSRV_ reserved */
/* Memtop M1 in Bits */
#define SQU_FMBIST_CTRL_1_MEMTOP_M1_IN_BITS_MSK         SHIFT12(0x3)    
#define SQU_FMBIST_CTRL_1_MEMTOP_M1_IN_BITS_BASE        12
/*      Bit(s) SQU_FMBIST_CTRL_1_RSRV_ reserved */
/* Col Size In */
#define SQU_FMBIST_CTRL_1_COL_SIZE_IN_MSK               SHIFT8(0x7)     
#define SQU_FMBIST_CTRL_1_COL_SIZE_IN_BASE              8
/*      Bit(s) SQU_FMBIST_CTRL_1_RSRV_ reserved */
/* SQU FMBIST Block Enable */
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_BLOCK_EN_MSK       SHIFT0(0x3f)    
#define SQU_FMBIST_CTRL_1_SQU_FMBIST_BLOCK_EN_BASE      0

/*  SQU_FMBIST_STATUS_0         0x0020  SQU FMBIST Status 0 Register */
/*      Bit(s) SQU_FMBIST_STATUS_0_RSRV_ reserved */
/* Fuse Ready */
#define SQU_FMBIST_STATUS_0_FUSE_READY                  BIT_28          
/* SQU FMBIST Register High Power */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_REG_HI_PWR       BIT_27          
/* SQU FMBIST Register Low Power */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_REG_LO_PWR       BIT_26          
/* FuseW Done */
#define SQU_FMBIST_STATUS_0_FUSEW_DONE                  BIT_25          
/* Burn Error */
#define SQU_FMBIST_STATUS_0_BURN_ERROR                  BIT_24          
/*      Bit(s) SQU_FMBIST_STATUS_0_RSRV_ reserved */
/* Cam MBIST Fail[5] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL5             BIT_19          
/* Cam MBIST Done[5] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE5             BIT_18          
/* Cam MBIST Fail[4] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL4             BIT_17          
/* Cam MBIST Done[4] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE4             BIT_16          
/* Cam MBIST Fail[3] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL3             BIT_15          
/* Cam MBIST Done[3] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE3             BIT_14          
/* Cam MBIST Fail[2] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL2             BIT_13          
/* Cam MBIST Done[2] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE2             BIT_12          
/* Cam MBIST Fail[1] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL1             BIT_11          
/* Cam MBIST Done[1] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE1             BIT_10          
/* Cam MBIST Fail[0] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_FAIL0             BIT_9           
/* Cam MBIST Done[0] */
#define SQU_FMBIST_STATUS_0_CAM_MBIST_DONE0             BIT_8           
/* SQU FMBIST Pointer */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_PTR_MSK          SHIFT4(0xf)     
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_PTR_BASE         4
/*      Bit(s) SQU_FMBIST_STATUS_0_RSRV_ reserved */
/* SWU FMBIST Ready */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_READY            BIT_2           
/* SQU FMBIST Logger Error */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_LOGGER_ERROR     BIT_1           
/* SQU FMBIST Logger Fatal */
#define SQU_FMBIST_STATUS_0_SQU_FMBIST_LOGGER_FATAL     BIT_0           

/*  SQU_RSVD                    0x0028  Reserved Register */
/*      Bit(s) SQU_RSVD_RSRV_ reserved */

/*  SQU_CTRL_2                  0x0030  SQU Control 2 Register */
#define SQU_CTRL_2_RTC_BK4_MSK      SHIFT30(0x3)    /* RTC BK 4 */
#define SQU_CTRL_2_RTC_BK4_BASE     30
#define SQU_CTRL_2_WTC_BK4_MSK      SHIFT28(0x3)    /* WTC BK 4 */
#define SQU_CTRL_2_WTC_BK4_BASE     28
#define SQU_CTRL_2_PDWN_BK4         BIT_27          /* Power Down BK 4 */
#define SQU_CTRL_2_CLKEN_BK4        BIT_26          /* Clock Enable BK 4 */
#define SQU_CTRL_2_FASTACCESS1      BIT_25          /* Fast Access 1 */
#define SQU_CTRL_2_CAMENABLE1       BIT_24          /* Camera Enable 1 */
#define SQU_CTRL_2_FIXPRIORITY1     BIT_23          /* Fix Priority 1 */
/*      Bit(s) SQU_CTRL_2_RSRV_ reserved */
#define SQU_CTRL_2_EN_BK4           BIT_16          /* EN BK 4 */
#define SQU_CTRL_2_RTC_BK3_MSK      SHIFT14(0x3)    /* RTC BK 4 */
#define SQU_CTRL_2_RTC_BK3_BASE     14
#define SQU_CTRL_2_WTC_BK3_MSK      SHIFT12(0x3)    /* WTC BK 3 */
#define SQU_CTRL_2_WTC_BK3_BASE     12
#define SQU_CTRL_2_PDWN_BK3         BIT_11          /* Power Down BK 3 */
#define SQU_CTRL_2_CLKEN_BK3        BIT_10          /* Clock Enable BK 3 */
#define SQU_CTRL_2_FASTACCESS2      BIT_9           /* Fast Access 2 */
#define SQU_CTRL_2_CAMENABLE2       BIT_8           /* Camera Enable 2 */
#define SQU_CTRL_2_FIXPRIORITY2     BIT_7           /* Fix Priority 2 */
/*      Bit(s) SQU_CTRL_2_RSRV_ reserved */
#define SQU_CTRL_2_EN_BK3           BIT_0           /* EN BK 3 */

/*  SQU_FMBIST_CTRL_2           0x0038  SQU fmbist wport contl2 Register */
/*      Bit(s) SQU_FMBIST_CTRL_2_RSRV_ reserved */
/* power down */
#define SQU_FMBIST_CTRL_2_PWR DONE_MSK                  SHIFT24(0x1f)   
#define SQU_FMBIST_CTRL_2_PWRDONE_BASE                  24
/* debug_sel_databyte */
#define SQU_FMBIST_CTRL_2_DEBUG_SEL_DATABYTE_MSK        SHIFT8(0x7)     
#define SQU_FMBIST_CTRL_2_DEBUG_SEL_DATABYTE_BASE       8
/* debug_selb */
#define SQU_FMBIST_CTRL_2_DEBUG_SELB_MSK                SHIFT0(0xff)    
#define SQU_FMBIST_CTRL_2_DEBUG_SELB_BASE               0

/*  PERF_COUNT_CNTRL            0x0040  Performance Counter Control Register */
/*      Bit(s) PERF_COUNT_CNTRL_RSRV_ reserved */
/* Read Byte Count S8 */
#define PERF_COUNT_CNTRL_S8_RD_BYTE_COUNT_EN        BIT_10              
/* Write Byte Count S8 */
#define PERF_COUNT_CNTRL_S8_WR_BYTE_COUNT_EN        BIT_9               
/* Clear Counter S8 */
#define PERF_COUNT_CNTRL_CLR_COUNT_S8               BIT_8               
/*      Bit(s) PERF_COUNT_CNTRL_RSRV_ reserved */
/* Read Byte Count S4 */
#define PERF_COUNT_CNTRL_S4_RD_BYTE_COUNT_EN        BIT_6               
/* Write Byte Count S4 */
#define PERF_COUNT_CNTRL_S4_WR_BYTE_COUNT_EN        BIT_5               
/* Clear Counter S4 */
#define PERF_COUNT_CNTRL_CLR_COUNT_S4               BIT_4               
/*      Bit(s) PERF_COUNT_CNTRL_RSRV_ reserved */
/* Read Byte Count S1 */
#define PERF_COUNT_CNTRL_S1_RD_BYTE_COUNT_EN        BIT_2               
/* Write Byte Count S1 */
#define PERF_COUNT_CNTRL_S1_WR_BYTE_COUNT_EN        BIT_1               
/* Clear Counter S1 */
#define PERF_COUNT_CNTRL_CLR_COUNT_S1               BIT_0               

/*  PERF_COUNT_S1               0x0048  Performance Count S1 Registers */
#define PERF_COUNT_S1_COUNT_VALUE_MSK       SHIFT0(0xffffffff)  /* Count Value */
#define PERF_COUNT_S1_COUNT_VALUE_BASE      0

/*  PERF_COUNT_S4               0x0050  Performance Count S4 Registers */
#define PERF_COUNT_S4_COUNT_VALUE_MSK       SHIFT0(0xffffffff)  /* Count Value */
#define PERF_COUNT_S4_COUNT_VALUE_BASE      0

/*  PERF_COUNT_S8               0x0058  Performance Count S8 Registers */
#define PERF_COUNT_S8_COUNT_VALUE_MSK       SHIFT0(0xffffffff)  /* Count Value */
#define PERF_COUNT_S8_COUNT_VALUE_BASE      0

/*  SQU_CHAN_0_BYTE_CNT         0x0800  Channel 0 Byte Count Register */
/*      Bit(s) SQU_CHAN_0_BYTE_CNT_RSRV_ reserved */
/* Channel 0 Byte Count */
#define SQU_CHAN_0_BYTE_CNT_BYTECNT0_MSK        SHIFT0(0xffff)  
#define SQU_CHAN_0_BYTE_CNT_BYTECNT0_BASE       0

/*  SQU_CHAN_1_BYTE_CNT         0x0804  Channel 1 Byte Count Register */
/*      Bit(s) SQU_CHAN_1_BYTE_CNT_RSRV_ reserved */
/* Channel 1 Byte Count */
#define SQU_CHAN_1_BYTE_CNT_BYTECNT1_MSK        SHIFT0(0xffff)  
#define SQU_CHAN_1_BYTE_CNT_BYTECNT1_BASE       0

/*  SQU_CHAN_0_SRC_ADDR         0x0810  Channel 0 Source Address Register */
/* Channel 0 Source Address */
#define SQU_CHAN_0_SRC_ADDR_SRCADD0_MSK         SHIFT0(0xffffffff)  
#define SQU_CHAN_0_SRC_ADDR_SRCADD0_BASE        0

/*  SQU_CHAN_1_SRC_ADDR         0x0814  Channel 1 Source Address Register */
/* Channel 1 Source Address */
#define SQU_CHAN_1_SRC_ADDR_SRCADD1_MSK         SHIFT0(0xffffffff)  
#define SQU_CHAN_1_SRC_ADDR_SRCADD1_BASE        0

/*  SQU_CHAN_0_DEST_ADDR        0x0820  Channel 0 Destination Address Register */
/* Channel 0 Destination Address */
#define SQU_CHAN_0_DEST_ADDR_DESTADD0_MSK       SHIFT0(0xffffffff)  
#define SQU_CHAN_0_DEST_ADDR_DESTADD0_BASE      0

/*  SQU_CHAN_1_DEST_ADDR        0x0824  Channel 1 Destination Address Register */
/* Channel 1 Destination Address */
#define SQU_CHAN_1_DEST_ADDR_DESTADD1_MSK       SHIFT0(0xffffffff)  
#define SQU_CHAN_1_DEST_ADDR_DESTADD1_BASE      0

/*  SQU_CHAN_0_NEXT_DESC_PTR    0x0830  Channel 0 Next Descriptor Pointer
 *                                      Register
 */
/* Channel 0 Next Descriptor Pointer Address */
#define SQU_CHAN_0_NEXT_DESC_PTR_NDPTR0_MSK         SHIFT0(0xffffffff)  
#define SQU_CHAN_0_NEXT_DESC_PTR_NDPTR0_BASE        0

/*  SQU_CHAN_1_NEXT_DESC_PTR    0x0834  Channel 1 Next Descriptor Pointer
 *                                      Register
 */
/* Channel 1 Next Descriptor Pointer Address */
#define SQU_CHAN_1_NEXT_DESC_PTR_NDPTR1_MSK         SHIFT0(0xffffffff)  
#define SQU_CHAN_1_NEXT_DESC_PTR_NDPTR1_BASE        0

/*  SQU_CHAN_0_CTRL             0x0840  Channel 0 Control Register */
/*      Bit(s) SQU_CHAN_0_CTRL_RSRV_ reserved */
#define SQU_CHAN_0_CTRL_SSPMOD              BIT_21          /* SSPMod */
#define SQU_CHAN_0_CTRL_ABR                 BIT_20          /* Channel Abort */
/*      Bit(s) SQU_CHAN_0_CTRL_RSRV_ reserved */
/* Close Descriptor Enable */
#define SQU_CHAN_0_CTRL_CDE                 BIT_17          
/*      Bit(s) SQU_CHAN_0_CTRL_RSRV_ reserved */
/* Source/Destination Address Alignment */
#define SQU_CHAN_0_CTRL_SDA                 BIT_15          
#define SQU_CHAN_0_CTRL_CHANACT             BIT_14          /* DMA Channel Active */
/* Fetch Next Descriptor */
#define SQU_CHAN_0_CTRL_FETCHND             BIT_13          
#define SQU_CHAN_0_CTRL_CHANEN              BIT_12          /* Channel Enable */
#define SQU_CHAN_0_CTRL_TRANSMOD            BIT_11          /* TransMod */
#define SQU_CHAN_0_CTRL_INTMODE             BIT_10          /* Interrupt Mode */
#define SQU_CHAN_0_CTRL_CHAINMOD            BIT_9           /* Chain Mode */
/* Burst Limit in each DMA Access */
#define SQU_CHAN_0_CTRL_BURSTLIMIT_MSK      SHIFT6(0x7)     
#define SQU_CHAN_0_CTRL_BURSTLIMIT_BASE     6
/* Destination Direction */
#define SQU_CHAN_0_CTRL_DESTDIR_MSK         SHIFT4(0x3)     
#define SQU_CHAN_0_CTRL_DESTDIR_BASE        4
#define SQU_CHAN_0_CTRL_SRCDIR_MSK          SHIFT2(0x3)     /* Source Direction */
#define SQU_CHAN_0_CTRL_SRCDIR_BASE         2
/*      Bit(s) SQU_CHAN_0_CTRL_RSRV_ reserved */

/*  SQU_CHAN_1_CTRL             0x0844  Channel 1 Control Register */
/*      Bit(s) SQU_CHAN_1_CTRL_RSRV_ reserved */
#define SQU_CHAN_1_CTRL_SSPMOD              BIT_21          /* SSPMod */
#define SQU_CHAN_1_CTRL_ABR                 BIT_20          /* Channel Abort */
/*      Bit(s) SQU_CHAN_1_CTRL_RSRV_ reserved */
/* Close Descriptor Enable */
#define SQU_CHAN_1_CTRL_CDE                 BIT_17          
/*      Bit(s) SQU_CHAN_1_CTRL_RSRV_ reserved */
#define SQU_CHAN_1_CTRL_CHANACT             BIT_14          /* DMA Channel Active */
/* Fetch Next Descriptor */
#define SQU_CHAN_1_CTRL_FETCHND             BIT_13          
#define SQU_CHAN_1_CTRL_CHANEN              BIT_12          /* Channel Enable */
#define SQU_CHAN_1_CTRL_TRANSMOD            BIT_11          /* TransMod */
#define SQU_CHAN_1_CTRL_INTMODE             BIT_10          /* Interrupt Mode */
#define SQU_CHAN_1_CTRL_CHAINMOD            BIT_9           /* Chain Mode */
/* Burst Limit in each DMA Access */
#define SQU_CHAN_1_CTRL_BURSTLIMIT_MSK      SHIFT6(0x7)     
#define SQU_CHAN_1_CTRL_BURSTLIMIT_BASE     6
/* Destination Direction */
#define SQU_CHAN_1_CTRL_DESTDIR_MSK         SHIFT4(0x3)     
#define SQU_CHAN_1_CTRL_DESTDIR_BASE        4
#define SQU_CHAN_1_CTRL_SRCDIR_MSK          SHIFT2(0x3)     /* Source Direction */
#define SQU_CHAN_1_CTRL_SRCDIR_BASE         2
/*      Bit(s) SQU_CHAN_1_CTRL_RSRV_ reserved */

/*  SQU_CHAN_PRI                0x0860  Channel Priority Register */
/* Reverse memory copy */
#define SQU_CHAN_PRI_REVERSE_MEMORY_COPY        BIT_31              
/*      Bit(s) SQU_CHAN_PRI_RSRV_ reserved */
/* Channels 0 and 1 Priority */
#define SQU_CHAN_PRI_PRIOCHAN10_MSK             SHIFT0(0x3)         
#define SQU_CHAN_PRI_PRIOCHAN10_BASE            0

/*  Channel_ID_FILTER           0x0864  Channel ID FILTER */
/*      Bit(s) CHANNEL_ID_FILTER_RSRV_ reserved */
/* Enable Access */
#define CHANNEL_ID_FILTER_EN_ACCESS_MSK         SHIFT0(0xff)        
#define CHANNEL_ID_FILTER_EN_ACCESS_BASE        0

/*  CHAN_0_CURR_DESC_PTR        0x0870  Channel 0 Current Descriptor Pointer
 *                                      Register
 */
/* Channel 0 Current Descriptor Pointer Address */
#define CHAN_0_CURR_DESC_PTR_CDPTR0_MSK         SHIFT0(0xffffffff)  
#define CHAN_0_CURR_DESC_PTR_CDPTR0_BASE        0

/*  CHAN_1_CURR_DESC_PTR        0x0874  Channel 1 Current Descriptor Pointer
 *                                      Register
 */
/* Channel 1 Current Descriptor Pointer Address */
#define CHAN_1_CURR_DESC_PTR_CDPTR1_MSK         SHIFT0(0xffffffff)  
#define CHAN_1_CURR_DESC_PTR_CDPTR1_BASE        0

/*  CHAN_0_RSR                  0x0890  Channel 0 Reset Select Register */
/*      Bit(s) CHAN_0_RSR_RSRV_ reserved */
/* Channel 0 reset select Address */
#define CHAN_0_RSR_RSR0_MSK         SHIFT0(0x3)         
#define CHAN_0_RSR_RSR0_BASE        0

/*  SQU_CHAN_1_RSR              0x0894  Channel 1 Reset Select Register */
/*      Bit(s) SQU_CHAN_1_RSR_RSRV_ reserved */
/* Channel 1 reset select Address */
#define SQU_CHAN_1_RSR_RSR1_MSK         SHIFT0(0x3)         
#define SQU_CHAN_1_RSR_RSR1_BASE        0

/*  SQU_CHAN_0_INT_MASK         0x0880  Channel 0 Interrupt Mask Register */
/*      Bit(s) SQU_CHAN_0_INT_MASK_RSRV_ reserved */
/* Channel 0 DMA Aborted Interrupt Mask */
#define SQU_CHAN_0_INT_MASK_DMA_ABORT_INT       BIT_1               
/* Channel 0 Interrupt Done Mask */
#define SQU_CHAN_0_INT_MASK_COMP                BIT_0               

/*  SQU_CHAN_1_INT_MASK         0x0884  Channel 1 Interrupt Mask Register */
/*      Bit(s) SQU_CHAN_1_INT_MASK_RSRV_ reserved */
/* Channel 1 DMA Aborted Interrupt Mask */
#define SQU_CHAN_1_INT_MASK_DMA_ABORT_INT       BIT_1               
/* Channel 1 Interrupt Done MasK */
#define SQU_CHAN_1_INT_MASK_COMP                BIT_0               

/*  SQU_CHAN_0_INT_STATUS       0x08A0  Channel 0 Interrupt Status Register */
/*      Bit(s) SQU_CHAN_0_INT_STATUS_RSRV_ reserved */
/* DMA Abort Interrupt Status and Clear Channel 0 */
#define SQU_CHAN_0_INT_STATUS_DMA_ABORT         BIT_1               
/* Interrupt Done Channel 0 */
#define SQU_CHAN_0_INT_STATUS_INT_DONE          BIT_0               

/*  SQU_CHAN_1_INT_STATUS       0x08A4  Channel 1 Interrupt Status Register */
/*      Bit(s) SQU_CHAN_1_INT_STATUS_RSRV_ reserved */
/* DMA Abort Interrupt Status and Clear Channel 1 */
#define SQU_CHAN_1_INT_STATUS_DMA_ABORT         BIT_1               
/* Interrupt Done Channel 1 */
#define SQU_CHAN_1_INT_STATUS_INT_DONE          BIT_0               



/* -------------------- */


#endif  /* __INC_SQU_H */
