#ifndef MEMORY_IM20_H
#define MEMORY_IM20_H

#if defined __cplusplus
extern "C" {
#endif

/*****************************************************************************/
/***        头文件  Include files                                          ***/
/*****************************************************************************/
#include <stdint.h>
#include <stdbool.h>
/*****************************************************************************/
/***        宏定义  Macro Definitions                                      ***/
/*****************************************************************************/
#define MAX_IM20_NUM 1

//变化上送的限值
#define DEAD_BAND_IM20_HZ 5	 
#define DEAD_BAND_IM20_U 10

/*****************************************************************************/
/***        数据类型定义  Type Definitions                                 ***/
/*****************************************************************************/
// modbus 业务任务类型
typedef enum MODBUS_POLL_TASK
{
	E_TASK_NULL = -1,
	E_FIND_IM20_SLAVE,
	E_READ_IM20_DATA_BASIC,
	E_READ_IM20_VOLTAGE_STATE,
	E_CHECK_MODBUS_RUNFLAG
} ENUM_MODBUS_POLL_TASK;

/* 结构体顺序必须严格按照点表, 未知情况禁止修改增加，插入，颠倒变量顺序 
*/

// 基本实时电参量
typedef struct IM20_DATA_BASIC
{                      // 寄存器地址      描述			    数据类型
    float Hz;          // 0x4000~4001 	频率 F 	             float
    float PhV_phsV1;   // 0x4002~4003   相电压 V1			 float
    float PhV_phsV2;   // 0x4004~4005   相电压 V2 			 float
    float PhV_phsV3;   // 0x4006~4007   相电压 V3            float
    float VLNavg;      // 0x4008~4009   相电压均值 VLNavg	 float
    float LinV_phsV12; // 0x400A~400B   线电压 V12 			 float
    float LinV_phsV23; // 0x400C~400D   线电压 V23 			 float
    float LinV_phsV31; // 0x400E~400F   线电压 V31			 float
    float VLin_avg;    // 0x4010~4011   线电压均值 VLin_avg  float
} STRUCT_IM20_DATA_BASIC;

// 采样电压状态，大于50V为1，小于50V为0
typedef struct IM20_VOLTAGE_STATE
{								 // 寄存器地址      描述		数据类型
	uint16_t Voltage_state;		// 0x3FFC		  电压状态      word        
}STRUCT_IM20_VOLTAGE_STATE;
/***************************/
// IM20采集模块数据集合
typedef struct IM20
{
	struct IM20_DATA_BASIC basic_electrical_rtdata;
	struct IM20_DATA_BASIC basic_electrical_rtdata_cloud;
	struct IM20_VOLTAGE_STATE voltage_rtdata;
	struct IM20_VOLTAGE_STATE voltage_rtdata_cloud;
	// modbus para
	ENUM_MODBUS_POLL_TASK modbus_task_status;
	int modbus_failedCount; // modbus communicating failed count,not used yet
	int modbus_runFlag;		// modbus failed,状态在线(1)还是离线(0)
	int iCloud_runFlag;		// iCloud failed,云端在线(1)还是离线(0)
	uint8_t modbusAddr;
	uint32_t devId;         
	uint8_t deviceType;     //11:电容控制器 12:im20

} STRUCT_IM20;

// 多个im20数据集合内存
typedef struct IM20_MEMORY
{
	struct IM20 im20List[MAX_IM20_NUM];
	int im20_current_num;
} STRUCT_IM20_MEMORY;

/*****************************************************************************/
/***        外部变量声明  Exported Variables                               ***/
/*****************************************************************************/

/*****************************************************************************/
/***        外部函数声明  Exported Functions                               ***/
/*****************************************************************************/
STRUCT_IM20_MEMORY*  GetIM20_MEMORY(void);
// 初始化内存
void InitIm20Memory(void);

// 获取IM20列表指针首地址,方便获取参数
STRUCT_IM20* GetIm20List(void);
// 获取当前IM20的数量
int GetCurrentIm20Num(void);
// 设置当前IM20的数量
void SetCurrentIm20Num(int num);

// 提示：获取具体参数和实时值通过结构体地址来读取
// 获取IM20基本实时电参量等数据，index是im20List的下标索引
STRUCT_IM20_DATA_BASIC *GetOneIm20BasicElectricalRtdata(int index);
STRUCT_IM20_DATA_BASIC *GetOneIm20BasicElectricalRtdataCloud(int index);
// 设置IM20基本实时电参量等数据
void SetOneIm20BasicElectricalRtdata(int index, STRUCT_IM20_DATA_BASIC *pBasicElectricalRtdata);
// 显示基本实时电参量等数据
void ShowIm20BasicElectricalRtdata(int index);

// 获取IM20电压状态结构体指针
STRUCT_IM20_VOLTAGE_STATE *GetOneIm20VoltageStateRtdata(int index);
STRUCT_IM20_VOLTAGE_STATE *GetOneIm20VoltageStateRtdataCloud(int index);
// 设置IM20电压状态
void SetOneIm20VoltageStateRtdata(int index, STRUCT_IM20_VOLTAGE_STATE *pVoltageStateRtdata);
// 显示IM20电压状态
void ShowIm20VoltageStateRtdata(int index);


// 获取modbus任务状态
ENUM_MODBUS_POLL_TASK GetOneIm20ModbusTaskStatus(int index);
// 设置IM20modbus任务状态
void SetOneIM20ModbusTaskStatus(int index,ENUM_MODBUS_POLL_TASK modbusPollTask);

// 获取modbus通讯错误次数
int GetOneIm20ModbusFailedCount(int index);
// 设置modbus通讯错误次数
void SetOneIm20ModbusFailedCount(int index, int failedCount);


// 获取modbus通讯正常标志位（在线 离线）
int GetOneIm20ModbusRunFlag(int index);
// 设置modbus通讯正常标志位（在线 离线）
void SetOneIm20ModbusRunFlag(int index,int runFlag);

// 获取modbus通讯正常云端标志位（在线 离线）
int GetOneIm20CloudRunFlag(int index);
// 设置modbus通讯正常云端标志位（在线 离线）
void SetOneIm20CloudRunFlag(int index, int CloudRunFlag);

// 获取IM20modbus地址
uint8_t GetOneSlaveModbusAddr(int index);
// 设置IM20modbus地址
void SetOneSlaveModbusAddr(int index, uint8_t slave);

// 获取IM20的ID
uint32_t GetOneSlaveDevId(int index);
// 设置IM20的ID
void SetOneSlaveDevId(int index, uint32_t devId);


// 变化判断
bool ChangedDataIm20V1(int index);
bool ChangedDataIm20V2(int index);
bool ChangedDataIm20V3(int index);
bool ChangedDataIm20VLNavg(int index);
bool ChangedDataIm20V12(int index);
bool ChangedDataIm20V23(int index);
bool ChangedDataIm20V31(int index);
bool ChangedDataIm20VLINavg(int index);
bool ChangedDataIm20VoltageState(int index);

#if defined __cplusplus
}
#endif

#endif /* MEMORY_IM20_H */
/*****************************************************************************/
/***        END OF FILE                                                    ***/
/*****************************************************************************/
