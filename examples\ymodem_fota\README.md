# Ymodem协议传输升级包实现FOTA升级测试 简介 

## Ymodem协议和FOTA升级介绍**
Ymodem 是一种经典的文件传输协议，由 Chuck Forsberg 基于 Xmodem 改进而来，广泛应用于嵌入式系统的固件升级和文件传输。相较于 Xmodem，Ymodem 支持更大的数据块（1024字节）以提高传输效率，同时也保留了标准的128字节数据包选项。它引入了批量文件传输功能，并在传输过程中包含额外的元数据信息，如文件名和时间戳。Ymodem 采用更强大的循环冗余校验（CRC）机制来确保数据完整性，同时保持与旧版本校验和方法的兼容性。这些特性使得 Ymodem 在需要可靠串行数据交换的应用场景中尤为适用

FOTA（Firmware Over-The-Air）升级，即固件空中升级，是一种通过云端升级技术为具有联网功能的设备提供固件升级服务的方式。

更多固件升级及升级包制作方法请参考手册《ML307R_固件升级开发指导手册》

## **实现功能**
1. 第三方通过Ymodem协议发送APP升级包, 模组通过串口接收升级包
2. 成功接收升级包后，自动触发模组APP整包升级流程

## **APP执行流程**
1. 进入fota测试线程，进行串口配置初始化，并打开串口以准备接收文件
2. 用户可使用如SecureCRT等上位机软件与模组串口进行交互，使用ymodem协议发起APP升级包文件传输
3. ota升级环境初始化，确保模组处于正确的状态以接收固件升级包
4. 模组串口接收来自上位机的APP升级包，并在传输过程中检查每个数据包的CRC以确保数据完整性，不正确的包请求重传
5. 升级包接收完成，模组将自动重启进行APP整包升级

## **使用说明**
- 支持的模组（子）型号：ML307R-DC/ML307C-DC-CN
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 无

## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 10:26
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/12/11 17:30
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------