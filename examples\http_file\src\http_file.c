/**
 * @file        http.c
 * @brief       http示例，该示例仅供参考
 * @copyright   Copyright © 2024 China Mobile IOT. All rights reserved.
 * <AUTHOR> ya<PERSON><PERSON><PERSON>
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "stdarg.h"
#include "cm_mem.h"
#include "cm_sys.h"
#include "cm_os.h"
#include "cm_pm.h"
#include "cm_modem.h"
#include "cm_http.h"

#define HTTP_FILE_TEST_SERVER ""	//HTTP 文件测试的服务器地址和端口，如“http://***********:80”

/* 文件上传参数 */
#define HTTP_FILE_TEST_POST_URL ""										   // HTTP POST上传链接，如“/post”，若不知晓post地址，可通过电脑上传文件到服务器时用wireshark抓包后查看
#define HTTP_FILE_NAME "http_file_test.amr"								   // HTTP上传文件后在服务器上的文件名
#define HTTP_FILE_POST_HEADER_CONTENT_TYPE "multipart/form-data"		   // HTTP POST报头配置，content-type,常见有multipart/form-data、application/json、text/plain等 需要与服务器端确认配置何种报头
#define HTTP_FILE_POST_HEADER_BOUNDARY "WebkitFomBoundaryRZ35208y0yZuRkAD" // HTTP POST报头配置，分割符

/* 文件下载参数 */
#define HTTP_FILE_TEST_GET_URL "" // HTTP GET下载链接,如“/get.bin”
#define HTTP_FILE_PERPACKET_DOWNLOAD_LEN	1024	//HTTP文件每包下载大小

//上传的amr音频文件(3302字节):内容为“欢迎使用中移模组”
const unsigned char amr_file_data[] = {
	0x23, 0x21, 0x41, 0x4D, 0x52, 0x0A, 0x3C, 0x91, 0x17, 0x16, 0xBE, 0x66, 0x79, 0xE1, 0xE0, 0x01, 
	0xE7, 0xAF, 0xF0, 0x40, 0x00, 0x00, 0x80, 0x00, 0x01, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0xCB, 0x20, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x48, 0x77, 0x24, 0x94, 0xCE, 0xD9, 0xE1, 0xE0, 0x01, 
	0xE7, 0xBA, 0xF0, 0x12, 0x01, 0x72, 0xC0, 0x00, 0x33, 0xCD, 0xB6, 0xDA, 0x92, 0x58, 0x00, 0x05, 
	0x64, 0xA2, 0x49, 0xBB, 0x64, 0xC0, 0x3C, 0x90, 0xED, 0x17, 0xBE, 0x89, 0xD9, 0xC1, 0xC8, 0x01, 
	0xE7, 0xAF, 0xE2, 0xEA, 0x39, 0xF1, 0xBB, 0x5E, 0xA2, 0xA4, 0x35, 0x3B, 0x24, 0x82, 0xDF, 0xC9, 
	0xE6, 0xD3, 0x6B, 0x2B, 0x65, 0x80, 0x3C, 0x3E, 0x69, 0xC3, 0xEC, 0x24, 0x4C, 0x40, 0x00, 0x73, 
	0x65, 0xFA, 0x45, 0xD3, 0x7E, 0x03, 0xC0, 0x92, 0x58, 0x4A, 0x7E, 0xF3, 0x7C, 0xBA, 0x49, 0xB0, 
	0xA5, 0x54, 0x56, 0xEC, 0xDB, 0xC0, 0x3C, 0x3A, 0x52, 0x3E, 0x50, 0x73, 0x6E, 0x8D, 0xE0, 0xF7, 
	0x99, 0x5B, 0x48, 0x86, 0x66, 0xC6, 0xA5, 0x61, 0x83, 0x5B, 0x41, 0xCE, 0xC9, 0xCC, 0x6F, 0x9F, 
	0x7B, 0xCE, 0xF9, 0x32, 0x28, 0xB0, 0x3C, 0x48, 0x58, 0x06, 0x55, 0xBE, 0xAA, 0x0E, 0x81, 0xE8, 
	0xC9, 0x7F, 0x87, 0x93, 0x3F, 0x8D, 0x1D, 0x29, 0x4E, 0x8C, 0x0A, 0xC2, 0x74, 0x0F, 0x7B, 0x37, 
	0xE8, 0x77, 0xFB, 0xDC, 0x03, 0x70, 0x3C, 0x72, 0x4C, 0x0E, 0x0A, 0xEF, 0xE2, 0x34, 0x01, 0xF2, 
	0x7D, 0xDF, 0x67, 0x41, 0x53, 0xEB, 0x9B, 0x06, 0xF6, 0xB1, 0xB7, 0xDB, 0x44, 0x57, 0x44, 0x28, 
	0xA2, 0xA6, 0xA0, 0xE1, 0x56, 0x60, 0x3C, 0x34, 0x5C, 0x0B, 0x5D, 0xC5, 0xE5, 0x92, 0xA1, 0xEE, 
	0xD1, 0xAF, 0xAA, 0xBF, 0xDC, 0x10, 0xBA, 0xA0, 0x03, 0x31, 0x4B, 0x9B, 0xD5, 0x52, 0x60, 0x77, 
	0xC1, 0xD1, 0xC4, 0x0F, 0x5F, 0x00, 0x3C, 0x6E, 0x62, 0x0E, 0x5A, 0x8E, 0xB1, 0x3D, 0x10, 0xEE, 
	0xCC, 0xFF, 0x26, 0x06, 0x38, 0xC0, 0x9D, 0xFD, 0x28, 0x61, 0x10, 0xDE, 0x49, 0xFF, 0x8B, 0xF0, 
	0x22, 0xA4, 0xF9, 0xA8, 0x7C, 0x20, 0x3C, 0x0F, 0x78, 0x04, 0x68, 0x48, 0x90, 0xE9, 0xB8, 0x2E, 
	0x95, 0x2A, 0x7B, 0x96, 0x2F, 0x9E, 0xCC, 0xF1, 0xCB, 0x02, 0x8D, 0x0C, 0x69, 0xC6, 0x85, 0x90, 
	0x40, 0x40, 0xF1, 0x88, 0x4E, 0x20, 0x3C, 0x46, 0x4C, 0x3E, 0x08, 0x19, 0x85, 0xF9, 0xB8, 0x67, 
	0x81, 0xBA, 0x4D, 0x81, 0x3C, 0x7A, 0xD4, 0x2C, 0x68, 0x46, 0x3E, 0x44, 0x1D, 0xDA, 0x63, 0x17, 
	0xFF, 0x60, 0xDF, 0xE1, 0x14, 0xF0, 0x3C, 0x1E, 0x46, 0x0F, 0x00, 0x19, 0x9D, 0xFE, 0x01, 0xFC, 
	0xD7, 0x4F, 0x07, 0xE0, 0xD5, 0xBA, 0x4C, 0xEA, 0x51, 0xB1, 0x04, 0x14, 0x06, 0x84, 0x2F, 0xBA, 
	0x88, 0x23, 0xF5, 0x79, 0xF6, 0x00, 0x3C, 0x30, 0x44, 0x32, 0x10, 0x19, 0x99, 0xE9, 0x74, 0xAF, 
	0x86, 0x2B, 0xB1, 0x74, 0x5A, 0xB1, 0x45, 0xD2, 0x49, 0x27, 0x7B, 0x55, 0xFC, 0xDA, 0x9A, 0x00, 
	0xE1, 0x4D, 0x23, 0x8A, 0x6E, 0xD0, 0x3C, 0x0D, 0x7A, 0x47, 0x28, 0x19, 0xA4, 0x79, 0x21, 0xFF, 
	0x81, 0x5F, 0x4A, 0x9F, 0xD4, 0x42, 0xBE, 0xC7, 0x20, 0x83, 0xF7, 0x8B, 0xA5, 0x9C, 0x24, 0xB8, 
	0xB9, 0x3E, 0x54, 0xBC, 0x70, 0x50, 0x3C, 0x24, 0x1C, 0x56, 0x98, 0x19, 0x9A, 0xF1, 0x01, 0xFF, 
	0x85, 0x6C, 0x2C, 0x1C, 0xA0, 0xF6, 0x3A, 0x09, 0x39, 0x6C, 0x16, 0x50, 0xBB, 0x48, 0xA5, 0xB8, 
	0x0B, 0x25, 0x2B, 0xE0, 0x09, 0x10, 0x3C, 0x0D, 0x7A, 0x3C, 0xB0, 0x1B, 0x52, 0x79, 0x68, 0x71, 
	0x2A, 0xCA, 0xCC, 0x5F, 0x93, 0x12, 0xD9, 0xCB, 0x82, 0xCA, 0x6A, 0xFA, 0x4C, 0x89, 0x61, 0xA9, 
	0x11, 0x9B, 0x73, 0xBD, 0x76, 0x90, 0x3C, 0x1C, 0x0E, 0x38, 0x90, 0x1E, 0x97, 0x69, 0xA1, 0xF1, 
	0x81, 0x9A, 0x7B, 0x02, 0xC5, 0xAC, 0xB4, 0x5A, 0x90, 0xD6, 0x3B, 0x4F, 0x6D, 0xAE, 0x70, 0x14, 
	0x02, 0x00, 0x77, 0xF3, 0xD6, 0xB0, 0x3C, 0x07, 0x80, 0x37, 0xB0, 0x35, 0x77, 0xEC, 0x61, 0xCB, 
	0x86, 0xB8, 0xB7, 0x92, 0x79, 0x3B, 0xDF, 0x77, 0xBD, 0xEA, 0xE0, 0xBB, 0x4C, 0xB8, 0xB9, 0x06, 
	0xB0, 0x61, 0xD1, 0xCC, 0xD8, 0x90, 0x3C, 0x12, 0x06, 0x4F, 0xF8, 0x63, 0x49, 0xE1, 0xC1, 0xF7, 
	0x8B, 0x5A, 0x8C, 0x3D, 0xA1, 0x6C, 0xC0, 0xF9, 0x02, 0xE6, 0xDD, 0xDD, 0xBC, 0xE2, 0xA0, 0x7B, 
	0x68, 0x53, 0x6C, 0xFE, 0x10, 0xF0, 0x3C, 0x08, 0x0E, 0x37, 0xD0, 0x78, 0xA5, 0xFD, 0xA1, 0xFB, 
	0x8C, 0xCA, 0xD3, 0x7A, 0x75, 0x68, 0x80, 0x5E, 0x84, 0xB6, 0xCD, 0x07, 0x6D, 0x44, 0x07, 0x37, 
	0x31, 0xFF, 0x9B, 0x94, 0x4A, 0x90, 0x3C, 0x16, 0x06, 0x8D, 0xC8, 0xD5, 0x07, 0xF8, 0x01, 0xFB, 
	0x83, 0xCA, 0x37, 0xB2, 0x8F, 0x2D, 0xC9, 0x5A, 0xF8, 0xDB, 0x05, 0x79, 0x85, 0xA8, 0x9A, 0x98, 
	0x75, 0xB9, 0x39, 0xE3, 0x7F, 0xB0, 0x3C, 0x0E, 0x1A, 0xF2, 0xD1, 0x83, 0x49, 0xE9, 0xF0, 0xEF, 
	0x83, 0x49, 0xB0, 0xCD, 0x7E, 0x98, 0x59, 0xFB, 0xF9, 0xB5, 0x74, 0x09, 0x2F, 0x27, 0x33, 0xB0, 
	0x43, 0x74, 0xCC, 0x0F, 0xF8, 0x90, 0x3C, 0x16, 0x0E, 0xF1, 0xC9, 0x86, 0x7D, 0xF9, 0x61, 0xFE, 
	0xD7, 0x06, 0x28, 0xED, 0xD9, 0x6B, 0x68, 0xE6, 0x3F, 0xCB, 0xD8, 0x82, 0x05, 0xCC, 0xB7, 0x58, 
	0x99, 0x14, 0xAE, 0xD5, 0x29, 0xC0, 0x3C, 0x0E, 0x1E, 0xF8, 0xC9, 0x84, 0xF3, 0xE1, 0x29, 0x61, 
	0x80, 0x47, 0x7E, 0x06, 0x3F, 0x16, 0xE0, 0x4A, 0xE7, 0x2C, 0x58, 0xA5, 0xF6, 0x38, 0x07, 0x46, 
	0x1E, 0xC4, 0xB7, 0x67, 0x86, 0xB0, 0x3C, 0x1E, 0x0F, 0xA2, 0xE9, 0x81, 0x13, 0xE1, 0x81, 0xF8, 
	0x7D, 0xB4, 0x77, 0x86, 0xDD, 0xB4, 0xF1, 0x84, 0x8D, 0x86, 0x91, 0x5A, 0x53, 0x0E, 0xFC, 0x30, 
	0x29, 0x29, 0x9A, 0xD9, 0x8D, 0x90, 0x3C, 0x0E, 0x0E, 0x0D, 0x40, 0x7D, 0xAB, 0xE1, 0x41, 0xE7, 
	0x81, 0x68, 0x3D, 0x07, 0xC8, 0xBF, 0x15, 0xB9, 0x7B, 0xF3, 0x23, 0x4C, 0x24, 0xC1, 0xEA, 0xF6, 
	0x00, 0xC2, 0x63, 0x2A, 0x48, 0xF0, 0x3C, 0x0E, 0x13, 0xA8, 0x18, 0x79, 0x63, 0xF2, 0xC1, 0xED, 
	0x2A, 0xDA, 0x03, 0x0A, 0x1C, 0x94, 0x1E, 0x60, 0xF0, 0x8D, 0xFF, 0xB8, 0x01, 0x6A, 0x98, 0xDB, 
	0x38, 0x3D, 0xFE, 0x0A, 0x66, 0x70, 0x3C, 0x12, 0x06, 0xC6, 0x90, 0x67, 0xE7, 0xCB, 0x23, 0xD5, 
	0x2A, 0xCA, 0x94, 0xFD, 0x32, 0x12, 0x14, 0x05, 0x8F, 0x72, 0xD8, 0x3E, 0xE0, 0x02, 0xDA, 0xB7, 
	0xB0, 0xC8, 0x9E, 0x22, 0x1F, 0x30, 0x3C, 0x0F, 0xBF, 0x38, 0xB8, 0xE7, 0x1B, 0x19, 0xE5, 0xBF, 
	0x9A, 0xBA, 0xCC, 0x39, 0xF7, 0xC2, 0xED, 0xE0, 0xD1, 0x3D, 0x5F, 0xB3, 0x02, 0x76, 0x09, 0x11, 
	0x4D, 0x9C, 0xCF, 0x5D, 0x8C, 0xF0, 0x3C, 0x35, 0x24, 0xAF, 0xA5, 0xFF, 0xBB, 0x1F, 0x14, 0xAB, 
	0x0C, 0xDF, 0x8F, 0x80, 0x4B, 0x86, 0xAA, 0xCF, 0x1B, 0xB2, 0xDD, 0x2E, 0xC7, 0x6E, 0x44, 0x5B, 
	0x66, 0x42, 0xE9, 0x31, 0x48, 0x60, 0x3C, 0x91, 0xE8, 0xB7, 0xB8, 0xAD, 0xE2, 0x60, 0x9E, 0x10, 
	0xB1, 0xAF, 0x91, 0x5E, 0x13, 0xBD, 0xB0, 0x0E, 0xDD, 0x6E, 0x78, 0xFD, 0x69, 0xAE, 0x75, 0xE1, 
	0x1A, 0x49, 0x36, 0xCB, 0x06, 0x40, 0x3C, 0x9B, 0xD4, 0xB7, 0xC8, 0x4A, 0x4E, 0x23, 0xEF, 0x11, 
	0x80, 0x0A, 0xBC, 0x62, 0x9E, 0x75, 0xC8, 0x36, 0x63, 0xB6, 0xA2, 0xF7, 0x62, 0xF6, 0x16, 0x0B, 
	0x5A, 0xD9, 0xE0, 0x18, 0xD6, 0x10, 0x3C, 0x57, 0xFE, 0xB7, 0xB2, 0xE8, 0x3E, 0x38, 0xD2, 0xCD, 
	0x21, 0x4F, 0x27, 0x9E, 0xFD, 0x2B, 0xC2, 0xB3, 0x4F, 0x33, 0x03, 0xEB, 0x5A, 0x8A, 0x44, 0xCB, 
	0x38, 0xCA, 0xC7, 0x29, 0x7C, 0xC0, 0x3C, 0xB5, 0x20, 0xB7, 0xC4, 0x30, 0x62, 0x0B, 0xBA, 0x45, 
	0x80, 0x6A, 0x24, 0x1B, 0x3C, 0x4A, 0xC3, 0xDF, 0x6D, 0x96, 0xC3, 0xEC, 0x36, 0x79, 0x20, 0x28, 
	0xA4, 0x9E, 0xED, 0x67, 0xC4, 0x70, 0x3C, 0x01, 0x52, 0xB4, 0x90, 0x3E, 0x42, 0x2E, 0x9E, 0x11, 
	0xCB, 0x28, 0x42, 0x04, 0x1D, 0x24, 0x25, 0xD2, 0xD5, 0x46, 0x04, 0x0F, 0xD4, 0x5E, 0xCA, 0xB4, 
	0x7E, 0x80, 0xBC, 0xF7, 0x90, 0xD0, 0x3C, 0x56, 0x39, 0x0F, 0x30, 0xD5, 0xF1, 0x65, 0xBE, 0x01, 
	0x96, 0xC5, 0x4B, 0x52, 0x0D, 0x2C, 0x29, 0xF6, 0x27, 0xE8, 0x83, 0xFB, 0x60, 0xD0, 0x42, 0x17, 
	0x4E, 0xCD, 0x5E, 0x03, 0x2A, 0x10, 0x3C, 0x0C, 0x07, 0x0E, 0x91, 0x87, 0xB6, 0x78, 0x72, 0xCD, 
	0x87, 0x37, 0xCF, 0x60, 0x19, 0xE1, 0x28, 0x82, 0xB9, 0x81, 0x13, 0xB5, 0x64, 0x8C, 0x4B, 0xC0, 
	0x12, 0xE5, 0xA5, 0xA4, 0x97, 0x80, 0x3C, 0x3B, 0x79, 0x13, 0x99, 0x98, 0x66, 0x1F, 0xDC, 0x23, 
	0x84, 0x66, 0xD0, 0x9A, 0xA7, 0x8D, 0x79, 0xEF, 0x97, 0xFD, 0x9F, 0x67, 0xA4, 0x24, 0x1A, 0x9C, 
	0x5B, 0x04, 0xC9, 0x1F, 0xCA, 0xA0, 0x3C, 0x08, 0x07, 0x1C, 0xB9, 0x92, 0xAE, 0x3C, 0x01, 0xFB, 
	0x2E, 0xC6, 0x6E, 0xC1, 0xBE, 0x07, 0x96, 0xC3, 0x08, 0xD9, 0x3C, 0x68, 0x40, 0x91, 0xEC, 0x52, 
	0xE8, 0x61, 0x97, 0xCA, 0xDE, 0x50, 0x3C, 0x0C, 0x10, 0x91, 0xA1, 0x86, 0x5D, 0x60, 0xE3, 0xD4, 
	0x79, 0xD4, 0x4C, 0x9D, 0x8E, 0x0A, 0xCD, 0x02, 0x13, 0x7B, 0x8D, 0xAD, 0x52, 0x9D, 0xEF, 0x46, 
	0xF1, 0x47, 0xEA, 0x4D, 0x4E, 0xB0, 0x3C, 0x1A, 0x0F, 0xCB, 0xA1, 0x2B, 0x27, 0xFF, 0xEB, 0x50, 
	0x7B, 0x14, 0x52, 0x60, 0xA9, 0xE1, 0x95, 0xFE, 0x89, 0xAD, 0x92, 0xFE, 0x14, 0x60, 0x39, 0x26, 
	0x90, 0xB4, 0x4D, 0xB3, 0xBA, 0xA0, 0x3C, 0x16, 0x0E, 0x97, 0x10, 0x78, 0x73, 0xEF, 0x92, 0xCD, 
	0x81, 0x43, 0x89, 0x90, 0xCF, 0x9E, 0x45, 0x2F, 0x8D, 0xA4, 0xC1, 0xD4, 0xE2, 0x9B, 0x5A, 0x99, 
	0x24, 0x37, 0xFF, 0x30, 0xAE, 0x30, 0x3C, 0x28, 0x39, 0xBA, 0x60, 0x78, 0x77, 0xFB, 0x9E, 0x00, 
	0xD7, 0x16, 0xA4, 0xF1, 0x78, 0x47, 0x99, 0x22, 0xE8, 0x00, 0x15, 0x4A, 0x2D, 0x13, 0xDF, 0x4C, 
	0xF2, 0x03, 0x3F, 0x76, 0xD9, 0x90, 0x3C, 0x26, 0x46, 0x0E, 0x60, 0x78, 0xD9, 0x68, 0xA5, 0xBB, 
	0x84, 0xA7, 0x55, 0x0F, 0x8F, 0x50, 0x44, 0x3E, 0x42, 0xEE, 0xD8, 0xB0, 0x62, 0xBC, 0xF7, 0x7D, 
	0xF6, 0xAE, 0x90, 0xA3, 0xC0, 0x70, 0x3C, 0x30, 0x3C, 0x02, 0x60, 0x79, 0xAE, 0xF1, 0x61, 0xFB, 
	0x86, 0x7A, 0xA7, 0xCB, 0x9E, 0x62, 0x9A, 0xDD, 0xE2, 0x62, 0x4A, 0xA6, 0x03, 0x57, 0x4B, 0xC8, 
	0x6B, 0x75, 0x85, 0x1A, 0x4D, 0xD0, 0x3C, 0x1F, 0x78, 0x04, 0x58, 0x7E, 0x91, 0xE0, 0x01, 0xFB, 
	0x87, 0x3B, 0x02, 0xB7, 0xE9, 0xB0, 0x70, 0xC4, 0xF8, 0x40, 0xA9, 0x15, 0x79, 0x63, 0xD9, 0x5C, 
	0xAE, 0xA0, 0x43, 0xA9, 0x79, 0xD0, 0x3C, 0x1C, 0x18, 0x06, 0x58, 0xD5, 0x15, 0xE0, 0x61, 0xFC, 
	0xD5, 0x79, 0x80, 0x0C, 0x9D, 0xBA, 0xF4, 0x2A, 0xB5, 0xA4, 0x69, 0xE7, 0xA4, 0xB4, 0xD4, 0x0C, 
	0x55, 0x10, 0x53, 0x3B, 0x46, 0x90, 0x3C, 0x1E, 0x13, 0xAE, 0xE9, 0x80, 0x25, 0xFA, 0x01, 0xED, 
	0x8D, 0x57, 0x60, 0x1F, 0xC4, 0xF8, 0x86, 0xEE, 0x05, 0xC1, 0x92, 0x8E, 0xE3, 0x52, 0x07, 0xAE, 
	0xDE, 0x58, 0xF4, 0xFA, 0x5A, 0xE0, 0x3C, 0x12, 0x09, 0xB1, 0x51, 0x80, 0xDE, 0xF1, 0x81, 0xD6, 
	0xD5, 0x48, 0xBD, 0x0D, 0x0F, 0xC7, 0x25, 0x25, 0x8B, 0x78, 0x12, 0x50, 0x01, 0xF6, 0x33, 0xA4, 
	0x9D, 0x41, 0x0F, 0xA6, 0xC0, 0xF0, 0x3C, 0x0E, 0x06, 0x39, 0x89, 0x82, 0x55, 0x0E, 0xE1, 0xC7, 
	0x2E, 0x98, 0xAD, 0xD4, 0x66, 0xB7, 0x2B, 0x94, 0x89, 0xA6, 0xC1, 0xCF, 0xC6, 0x32, 0xC3, 0x0F, 
	0x1D, 0x4E, 0xEB, 0x72, 0xDD, 0x00, 0x3C, 0x0E, 0x07, 0x9B, 0x21, 0x84, 0xB7, 0x0E, 0x80, 0x93, 
	0x2A, 0xBA, 0x2A, 0x04, 0x62, 0x95, 0xC8, 0x83, 0xE5, 0xBE, 0x4E, 0x9D, 0xD7, 0x82, 0x0D, 0x13, 
	0xFE, 0x30, 0xAD, 0x40, 0xD5, 0xF0, 0x3C, 0x0E, 0x22, 0x67, 0x41, 0x2E, 0x09, 0x15, 0x21, 0xA5, 
	0x54, 0x3B, 0xBD, 0xFB, 0xAA, 0x23, 0x48, 0xAA, 0x82, 0xE9, 0x5F, 0xB0, 0x53, 0x26, 0xAC, 0xA4, 
	0xAB, 0x85, 0xAB, 0xDB, 0x3A, 0x80, 0x3C, 0x4C, 0xEE, 0x89, 0x3C, 0x23, 0xA8, 0xBA, 0xE3, 0xC5, 
	0x8A, 0xCF, 0x5E, 0xE5, 0xD8, 0x27, 0xC9, 0xD6, 0xE2, 0x1C, 0xA1, 0xFE, 0x8A, 0xB1, 0xBE, 0xE6, 
	0x1F, 0x06, 0xBB, 0xDA, 0xE1, 0x40, 0x3C, 0x43, 0xC4, 0xAC, 0x92, 0xF5, 0xEA, 0x21, 0xD6, 0x89, 
	0x2B, 0x4B, 0x56, 0x40, 0x04, 0x21, 0x41, 0x3D, 0x2B, 0x8F, 0x47, 0x92, 0x6C, 0x2B, 0x99, 0x49, 
	0x48, 0x28, 0x8F, 0x00, 0x56, 0x00, 0x3C, 0x25, 0x4E, 0x6E, 0x25, 0x0C, 0xE5, 0x2A, 0x1E, 0x01, 
	0xDC, 0xDF, 0xD7, 0xDF, 0x93, 0x51, 0x8C, 0xA5, 0xD4, 0x84, 0xE2, 0x77, 0x3E, 0xF6, 0x33, 0x6B, 
	0x1A, 0x71, 0xC7, 0xF8, 0xF6, 0xD0, 0x3C, 0x12, 0x56, 0x18, 0x58, 0x1E, 0x5B, 0x70, 0xCD, 0x33, 
	0x80, 0x9C, 0x39, 0xD9, 0x95, 0x22, 0x2B, 0xEF, 0x04, 0xBB, 0xF1, 0xEA, 0x15, 0x84, 0xD3, 0x3B, 
	0x98, 0x5B, 0xE3, 0x04, 0xBE, 0xB0, 0x3C, 0x1E, 0x23, 0xB2, 0x68, 0x1E, 0x31, 0x4E, 0xB4, 0xAB, 
	0x80, 0x08, 0x85, 0x5B, 0x72, 0xAD, 0xF2, 0x2B, 0xA9, 0x14, 0x10, 0x37, 0x9C, 0x82, 0x84, 0x46, 
	0x14, 0xC8, 0x90, 0xEB, 0x23, 0x30, 0x3C, 0x12, 0x50, 0x02, 0xE8, 0x1E, 0x1D, 0x4B, 0x25, 0xA9, 
	0x80, 0x6B, 0xFA, 0x46, 0x5C, 0x12, 0x41, 0x5B, 0xE7, 0x91, 0x27, 0x7D, 0x51, 0x80, 0xE7, 0x4C, 
	0x26, 0xC4, 0xAE, 0xB8, 0x43, 0xD0, 0x3C, 0x1C, 0x3A, 0x08, 0x10, 0x1E, 0x1F, 0x6D, 0xC1, 0xED, 
	0x81, 0x1A, 0xA8, 0xE9, 0x60, 0x31, 0xF4, 0xA1, 0x45, 0xBB, 0x25, 0xC0, 0x59, 0x14, 0x81, 0xB0, 
	0x12, 0x63, 0x0E, 0x51, 0x0D, 0x90, 0x3C, 0x13, 0x78, 0x03, 0xE8, 0x1E, 0x1B, 0xD7, 0x61, 0xFC, 
	0xD5, 0xDB, 0x93, 0x43, 0xD2, 0xF9, 0x4A, 0x4E, 0x65, 0x0A, 0x4B, 0x11, 0x3F, 0x35, 0xAD, 0x7E, 
	0x7E, 0x51, 0x4E, 0x7F, 0x4A, 0x60, 0x3C, 0x12, 0x12, 0x0F, 0x10, 0x1E, 0x21, 0xF5, 0x01, 0xEB, 
	0x85, 0x5A, 0x8E, 0xFB, 0x38, 0x87, 0xC3, 0xAA, 0x0C, 0x13, 0x97, 0x26, 0x30, 0x0E, 0xE9, 0x81, 
	0x81, 0xA7, 0xFB, 0xA4, 0xB4, 0x10, 0x3C, 0x12, 0x0E, 0x09, 0x88, 0x1E, 0x79, 0x4F, 0x81, 0xE1, 
	0x86, 0xA9, 0x87, 0x3F, 0xE4, 0x3B, 0xC5, 0x7B, 0xF9, 0xA2, 0xAB, 0x8F, 0xDA, 0x9B, 0x68, 0x35, 
	0x70, 0x85, 0x2F, 0xEA, 0x17, 0x50, 0x3C, 0x0C, 0x0F, 0x9F, 0x40, 0x1F, 0xAF, 0xA5, 0xA1, 0xCB, 
	0x86, 0xEA, 0x9D, 0x20, 0x64, 0xED, 0x87, 0x87, 0xD6, 0x4F, 0x31, 0x35, 0xAD, 0x96, 0x13, 0x34, 
	0xBF, 0x80, 0x59, 0xC2, 0xCF, 0x80, 0x3C, 0x15, 0x80, 0x3F, 0xE0, 0x60, 0xED, 0xF9, 0xF0, 0xE5, 
	0x8D, 0xFA, 0xCD, 0x79, 0x77, 0x82, 0xD7, 0xCE, 0xA4, 0xEF, 0x3D, 0xFB, 0xA6, 0x0C, 0x5B, 0x62, 
	0x5F, 0x39, 0x7F, 0xE0, 0x47, 0x80, 0x3C, 0x06, 0x07, 0x76, 0xD0, 0x6C, 0x4B, 0xE0, 0x21, 0xEB, 
	0x99, 0xFF, 0x3F, 0xDA, 0x44, 0x97, 0xA9, 0x2F, 0x84, 0x92, 0x19, 0x49, 0x30, 0x1D, 0xE4, 0x29, 
	0xC9, 0xED, 0xCD, 0xE4, 0x0C, 0x20, 0x3C, 0x1A, 0x0F, 0xA5, 0xC8, 0x7B, 0x7F, 0xF8, 0xE1, 0xED, 
	0x87, 0xCA, 0xBB, 0xF8, 0x82, 0x30, 0xF9, 0x66, 0xDA, 0x13, 0x2D, 0x6C, 0x71, 0x2C, 0x68, 0x90, 
	0x85, 0x2B, 0x79, 0x34, 0xDE, 0xB0, 0x3C, 0x0C, 0x06, 0x58, 0xD0, 0x7F, 0xBD, 0xE9, 0x61, 0xEF, 
	0x86, 0x1F, 0x7D, 0x52, 0xFB, 0x48, 0x81, 0xF5, 0x9E, 0x83, 0x90, 0x0E, 0xDC, 0x88, 0xE3, 0x20, 
	0xB6, 0x66, 0xED, 0xBE, 0x39, 0x60, 0x3C, 0x1A, 0x0E, 0x56, 0xC0, 0x7F, 0x3F, 0xFE, 0x05, 0xBB, 
	0x80, 0xE5, 0xE0, 0xEF, 0xB4, 0xF1, 0x78, 0x1E, 0x98, 0x52, 0xA2, 0xD1, 0xD6, 0xC0, 0x00, 0xB0, 
	0x01, 0x30, 0x3F, 0x2B, 0xE1, 0x20, 0x3C, 0x16, 0x10, 0x2F, 0xA0, 0x7E, 0x65, 0xDB, 0x30, 0xEF, 
	0x81, 0xDF, 0x85, 0xDF, 0x10, 0xF2, 0x63, 0x66, 0x07, 0x6B, 0xB7, 0x4F, 0x1B, 0x00, 0x48, 0x38, 
	0x04, 0x9D, 0xDF, 0xE3, 0x80, 0x10, 0x3C, 0x1A, 0x0E, 0x0A, 0x48, 0x7E, 0x4C, 0xE7, 0x81, 0x6B, 
	0x80, 0x97, 0xAE, 0x8F, 0x70, 0x37, 0x16, 0x43, 0x48, 0x00, 0x5B, 0x8A, 0x56, 0xCC, 0xAF, 0x69, 
	0x46, 0xA9, 0x34, 0x42, 0xA8, 0xF0, 0x3C, 0x0E, 0x08, 0x09, 0x48, 0x7E, 0x3F, 0x68, 0xE1, 0xCC, 
	0xD7, 0x7A, 0x7B, 0x5D, 0x26, 0x32, 0xF4, 0x13, 0x84, 0x11, 0xCB, 0x2D, 0xC3, 0x58, 0x16, 0x3D, 
	0xAE, 0x94, 0x4E, 0xD6, 0x3D, 0xC0, 0x3C, 0x16, 0x12, 0x06, 0x58, 0x7F, 0xE7, 0xF8, 0x61, 0xEB, 
	0x83, 0x3A, 0x38, 0x4A, 0xE9, 0xD5, 0xF4, 0xDF, 0xE9, 0x28, 0xF7, 0x2F, 0xA4, 0xE2, 0xCF, 0x16, 
	0xDA, 0x0B, 0x08, 0x40, 0xB9, 0x20, 0x3C, 0x16, 0x12, 0x45, 0x59, 0x80, 0x21, 0xF6, 0x81, 0xA7, 
	0x8D, 0x9A, 0x07, 0xD7, 0xEC, 0x21, 0xD8, 0x6A, 0x45, 0xAC, 0x16, 0x66, 0x02, 0xFA, 0xD5, 0xA0, 
	0x35, 0xFC, 0x55, 0x38, 0xBC, 0x10, 0x3C, 0x1F, 0x79, 0xBE, 0x51, 0x86, 0xBD, 0xF9, 0xE1, 0xF3, 
	0x98, 0xEA, 0x75, 0xF5, 0x6B, 0xA0, 0x3D, 0x29, 0xAA, 0xF7, 0x82, 0xCC, 0x3F, 0xA9, 0x0F, 0x2D, 
	0x09, 0xFC, 0x5A, 0x2E, 0x52, 0x90, 0x3C, 0x21, 0x7A, 0x01, 0x59, 0x9B, 0x11, 0xA7, 0xC1, 0x9F, 
	0x84, 0xBA, 0x8B, 0x2F, 0xF7, 0x90, 0xCF, 0x9A, 0xA3, 0xF8, 0x9F, 0x1E, 0x2F, 0x6C, 0x52, 0x58, 
	0x25, 0x00, 0xB3, 0x00, 0xF5, 0x80, 0x3C, 0x28, 0x45, 0xB6, 0x69, 0x9E, 0x4F, 0xF9, 0xD0, 0xEB, 
	0x82, 0x1A, 0x6B, 0xDD, 0x4A, 0xC5, 0xF9, 0xF5, 0x67, 0x1D, 0x16, 0xF4, 0x4F, 0x34, 0x0B, 0x2F, 
	0x1D, 0xEB, 0x47, 0x6A, 0x28, 0x20, 0x3C, 0x30, 0x3D, 0xBE, 0x59, 0x9E, 0x07, 0xF1, 0x67, 0x80, 
	0x7D, 0x3F, 0x4C, 0xC0, 0xCE, 0xE2, 0xA2, 0x03, 0x35, 0x07, 0x2B, 0x6A, 0xAF, 0x62, 0x45, 0xA0, 
	0xFB, 0x16, 0x88, 0x91, 0x54, 0x40, 0x3C, 0x36, 0x40, 0x10, 0x61, 0x93, 0xED, 0xF3, 0xC7, 0x98, 
	0x6D, 0x7F, 0xD4, 0x4A, 0x4E, 0xF1, 0x55, 0x9F, 0x9F, 0x68, 0x2F, 0x70, 0x2E, 0x17, 0x6E, 0x77, 
	0x04, 0x2F, 0x89, 0xAF, 0x7B, 0xF0, 0x3C, 0x30, 0x41, 0xB1, 0x01, 0x84, 0x63, 0xF0, 0x2D, 0x32, 
	0x7C, 0x09, 0x41, 0xBC, 0x49, 0x89, 0xAA, 0x8F, 0x86, 0xA1, 0x82, 0xEE, 0x2C, 0x1B, 0x28, 0xD8, 
	0xFB, 0x00, 0xF1, 0xF2, 0x41, 0x20, 0x3C, 0x31, 0x78, 0x24, 0x58, 0x7D, 0xAB, 0xC3, 0x2B, 0x54, 
	0x7D, 0x78, 0xEE, 0xDD, 0xA9, 0xA3, 0x5C, 0x6B, 0x49, 0x3A, 0x92, 0x00, 0x1B, 0xF7, 0x72, 0x48, 
	0xF0, 0x17, 0x93, 0x43, 0x0D, 0x90, 0x3C, 0x17, 0x7A, 0x21, 0x00, 0x78, 0x78, 0x1F, 0x01, 0xE5, 
	0x89, 0x3A, 0xED, 0x76, 0x55, 0xC8, 0xA0, 0x62, 0x72, 0xF2, 0x70, 0x2D, 0xA8, 0x6C, 0x7E, 0x17, 
	0x08, 0x8A, 0x08, 0xE0, 0x4F, 0x70, 0x3C, 0x06, 0x28, 0x69, 0xE8, 0x79, 0x3E, 0x07, 0x81, 0x4A, 
	0xD3, 0x2A, 0x8C, 0x8A, 0x3D, 0x6E, 0xDE, 0xF4, 0xC4, 0xA2, 0xDE, 0xC5, 0x49, 0x90, 0x06, 0x12, 
	0xA5, 0x64, 0x4C, 0x9B, 0x46, 0x20, 0x3C, 0x1A, 0xC9, 0x67, 0x2B, 0x21, 0x35, 0x03, 0x41, 0xE0, 
	0x46, 0xCF, 0x49, 0x2A, 0x09, 0xA4, 0x13, 0x20, 0x60, 0x89, 0xD1, 0x5E, 0x77, 0xA8, 0xC2, 0x07, 
	0xF9, 0x6E, 0x4D, 0xAD, 0x61, 0xC0, 0x3C, 0x52, 0xFF, 0x53, 0x91, 0xEB, 0xAF, 0x16, 0xC1, 0xE1, 
	0x2E, 0x1C, 0x0F, 0xF7, 0x5E, 0xA5, 0xF5, 0x17, 0xBA, 0x95, 0x65, 0xA2, 0x77, 0x0A, 0xF5, 0xB8, 
	0xE4, 0x34, 0xF4, 0x09, 0x59, 0x50, 0x3C, 0x40, 0x72, 0x67, 0x3E, 0xA2, 0x06, 0x58, 0xE1, 0xFD, 
	0x80, 0xD8, 0x91, 0xA1, 0x72, 0xC7, 0x21, 0x9D, 0xA6, 0xC6, 0xBA, 0xE6, 0x36, 0x62, 0xF3, 0xB8, 
	0x73, 0xDA, 0x5E, 0xB8, 0x5C, 0x50, 0x3C, 0x4B, 0x0A, 0x6C, 0x4D, 0x67, 0x8B, 0x11, 0xA1, 0xFE, 
	0xC5, 0x16, 0xC6, 0x97, 0xBF, 0x4E, 0x8F, 0xDB, 0xE5, 0xFD, 0xDC, 0x5E, 0x2F, 0xE0, 0x84, 0x25, 
	0x0B, 0x4B, 0xEE, 0x5D, 0x32, 0xC0, 0x3C, 0x0C, 0x04, 0x0A, 0x64, 0x90, 0xAA, 0xFE, 0xD0, 0xEF, 
	0xC6, 0xA6, 0x63, 0x64, 0x06, 0x79, 0x7F, 0xA1, 0x38, 0x34, 0x75, 0xD8, 0xE1, 0x4D, 0x61, 0xA6, 
	0x3E, 0xC1, 0x7E, 0x3A, 0x95, 0x90, 0x3C, 0x30, 0x44, 0x02, 0xE9, 0x9A, 0x12, 0xD5, 0x25, 0x3B, 
	0x92, 0x18, 0x3F, 0x8B, 0xC1, 0xD2, 0xEB, 0x5C, 0x56, 0xFD, 0xA3, 0x52, 0x37, 0x1C, 0x7D, 0x5D, 
	0x85, 0xD7, 0xC1, 0xC2, 0xD0, 0x40, 0x3C, 0x0C, 0x13, 0xB5, 0x69, 0xB7, 0x51, 0xF1, 0xC1, 0xEF, 
	0x98, 0xD7, 0xF8, 0x8A, 0x0F, 0xA0, 0x1C, 0xA3, 0xD1, 0x24, 0xA2, 0xF1, 0x8A, 0xF3, 0x0C, 0x33, 
	0x69, 0xEB, 0xEA, 0x91, 0xD0, 0xF0, 0x3C, 0x16, 0x0F, 0xFF, 0xB1, 0xEC, 0x3A, 0x72, 0xC1, 0xC7, 
	0x87, 0x5A, 0x65, 0x3C, 0xCB, 0xEF, 0xB4, 0x04, 0x75, 0xD6, 0xFE, 0x2B, 0x40, 0xDD, 0x3D, 0x37, 
	0xF6, 0xD4, 0x16, 0x41, 0x5B, 0x60, 0x3C, 0x17, 0x7A, 0x07, 0x19, 0xFE, 0x29, 0xAD, 0xE1, 0xB5, 
	0x93, 0x58, 0xA6, 0xE8, 0x14, 0x58, 0xC2, 0x63, 0xA0, 0xE6, 0xE3, 0xF3, 0x57, 0xC2, 0x49, 0xEF, 
	0x9A, 0xA0, 0x6D, 0x14, 0x77, 0x80, 0x3C, 0x1A, 0x0F, 0xFF, 0x89, 0xFF, 0xAF, 0x69, 0x01, 0xC7, 
	0x83, 0xD8, 0xE4, 0xA3, 0x78, 0x9E, 0xC7, 0xB4, 0xD9, 0xC5, 0xD2, 0x32, 0xD8, 0x81, 0x2C, 0x43, 
	0xCB, 0x91, 0x4A, 0x5A, 0xF1, 0x60, 0x3C, 0x16, 0x0F, 0xBF, 0x46, 0x00, 0xB5, 0xF9, 0x21, 0x8F, 
	0x80, 0x4A, 0x0C, 0xD7, 0x9C, 0x99, 0x80, 0x3B, 0xA8, 0xAE, 0x2D, 0x20, 0x1B, 0x30, 0x0F, 0xE6, 
	0x40, 0x81, 0x77, 0x8E, 0x1C, 0x70, 0x3C, 0x0E, 0x0F, 0xBD, 0x46, 0x03, 0x71, 0x82, 0x50, 0x2D, 
	0xAD, 0x69, 0x23, 0xE1, 0x70, 0x5F, 0xB3, 0x50, 0x50, 0xB6, 0xC1, 0x2D, 0xF4, 0xD2, 0x61, 0x25, 
	0x76, 0x42, 0x37, 0x0E, 0xC9, 0xF0, 0x3C, 0x35, 0x7E, 0x51, 0x46, 0x12, 0xD8, 0x1E, 0xE1, 0xED, 
	0x0A, 0xCB, 0x88, 0xB6, 0xC1, 0xB5, 0x51, 0xF6, 0x51, 0x28, 0x50, 0x23, 0xC5, 0x94, 0x1E, 0x7E, 
	0xCB, 0x30, 0x8A, 0x01, 0x38, 0x80, 0x3C, 0x04, 0x0F, 0xFF, 0x40, 0x71, 0x62, 0x05, 0xA1, 0xC2, 
	0x83, 0xBA, 0xED, 0x15, 0x1C, 0x45, 0x86, 0x50, 0x95, 0xF6, 0x6D, 0x7A, 0x84, 0x7B, 0x37, 0x8E, 
	0x0E, 0xCD, 0xA9, 0xF7, 0x01, 0x50, 0x3C, 0x37, 0x7A, 0xF2, 0x8E, 0x22, 0xB0, 0x01, 0xE1, 0x0E, 
	0x51, 0x48, 0x80, 0xF1, 0x6A, 0xC2, 0x04, 0xD5, 0xE4, 0x38, 0xAF, 0xCF, 0xCE, 0x3F, 0x2A, 0x1F, 
	0x1B, 0xFF, 0x9C, 0x40, 0x2B, 0x40, 0x3C, 0x0E, 0x43, 0x8D, 0x46, 0x34, 0x9C, 0x00, 0xC1, 0x07, 
	0xC7, 0x6F, 0xB4, 0x54, 0x71, 0x50, 0xA0, 0x0E, 0x85, 0xA5, 0x64, 0x27, 0xB9, 0x23, 0xCD, 0x3C, 
	0xA5, 0x20, 0x2D, 0xC1, 0x88, 0xC0, 0x3C, 0x52, 0xF9, 0x6F, 0x97, 0xFB, 0x86, 0x01, 0x60, 0x43, 
	0xE3, 0xEA, 0x0C, 0xC5, 0x75, 0xC4, 0xF4, 0x61, 0xCD, 0xFF, 0x89, 0x5A, 0xC9, 0x24, 0x3A, 0xC2, 
	0xFB, 0x26, 0x81, 0x0E, 0xC4, 0xA0, 0x3C, 0xDA, 0x76, 0xA3, 0xA7, 0xF6, 0xEE, 0x00, 0x80, 0x39, 
	0xE3, 0x8A, 0x00, 0xB2, 0x08, 0x71, 0x0C, 0x3A, 0x12, 0x91, 0x80, 0x41, 0xDB, 0x4A, 0xB4, 0x6E, 
	0xD3, 0xF0, 0x20, 0x0F, 0xA4, 0x80, 0x3C, 0x52, 0x76, 0x93, 0xFA, 0xB8, 0x6A, 0x00, 0x01, 0x0A, 
	0x4F, 0x5A, 0x33, 0xAA, 0x19, 0x4C, 0xEA, 0x25, 0xA8, 0x21, 0x95, 0x46, 0x9E, 0xBB, 0xD5, 0x4E, 
	0x50, 0x69, 0x79, 0xBC, 0xB4, 0xC0, 0x3C, 0x6E, 0xF9, 0x2C, 0x9D, 0x79, 0xF4, 0x60, 0x60, 0x01, 
	0xE2, 0xEF, 0xB8, 0xC2, 0x02, 0x64, 0x94, 0xF0, 0x7E, 0x4D, 0x6C, 0x92, 0x00, 0x02, 0x20, 0x47, 
	0xF6, 0x92, 0x59, 0x20, 0x00, 0x20, 0x3C, 0x55, 0x17, 0x24, 0xBE, 0x1D, 0x01, 0x81, 0x90, 0x05, 
	0xE7, 0xA8, 0xC0, 0xA0, 0x08, 0x00, 0x12, 0x31, 0x00, 0x00, 0xC8, 0x48, 0x00, 0x01, 0x52, 0x78, 
	0x00, 0x0C, 0x52, 0x60, 0x00, 0x00
};

//使用http post往http服务器上传音频文件
int cm_http_file_post()
{
    cm_httpclient_handle_t http_client = NULL;
    cm_httpclient_ret_code_e ret = CM_HTTP_RET_CODE_UNKNOWN_ERROR;
    cm_httpclient_cfg_t client_cfg = {0};

    char http_header[256] = {0};

    /* 1. 申请文件上传时http body数据的缓冲区大小，用户可以自行更改 */
    uint8_t *http_content = cm_malloc(1024 * 4);
    if(http_content == NULL)
    {
        return -1;
    }
    memset(http_content,0,1024 * 4);

    /* 2. 创建http实例 */
    ret = cm_httpclient_create((const uint8_t *)HTTP_FILE_TEST_SERVER, NULL, &http_client);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        cm_free(http_content);
        cm_log_printf(0, "create http client fail");
        return -1;
    }
    client_cfg.ssl_enable = 0;
    client_cfg.ssl_id = 0;
    client_cfg.cid = 0;
    client_cfg.conn_timeout = HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT;
    client_cfg.rsp_timeout = HTTPCLIENT_WAITRSP_TIMEOUT_DEFAULT;
    client_cfg.dns_priority = 1;

    /* 3. 配置http参数 */
    ret = cm_httpclient_set_cfg(http_client, client_cfg);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        cm_free(http_content);
        cm_httpclient_delete(http_client);
        cm_log_printf(0,"http client set config fail...");
        return -2;
    }

    /* 4. 设置http header：以http文件方式上传，注意header里的boundary  “-”符号4个 */
    int http_headerlen = sprintf(http_header, "Content-Type: %s;boundary=----%s\r\n",HTTP_FILE_POST_HEADER_CONTENT_TYPE, HTTP_FILE_POST_HEADER_BOUNDARY);
    cm_httpclient_specific_header_set(http_client,(uint8_t *)http_header,(uint16_t)http_headerlen);
    cm_log_printf(0,"http_headerlen :%d",http_headerlen);
    
    /* 5. 封装http body，注意body里的boundary  “-”符号6个 */
    int http_contentlen = sprintf((char *)http_content, "------%s\r\nContent-Disposition: form-data;name=\"files[]\";filename=\"%s\"\r\nContent-Type: application/octet-stream\r\n\r\n", HTTP_FILE_POST_HEADER_BOUNDARY,HTTP_FILE_NAME);
    
    //将音频文件内容加入到报文中，注意数据的开始和结束都要有一个空行，以在上一条数据和下一条数据中加了“\r\n\r\n”，即保证数据前后都有一个空行
    memcpy(http_content + http_contentlen, amr_file_data, sizeof(amr_file_data));
    http_contentlen = http_contentlen + sizeof(amr_file_data);

    //加入换行符表示音频文件已加入完成，注意boundar结束后有 “-”符号2个
    http_contentlen += sprintf((char *)(http_content + http_contentlen),"\r\n\r\n------%s--",HTTP_FILE_POST_HEADER_BOUNDARY);
   
    /* 6. 发起http同步请求 */
    cm_httpclient_sync_response_t response = {0};
    cm_httpclient_sync_param_t param = {HTTPCLIENT_REQUEST_POST, (const uint8_t *)HTTP_FILE_TEST_POST_URL,(uint32_t)http_contentlen,(uint8_t *)http_content};
    ret = cm_httpclient_sync_request(http_client, param, &response);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        cm_log_printf(0,"cm_httpclient_sync_request() error! ret is %d\r\n", ret);
        cm_free(http_content);
        cm_httpclient_custom_header_free(http_client);
        cm_httpclient_sync_free_data(http_client);
        cm_httpclient_terminate(http_client);
        cm_httpclient_delete(http_client);
        return -3;
    }

    /* 7. 打印获取到的数据（因cm_log_printf打印长度有限，数据可能打印不完整）  */
    cm_log_printf(0,"http response code is %d\r\n", response.response_code);
    cm_log_printf(0,"http header is %d,%s\r\n", response.response_header_len,response.response_header);
    cm_log_printf(0,"response_content_len is %d,%s\r\n", response.response_content_len,response.response_content);

    cm_free(http_content);
    cm_httpclient_custom_header_free(http_client);
    cm_httpclient_sync_free_data(http_client);
    cm_httpclient_terminate(http_client);
    cm_httpclient_delete(http_client);
    return 0;
}

//使用http get从http服务器下载文件
int cm_http_file_get()
{
	cm_httpclient_handle_t http_client = NULL;
    cm_httpclient_ret_code_e ret = CM_HTTP_RET_CODE_UNKNOWN_ERROR;
    cm_httpclient_cfg_t client_cfg = {0};

	int http_ret = -1;
    char http_header[64] = {0};

	/* 1. 创建http实例 */
    ret = cm_httpclient_create((const uint8_t *)HTTP_FILE_TEST_SERVER, NULL, &http_client);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        cm_log_printf(0, "create http client fail");
        return -1;
    }
    client_cfg.ssl_enable = 0;
    client_cfg.ssl_id = 0;
    client_cfg.cid = 0;
    client_cfg.conn_timeout = HTTPCLIENT_CONNECT_TIMEOUT_DEFAULT;
    client_cfg.rsp_timeout = HTTPCLIENT_WAITRSP_TIMEOUT_DEFAULT;
    client_cfg.dns_priority = 1;

    /* 2. 配置http参数 */
    ret = cm_httpclient_set_cfg(http_client, client_cfg);
    if (CM_HTTP_RET_CODE_OK != ret)
    {
        cm_httpclient_delete(http_client);
        cm_log_printf(0,"http client set config fail...");
        return -2;
    }

	cm_httpclient_sync_response_t response = {0};
    cm_httpclient_sync_param_t param = {HTTPCLIENT_REQUEST_GET, (const uint8_t *)HTTP_FILE_TEST_GET_URL,0,NULL};

    /* 3. 设置header，并发起http同步请求，尝试先下载1字节数据，获取文件的大小 */
	sprintf(http_header, "Range:bytes=%d-%d\r\n", 0, 0);          // 使用Range配置下载文件始末
    cm_httpclient_custom_header_set(http_client, (uint8_t *)http_header, strlen((const char*)http_header));
	ret = cm_httpclient_sync_request(http_client, param, &response);
    if (CM_HTTP_RET_CODE_OK != ret)
	{
		http_ret = -3;
		goto EXIT;
	}
	int file_size = 0;
	char *file_size_offset = strstr((const char *)response.response_header, "Content-Range");
	if(file_size_offset != NULL)
	{
		if (sscanf((const char *)file_size_offset, "%*[^/]/%d", &file_size) != 1 || file_size <= 0)
		{
			http_ret = -4;
			goto EXIT;
		}
	}
	cm_log_printf(0,"http file size is:%d",file_size);
	
	/* 4. 设置header，开始分包下载数据 */
	int download_len = 0;
	while(download_len < file_size)
	{
		memset(http_header, 0, sizeof(http_header));
		if(file_size - download_len >= HTTP_FILE_PERPACKET_DOWNLOAD_LEN)
		{
			sprintf(http_header, "Range:bytes=%d-%d\r\n", download_len, download_len + HTTP_FILE_PERPACKET_DOWNLOAD_LEN - 1);
		}
		else	//最后一包数据
		{
			sprintf(http_header, "Range:bytes=%d-%d\r\n", download_len, file_size - 1);
		}
		cm_httpclient_custom_header_set(http_client, (uint8_t *)http_header, strlen((const char*)http_header));
		ret = cm_httpclient_sync_request(http_client, param, &response);
		if (CM_HTTP_RET_CODE_OK != ret)
		{
			http_ret = -3;
			goto EXIT;
		}
		download_len += response.response_content_len;
		cm_log_printf(0,"cm_httpclient_sync_request ret:%d,contentlen:%d,download:%d",response.response_code,response.response_content_len,download_len);
	}

	/* 5. 下载完成，打印下载长度 */
	cm_log_printf(0,"download file success,download len:%d",download_len);

EXIT:
    cm_httpclient_custom_header_free(http_client);
    cm_httpclient_sync_free_data(http_client);
    cm_httpclient_terminate(http_client);
    cm_httpclient_delete(http_client);
    return http_ret;
}

/* http测试线程 */
void cm_http_task_test(void *p)
{
    /* 等待模组PDP激活 */
    int32_t pdp_time_out = 0;
    while(1)
    {
        if(pdp_time_out > 20)
        {
            cm_log_printf(0, "network timeout\n");
            cm_pm_reboot();
        }
        if(cm_modem_get_pdp_state(1) == 1)
        {
            cm_log_printf(0, "network ready\n");
            break;
        }
        osDelay(200);
        pdp_time_out++;
    }

    /* 适当等待（模组PDP激活到能进行数据通信需要适当等待，或者通过PING操作确定模组数据通信是否可用） */
    osDelay(1000);

    /* 1. 使用http post方式上传文件到服务器 */
    cm_http_file_post();

    osDelay(400);

	/* 2. 使用http get方式分包下载文件 */
	cm_http_file_get();

    while(1)
    {
        osDelay(1000);
    }
}


/* opencpu程序主入口，禁止任何阻塞操作*/
int cm_opencpu_entry(void *arg)
{
    (void)arg;
    
    osThreadAttr_t app_task_attr = {0};
    app_task_attr.name  = "test_task";
    app_task_attr.stack_size = 1024 * 4;
    app_task_attr.priority = osPriorityNormal;

    osThreadNew((osThreadFunc_t)cm_http_task_test, 0, &app_task_attr);
    return 0;
}