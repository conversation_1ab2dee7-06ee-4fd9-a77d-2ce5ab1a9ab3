# 移动DM平台自注册  

## **相关平台和协议介绍**
DM（终端管理）技术为运营商和终端厂商提供了一种低成本的手段来维护管理终端软件和数据，主要功能包括终端自注册、业务配置、应用数据管理三大部分，适用于运营商的深度合作终端、战略合作终端及开放合作终端。

## **实现功能**
实现模组移动DM平台自注册功能：  
1. 设置自注册参数；  
2. 启动自注册；  

## **APP执行流程**
1. 设备上电，等待PDP激活；  
2. 配置连接参数；  
3. 启动自注册；  

## **使用说明**
- 支持的模组（子）型号：ML307R-DC/ML307C-DC-CN
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：**dmp接口在头文件cm_dmp.h中，使用前需要先在kernel中对应的.list文件下添加dmp接口，并且之后要进行kernel编译才能链接dmp相关函数，否则会出现函数接口未定义的报错**
- APP使用前提：开发板、SIM卡（APP需要上网）、移动DM平台账号、应用密钥和私钥

## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 10:26
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/10/22 18:42
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------