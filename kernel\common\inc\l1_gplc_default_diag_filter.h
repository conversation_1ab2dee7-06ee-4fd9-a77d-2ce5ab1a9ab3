/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/*******************************************************************************
*               MODULE HEADER FILE
********************************************************************************
* Title: l1_default_diag_filter
*
* Filename: l1_gplc_default_diag_filter.h
*
* Description: holds all default GPLC-related diag filter in power-up
*
* Last updated:
*
* Notes:
*******************************************************************************/

#ifndef _L1_GPLC_DEFAULT_DIAG_FILTER_H_
#define _L1_GPLC_DEFAULT_DIAG_FILTER_H_

/* Please add you traces below accoroding to the relevant subject
 *  The interface is:
	DIAG_SET_FILTER(CAT1,CAT2,CAT3,FALSE)  -> by default after L1 power up this trace will be close
	DIAG_SET_FILTER(CAT1,CAT2,CAT3,TRUE)  -> by default after L1 power up this trace will be Open
 *
 * */

// DEBUG 2G Ncell measurements during 2G Packet
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1BgGmphPccchConfigReq1, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1BgGmphPccchConfigReq2, FALSE)
//DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1BgMeasurementComplete, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrHandleStopNetCtrlMeasReq, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrHandleNetCtrlMeasReq, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, ReselectionMeasSubScheduler, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, ExtendedMeasSubScheduler, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsStartGPMeasRates, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsTaReselectionMeas, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsTaExtendedMeas, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsTaInterferenceMeas, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsTaCsCombinedMeas, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsInitialiseImsData, FALSE)
DIAG_SET_FILTER(GPLC, NCELL_PACKET_DEBUG, L1FrImsMeasScheduler, FALSE)

// Control loops
//===============
	// AFC AGING
DIAG_SET_FILTER(GPLC,AFC_AGING, AFC_AGING_TIMER_EXPIRED, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, TIMED_HO_INVALID, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, SIG_TIMER_EXPIRY_L1FR, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, SIG_TIMER_EXPIRY_L1FR_DEFAULT, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, L1FRLOOP_UPDATE_AFC, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, STOP_AFC_AGING_TIMER, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, START_AFC_AGING_TIMER, FALSE)
//DIAG_SET_FILTER(GPLC,AFC_AGING, RESTART_AFC_AGING_TIMER, TRUE)
// In C8 interRat flag is set to TRUE DIAG_SET_FILTER(GPLC,AFC_AGING, L1BgHandoverSyncToScell_1, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, SEARCHES_AND_GROUPS,FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, L1FrNull_4, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, ProcessDedicatedModeConfig_1, FALSE)

// DEBUG AFC

//DIAG_SET_FILTER(GPLC, AFC, MPH_HANDOVER_REQ, FALSE)
//DIAG_SET_FILTER(GPLC, AFC, L1_BG_SYNC_START, FALSE)
DIAG_SET_FILTER(GPLC, AFC, L1_SET_SERVING_CELL_1,FALSE)
DIAG_SET_FILTER(GPLC, AFC, L1_SET_SERVING_CELL_2,FALSE)
DIAG_SET_FILTER(GPLC, AFC, L1_SET_SERVING_CELL_3,FALSE)
DIAG_SET_FILTER(GPLC, AFC, CF_SAVE_AFC_NOMINAL_DAC,FALSE)
#if !defined (_QT_)
DIAG_SET_FILTER(GPLC, AFC, CF_CALC_NEW_AFC_DAC_VALUE_1,FALSE)
DIAG_SET_FILTER(GPLC, AFC, CF_CALC_NEW_AFC_DAC_VALUE_2,FALSE)
#endif	 // _QT_
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, PROCCESS_DEDICATED_MODE_CONFIG_1,FALSE)
DIAG_SET_FILTER(GPLC, AFC, PROCCESS_DEDICATED_MODE_CONFIG_2, FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_1,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_2,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_3,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_4,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_8,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_7,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_5,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_DED_6,FALSE)
// use only for 2G->2G, 3G->2G will cause data abort DIAG_SET_FILTER(GPLC, AFC, L1_FR_SYNC_1,FALSE)
DIAG_SET_FILTER(GPLC, AFC, PROCCESS_SYNC_SB,FALSE)
DIAG_SET_FILTER(GPLC, AFC, SQ_FBS_START_SYNC_SB_DECODE,FALSE)


// General
//DIAG_SET_FILTER(GPLC_COMM,SEQ,L1FrSeqAlloc,FALSE)
DIAG_SET_FILTER(GPLC_COMM,SEQ,L1FrSeqFree,FALSE)


// PTM
// Example: DIAG_SET_FILTER(MAC,RX,MacRxRadioBlockInd1,FALSE)
DIAG_SET_FILTER(GPLC_COMM,SYNC,BCCH_DEBUG_IDLEFRAME, FALSE)
DIAG_SET_FILTER(GPLC_COMM,SYNC,BCCH_DEBUG_HIGHPRIO, FALSE)
#if defined (INTEL_UPGRADE_GSM_CRL_IF) /*relevant for CRL modem only*/
DIAG_SET_FILTER(GPLC_COMM,AGC_DEBUG,AgcCharPTM,FALSE)
DIAG_SET_FILTER(Mac_IF_Rx_PayloadData, MacRxEgprsHeaderDecodeTrace, DecInfoRetFromMac, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, UPDATE_CONTROL_LOOP1, FALSE)
DIAG_SET_FILTER(GPLC,AFC_AGING, UPDATE_CONTROL_LOOP2, TRUE)
#endif /*INTEL_UPGRADE_GSM_CRL_IF*/

// PRACH
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_CNF,FALSE)
DIAG_SET_FILTER(GPLC_COMM,SYNC,UPDATE_TRIGGER_COUNTERS,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_TCB,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_USF,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_SCHED_TX_2,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_EVENT,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_INT,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_SCHED_FBS,FALSE)
DIAG_SET_FILTER(GPLC_COMM,IDLE_PRACH,DEBUG_PRACH_CNF_2, FALSE)

// Dedicated
DIAG_SET_FILTER(GPLC, AUDIO, L1SqDedAudEncoder1, FALSE)
DIAG_SET_FILTER(GPLC, AUDIO, L1SqDedAudEncoder2, FALSE)
DIAG_SET_FILTER(GPLC, AUDIO, CreateDedAudEncoderSequencer, FALSE)
DIAG_SET_FILTER(GPLC, AUDIO, L1SqDedVoice1, FALSE)
DIAG_SET_FILTER(GPLC, AUDIO, CreateDedVoiceSequencer, FALSE)
//DIAG_SET_FILTER( GPLC, HO, L1BgMphHandoverReq, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, PROCESS_HO_COMMAND, TimingAdvance , FALSE )
//DIAG_SET_FILTER( GPLC_COMM, DED, L1FrDedTaTxRachUnSync, TRUE )
DIAG_SET_FILTER( GPLC_COMM, Ded, DedRxTchFSeq1, FALSE )
DIAG_SET_FILTER( GPLC_COMM, Ded, DedRxTchFSeq2, FALSE )

#if defined (REPEATED_SACCH)
// traces for Repeated_SACCH debugging - closed by deafult
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, L1_PH_TASK_PH_DATA_REQ, TRUE)
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, L1PhRepeatedSacchOverrideL1HeaderPre, TRUE)
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, SetupNbTdsSetForTch, TRUE)	
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, SqFbsStartTxSacchT, TRUE)
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, L1FrProcessSacchHeader, TRUE)

// traces for R-SACCH - opened by default
DIAG_SET_FILTER(GPLC_COMM,R_SACCH, L1FrSendRts,TRUE)
#endif // (REPEATED_SACCH)
// AMR
//====
//DIAG_SET_FILTER( GPLC_COMM, AMR, SETUP_AMR_CFG_TDS_1, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, SETUP_AMR_CFG_TDS_2, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, SETUP_AMR_CFG_TDS_3, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, SETUP_AMR_CFG_TDS_4, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, L1_SQS_AMR_CONFIG, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, AMR_NO_CFG, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, AMRCONFIG, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, AFS_AMR_FLAGS, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, AHS_AMR_FLAGS, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, CONFIG_AMR_PARAMS_1, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, CONFIG_AMR_PARAMS_2, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, CONFIG_AMR_PARAMS_3, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, ENCODE_PARAMS_AHS1, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, ENCODE_PARAMS, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, ENCODE_PARAMS_AHS2, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, ENCODE_PARAMS_AHS_DTX, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, RATCCH, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, RATCCH_FUNC, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, RATCCH1, FALSE )
DIAG_SET_FILTER( GPLC_COMM, AMR, RATCCH2, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, RESTORE_1, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, RESTORE_2, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, SET_AND_SAVE_1, FALSE )
//DIAG_SET_FILTER( GPLC_COMM, AMR, SET_AND_SAVE_2, FALSE )

// Dual-RAT related
//==================
// HO & Reselections
//DIAG_SET_FILTER( GPLC_DUAL, PROCESS_HO_COMMAND, TimingAdvance, TRUE )
// De-activation
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, L1FrDedTaShutdown, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, l1frnull, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, L1FrDedicated2, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, L1FrDedicated3, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, L1FrDedicated4, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, L1FrDedicated5, TRUE )
//DIAG_SET_FILTER( GPLC_DUAL, DEACTIVATE, SqCfgSfsAudRunEncoder, TRUE )
// WB-measurements

//Platform


//Idle	=======
//============

// Required:
//===========

// General
//DIAG_SET_FILTER(GPLC_COMM,IDLE,ProcessIdleModeEvents, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelSeq0, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchSeq0, FALSE)

// TCB triggering sequencer
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaTxRach, TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaRxPchNorm, TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaRxPchExt,TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaRxBcch, TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaRxCbch,TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, L1FrIdleTaNcellBcch,TRUE)
DIAG_SET_FILTER(GPLC_COMM, IDLE, SqFbsStartIdleScellBcch, TRUE )

// Single Slot and Partial Decode
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessIdleNbResultsInFunc, TRUE) 
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessSingleSlotNbResultsASSERT,TRUE) 

DIAG_SET_FILTER(GPLC_COMM, DRX, DISABLE_DRX_SingleSlot_DECODE,TRUE)
DIAG_SET_FILTER(GPLC_COMM, DRX, ENABLE_DRX_SingleSlot_DECODE,TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessSingleSlotNbResultsGotSingleSlotMatch,TRUE) 
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessSingleSlotNbResultsDEBUG,FALSE) 
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessNbResults, FALSE) 
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessSingleSlotNbResults,FALSE) 

DIAG_SET_FILTER(GPLC_COMM,DRX,DISABLE_FORCE_DRX_PARTIAL_DECODE,TRUE)
DIAG_SET_FILTER(GPLC_COMM,DRX,ENABLE_FORCE_DRX_PARTIAL_DECODE,TRUE)

#if defined (L1_PARTIAL_DECODING)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessPartialDecodingNbResultsASSERT,TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessPartialDecodingNbResultsGotMatchOn2,TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessPartialDecodingNbResultsGotMatchOn3,TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,IdentifySingleSlotMatch, TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,IdentifyPartialDecodeMatch, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq0, TRUE)
DIAG_SET_FILTER(GPLC_COMM, DRX, DISABLE_DRX_Partial_Decode,TRUE)
DIAG_SET_FILTER(GPLC_COMM, DRX, ENABLE_DRX_Partial_Decode,TRUE)
DIAG_SET_FILTER(GPLC_COMM, DRX, VerifyPartialDecodeEnable,TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq0, TRUE)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessSingleSlotNbResultsASSERT2,TRUE)	
DIAG_SET_FILTER(GPLC_COMM,RX,FillSequenceEqTask4WithAverage1to3Data,FALSE)
DIAG_SET_FILTER(GPLC_COMM,RX,ProcessPartialDecodingNbResults,FALSE) 
#else // L1_PARTIAL_DECODING
DIAG_SET_FILTER(GPLC_COMM, DRX, L1FrIdleTaRxPchNormDisableSingleSlot, TRUE)
#endif // L1_PARTIAL_DECODING

//sequencers finish and abort
DIAG_SET_FILTER(GPLC_COMM, RX, AbortRxSequencer, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeqAbort, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeqAbort, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq10, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq10, TRUE)
DIAG_SET_FILTER(GPLC_COMM, RX, DestroyDrxPartialDecode2, TRUE)

// GPLC statistics
#if defined (GPLC_STATISTICS)
DIAG_SET_FILTER( GPLC,STATS,GPLCStatisticsReset, TRUE )
DIAG_SET_FILTER( GPLC,STATS,GPLCStatisticsPrintBasicStats, TRUE )
DIAG_SET_FILTER( GPLC,STATS,GPLCStatisticsPrintPowerStats, TRUE )
DIAG_SET_FILTER( GPLC,STATS,GPLCStatisticsPrintAtReset, TRUE )
DIAG_SET_FILTER( GPLC,STATS,GPLCStatisticsPrintPowerStatsAtReset, TRUE )
#endif //(GPLC_STATISTICS)

// Ncell monitoring properties
DIAG_SET_FILTER(GPLC_COMM,DRX,L1FrIdleUpdatePchMonsProperties1,TRUE)
DIAG_SET_FILTER(GPLC_COMM,DRX,L1FrIdleSetNumOfMonForThisDrxCycle2,TRUE)
DIAG_SET_FILTER(GPLC_COMM,DRX,L1FrIdleSetNumOfMonForThisDrxCycle3,FALSE)

// extracted when TT filter at level 5:
DIAG_SET_FILTER( GPLC_COMM, DRX_CL, FUNC_DL_SBLOCK, TRUE )
DIAG_SET_FILTER( GPLC_COMM, DRX_CL, FUNC_PBCCH_RES, TRUE )
DIAG_SET_FILTER( GPLC_COMM, DRX_CL, FUNC_NORMPPCH_RES, TRUE )
DIAG_SET_FILTER( GPLC_COMM, DRX_CL, FUNC_EXT_PPCH, TRUE )

// disabled until required for debug
//=============================

DIAG_SET_FILTER(GPLC_COMM, SYNC, UPDATE_TRIGGER_COUNTERS,FALSE)
DIAG_SET_FILTER(GPLC_COMM,DRX,L1FrIdleSetNumOfMonForThisDrxCycle1,FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, SetupNbTdsSetStart, FALSE)

DIAG_SET_FILTER(GPLC_COMM, RX, SetupSingleSlotFields, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, SetupPartialDecodingFields, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, DisableSaicOnPartialDecode, FALSE)

#if defined (PARTIAL_DECODE_ON_CBCH_AND_NcBCCH)		// omoravch - Partial Decode not implemented yet for CBCH and NcBCCH
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeqAbort, TRUE)	// we'll want this one open once enabled
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq10, TRUE)		// we'll want this one open once enabled
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq0, TRUE)

DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq2, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq3, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq4, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchPartialDecodeSeq11, FALSE)
#endif //(PARTIAL_DECODE_ON_CBCH_AND_NcBCCH)

DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchSeq1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchSeq2, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchSeq3, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxHoppingCchSeq4, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelSeq1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelSeq2, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelSeq3, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelSeq4, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq2, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq3, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq4, FALSE)

// activity matrix cleanup
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq15, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1RxControlChannelPartialDecodeSeq16, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq11, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq12, FALSE)

DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq2, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq3, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq4, FALSE)

//DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq13, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq5, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, L1DRxPchPartialDecodeSeq6, FALSE)

DIAG_SET_FILTER(GPLC_COMM, RX, DestroyDrxPartialDecode1, FALSE)
DIAG_SET_FILTER(GPLC_COMM, RX, CreateIdleCchRxSequencer1, FALSE)
DIAG_SET_FILTER(GPLC_COMM,GSM_MEAS_IN_WB,IDLE_SCELL_MEAS_IND,FALSE)


DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine1, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine2, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine3, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine4, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine5, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine6, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine7, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine8, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine9, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine10, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine11, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine12, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine13, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine14, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine15, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine16, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine17, FALSE)
DIAG_SET_FILTER(GPLC_PLAT,SLOWCLOCK,L1FrSlowClockStateMachine18, FALSE)

//DIAG_SET_FILTER(GPLC_PLAT, TCB, EnableFIShift, FALSE);

/* disable 2G PTM log */ 
DIAG_SET_FILTER(GPLC, TX_MULTI_SLOT, NEW_TX_SCHED, FALSE)
DIAG_SET_FILTER (GPLC_COMM, PTM, TX_PAYLOAD_CS_USED, FALSE)

/* disable 2G PTM log */ 
 

DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1FrScheduleIdleMonitor1,FALSE);
DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1FrScheduleIdleMonitor2,FALSE);
//DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1FrScheduleIdleMonitor3,FALSE);
DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1FrScheduleIdleMonitor4,FALSE);
//DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1FrSendNcelllIdleMonitorReport,FALSE);

DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, DeleteMonitorSequencerPartialPaging,FALSE);
DIAG_SET_FILTER(GPLC, NCELL_MEAS_DEBUG, L1SqMonitorPartialPaging,FALSE);


#if defined (TAVOR_DVFM_GSM_L1_SUPPORT)
//DVFM
DIAG_SET_FILTER(GPLC,DVFM,GsmDVFMInit,FALSE)
DIAG_SET_FILTER(GPLC,DVFM,GsmDVFMModeChangeReq,FALSE)
DIAG_SET_FILTER(GPLC,DVFM,GsmDVFMctrlDvfmToGsmAckCallbackFunc,FALSE)
//DIAG_SET_FILTER(GPLC,DVFM,GsmDVFMVerifyStateChangeApproved,FALSE)
//DIAG_SET_FILTER(GPLC,DVFM,GsmDVFMRequestPPforState,FALSE)
#endif // TAVOR_DVFM_GSM_L1_SUPPORT


#endif // _L1_GPLC_DEFAULT_DIAG_FILTER_H_

