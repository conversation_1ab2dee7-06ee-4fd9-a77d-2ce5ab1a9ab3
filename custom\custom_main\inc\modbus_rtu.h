/*
 * @FilePath: \ml307_dcln\custom\custom_main\inc\modbus_rtu.h
 * @Description: 
 * @Author: ji<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-17 13:09:34
 * @LastEditors: jiaw<PERSON>jie <EMAIL>
 * @LastEditTime: 2023-08-15 10:34:39
 * 
 * Copyright (c) 2023 by Zhejiang Tengen Electric Co., Ltd., All Rights Reserved. 
 */

#ifndef MODBUS_RTU_H
#define MODBUS_RTU_H
#include <stdint.h>

int modbus_rtu_read_registers(int cm_uart_id, uint8_t slave, uint16_t reg_addr, uint16_t nb, uint16_t *dest,uint8_t netByteOrder);
// int modbus_rtu_write_register(int cm_uart_id, uint8_t slave, uint16_t reg_addr, const uint16_t value);
int get_errno(void);
int get_crcerrorcount(void);


#endif // MODBUS_RTU_H