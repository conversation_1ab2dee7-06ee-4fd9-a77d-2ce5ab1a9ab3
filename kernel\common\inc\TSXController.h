#ifndef TSX_CONTROLLER_H
#define TSX_CONTROLLER_H

#include "WS_IPCComm.h"
#include "IPCComm.h"
#include "WS_IPCCommConfig.h"

#include <stdlib.h>
#include <string.h>
#include <stdio.h>


#include "oss.h"
#include "diags.h"
#include "Csw_mem.h"

//#include "pl_am_err_handler.h"
//#include "pl_econst.h"

#ifdef PHS_SW_DEMO_TTC
#include "nvm_header.h"
#include "crossPlatformSW.h"

#endif


#if 0

#ifndef UINT32
#define UINT32 unsigned int
#endif // UINT32

#ifndef INT32
#define INT32 signed int
#endif // INT32


#ifndef UINT16
#define UINT16 unsigned short
#endif // UINT16

#ifndef INT16
#define INT16 signed short
#endif // INT16

#ifndef UINT8
#define UINT8 unsigned char
#endif
#endif

#define TRUE  1
#define FALSE 0


#ifndef NULL
    #define NULL    0
#endif 

#define TSX_TC_CTRL_NVM_FILE_NAME                "TSX_TC_CTRL.nvm"
#define TSX_TC_CTRL_NVM_CUSTOMER_FILE_NAME        "TSX_TC_CTRL_Customer.nvm"
#define TSX_TC_CTRL_NVM_VER                 "00.00"  


//IPC MSG
#define TSX_CTRL_GNSS_IPC_REPORT (IPC_COMPOSE_HEADER(IPC_SETID_TSX, 0)) //0x50

//IPC CMD
#define TSX_CTRL_GNSS_IPC_CMD (IPC_COMPOSE_HEADER(IPC_SETID_TSX, 0)) //0x50


#define TSX_MIN_TEMPERATURE         (-40)
#define TSX_MAX_TEMPERATURE         (120)
#define TSX_FREQ_OFF_INF_LENGTH 161 //-40~120 centigrade
#if 0 //Default Coef
#define DEFAULT_POLY_COEF {0,167398839, -2714281, -144490, 1549}
#if 1
#define DEFAULT_F2C_TABLE {{-387553690,-326820168,-288064799,-202165453,-25669140,116098335,227834593,283870495,318263788,352321536},\
    {32000*2,28000*2,2*26000,23000*2,18000*2,14000*2,10000*2,7000*2,4000*2,0}}
#else
#define DEFAULT_F2C_TABLE {{-387553690,-326820168,-288064799,-202165453,-25669140,116098335,227834593,283870495,318263788,352321536},\
    {65000,65000,65000,65000,65000,65000,65000,65000,65000,65000}}

#endif

#define DEFAULT_V2T_TABLE {\
    64332,64237,64134,64025,63909,63785,63653,63513,63364,63205,63037,62859,62671,62471,62260,62037,61802,61554,61294,61019,\
    60730,60428,60110,59777,59428,59064,58683,58286,57873,57442,56994,56529,56046,55547,55029,54495,53943,53374,52788,52186,\
    51567,50932,50282,49617,48937,48244,47537,46819,46088,45347,44595,43835,43066,42290,41507,40720,39927,39132,38334,37535,\
    36736,35937,35140,34346,33555,32768,31987,31211,30443,29682,28930,28186,27453,26729,26016,25314,24624,23946,23280,22627,\
    21986,21359,20745,20145,19558,18984,18424,17878,17345,16826,16320,15827,15348,14881,14428,13987,13558,13142,12738,12346,\
    11965,11596,11238,10891,10554,10228,9912,9606,9309,9022,8744,8474,8214,7961,7717,7480,7252,7030,6816,6609,6408,6214,6027,\
    5845,5670,5500,5335,5175,5020,4870,4724,4582,4445,4312,4183,4057,3936,3818,3703,3592,3485,3380,3279,3181,3086,2993,2903,\
    2816,2732,2650,2571,2494,2419,2347,2276,2208,2142,2078,2015,1955,1897}
#else
//20220803
//#define DEFAULT_POLY_COEF {0,26614273, 1082335, -159005, 1678}
//20220809
#define DEFAULT_POLY_COEF {0,-485119579, -2053996, -146370, 1639}


//20220803 HL TSX
#define DEFAULT_F2C_TABLE {{-597746395,-466109777,-389321750,-250587079,-1509949,189492202,353392697,450184327,512130971,553428734},\
    {65000,56000,52000,46000,36000,28000,20000,14000,8000,0}}

//202201 HL TSX
#if 0
#define DEFAULT_V2T_TABLE {\
    63924,63847,63758,63657,63544,63420,63284,63135,62975,62804,62617,62444,62261,62055,61822,61570,61346,61110,60847,\
    60550,60229,59946,59647,59318,58950,58554,58198,57830,57423,56970,56501,56062,55620,55132,54597,54028,53528,53011,\
    52438,51816,51183,50583,49991,49350,48655,47927,47284,46631,45925,45160,44380,43678,42975,42220,41419,40608,39860,\
    39127,38350,37527,36705,35949,35211,34432,33614,32798,32061,31334,30573,29781,28990,28291,27598,26884,26144,25405,\
    24750,24100,23434,22751,22066,21469,20882,20275,19656,19039,18507,17978,17443,16895,16352,15877,15412,14943,14462,\
    13985,13570,13163,12750,12328,11924,11557,11206,10854,10496,10153,9845,9549,9250,8948,8657,8388,8128,7865,7599,7348,\
    7126,6916,6706,6494,6288,6096,5909,5724,5538,5358,5177,4999,4826,4656,4491,4329,4172,4018,3869,3724,3582,3445,3311,\
    3182,3057,2935,2818,2704,2595,2490,2388,2291,2198,2109,2023,1942,1865,1792,1722,1657,1596,1539,1486,1437,1391}
#else //202212, HL TSX
#define DEFAULT_V2T_TABLE {\
    4197753470,4190015026,4182392347,4174525351,4166093035,4157277014,4147932360,4138089612,4127704892,4116690923,4105006807,4092759955,\
    4079961162,4066297869,4051990532,4036943076,4021115007,4004567035,3987120353,3968960067,3949886733,3929909483,3908996961,3887172189,\
    3864482548,3840779735,3815914423,3790043322,3763334252,3735327321,3706811527,3676765771,3645908912,3613694098,3580309885,3541263106,\
    3505465550,3469110796,3431101026,3392315565,3353490714,3312246698,3270283927,3227572498,3184124576,3139511434,3094463910,3048280067,\
    3001674656,2953876333,2906036303,2856989000,2807794046,2758098668,2708040916,2657345810,2606566731,2554988023,2503768004,2452293689,\
    2401035072,2349514089,2298264453,2246565104,2195305637,2144221643,2092508623,2042784725,1993159995,1943565706,1894849030,1846290196,\
    1798483838,1751414671,1704632770,1658853438,1613487685,1569145319,1525495193,1482499686,1440479096,1399367898,1358610532,1319076370,\
    1280792091,1242844361,1205642256,1169593592,1134514480,1099888066,1066640409,1034010213,1002118020,971048622,941065351,911823482,\
    883451148,855548681,828620846,802633081,777489463,752902494,729105260,706085728,683673998,661931912,640934481,620546033,600861118,\
    581898668,563442146,545585004,528202248,511452111,495265576,479595128,464380058,449607709,435395973,421700746,408457623,395643925,\
    382943106,370491711,358853710,347650972,336765210,326319244,316229919,306426457,297019001,287891491,279007152,270489072,262222627,\
    254214224,246517204,239069626,231821395,224837883,218076812,211543891,205248227,199074410,193152173,187029129,181113441,175405108,\
    169904130,164610508,159524240,154645328,149973770,145509568,141252721,137203229,133361092,129726310,126298883,123078811,120066095}

#endif

#endif

#define DEFAULT_FREQ_OFFSET_DATA  {\
    {0,0,0,-3241},{0,0,0,-2290},{0,0,0,-1378},{0,0,0,-504},{0,0,0,332},{0,0,0,1131},{0,0,0,1893},{0,0,0,2619},{0,0,0,3310},\
    {0,0,0,3966},{0,0,0,4587},{0,0,0,5174},{0,0,0,5728},{0,0,0,6250},{0,0,0,6739},{0,0,0,7197},{0,0,0,7623},{0,0,0,8019},\
    {0,0,0,8385},{0,0,0,8722},{0,0,0,9030},{0,0,0,9309},{0,0,0,9561},{0,0,0,9785},{0,0,0,9983},{0,0,0,10155},{0,0,0,10301},\
    {0,0,0,10423},{0,0,0,10519},{0,0,0,10592},{0,0,0,10642},{0,0,0,10669},{0,0,0,10674},{0,0,0,10657},{0,0,0,10618},{0,0,0,10560},\
    {0,0,0,10481},{0,0,0,10383},{0,0,0,10266},{0,0,0,10131},{0,0,0,9978},{0,0,0,9807},{0,0,0,9620},{0,0,0,9417},{0,0,0,9199},\
    {0,0,0,8965},{0,0,0,8717},{0,0,0,8455},{0,0,0,8180},{0,0,0,7891},{0,0,0,7591},{0,0,0,7279},{0,0,0,6956},{0,0,0,6622},\
    {0,0,0,6278},{0,0,0,5925},{0,0,0,5563},{0,0,0,5192},{0,0,0,4814},{0,0,0,4428},{0,0,0,4036},{0,0,0,3638},{0,0,0,3234},\
    {0,0,0,2824},{0,0,0,2411},{0,0,0,1993},{0,0,0,1573},{0,0,0,1149},{0,0,0,723},{0,0,0,295},{0,0,0,-133},{0,0,0,-563},\
    {0,0,0,-992},{0,0,0,-1421},{0,0,0,-1849},{0,0,0,-2275},{0,0,0,-2699},{0,0,0,-3121},{0,0,0,-3539},{0,0,0,-3953},{0,0,0,-4363},\
    {0,0,0,-4768},{0,0,0,-5167},{0,0,0,-5561},{0,0,0,-5947},{0,0,0,-6327},{0,0,0,-6699},{0,0,0,-7063},{0,0,0,-7417},{0,0,0,-7763},\
    {0,0,0,-8098},{0,0,0,-8423},{0,0,0,-8737},{0,0,0,-9040},{0,0,0,-9330},{0,0,0,-9608},{0,0,0,-9872},{0,0,0,-10123},\
    {0,0,0,-10359},{0,0,0,-10580},{0,0,0,-10786},{0,0,0,-10976},{0,0,0,-11149},{0,0,0,-11305},{0,0,0,-11443},{0,0,0,-11563},\
    {0,0,0,-11665},{0,0,0,-11747},{0,0,0,-11809},{0,0,0,-11851},{0,0,0,-11871},{0,0,0,-11870},{0,0,0,-11847},{0,0,0,-11802},\
    {0,0,0,-11733},{0,0,0,-11640},{0,0,0,-11523},{0,0,0,-11381},{0,0,0,-11214},{0,0,0,-11020},{0,0,0,-10800},{0,0,0,-10553},\
    {0,0,0,-10279},{0,0,0,-9976},{0,0,0,-9644},{0,0,0,-9283},{0,0,0,-8892},{0,0,0,-8471},{0,0,0,-8019},{0,0,0,-7535},{0,0,0,-7019},\
    {0,0,0,-6471},{0,0,0,-5889},{0,0,0,-5273},{0,0,0,-4624},{0,0,0,-3939},{0,0,0,-3219},{0,0,0,-2463},{0,0,0,-1670},{0,0,0,-841},\
    {0,0,0,27},{0,0,0,932},{0,0,0,1876},{0,0,0,2860},{0,0,0,3883},{0,0,0,4947},{0,0,0,6051},{0,0,0,7198},{0,0,0,8386},{0,0,0,9617},\
    {0,0,0,10891},{0,0,0,12209},{0,0,0,13571},{0,0,0,14978},{0,0,0,16430},{0,0,0,17928},{0,0,0,19473},{0,0,0,21065},{0,0,0,22704},{0,0,0,24391},{0,0,0,26128}}



/**************************************************************/
// HW  base address definition
/**************************************************************/
#define BB_MODEM_REG_BASE    ((UINT32)0x00000000UL)

#define TC_CTRL_REG_BASE            (BB_MODEM_REG_BASE + 0xD4020000)

#define APBAUX_TSX_CLK_GATE            (0xD401509C) //Clock enable, release the reset of tsx_controller
#define M1_APB_SPARE5_REG              (0xD4090110)


/*******************************************************************************
TC Controller Register Addr
*******************************************************************************/

#define TC_CTRL_EN_REG                   (TC_CTRL_REG_BASE+0x0000)
#define TC_CTRL_CFG_REG                  (TC_CTRL_REG_BASE+0x0004)                
                                      
#define TC_CTRL_COEF0_REG                (TC_CTRL_REG_BASE+0x0008)
#define TC_CTRL_COEF1_REG                (TC_CTRL_REG_BASE+0x000C)
#define TC_CTRL_COEF2_REG                (TC_CTRL_REG_BASE+0x0010)
#define TC_CTRL_COEF3_REG                (TC_CTRL_REG_BASE+0x0014)
#define TC_CTRL_COEF4_REG                (TC_CTRL_REG_BASE+0x0018)

#define TC_CTRL_VTLUT0_REG               (TC_CTRL_REG_BASE+0x001C)
#define TC_CTRL_VTLUT160_REG             (TC_CTRL_REG_BASE+0x029C)

//Output
#define TC_CTRL_VTO_REG                  (TC_CTRL_REG_BASE+0x02A0)
#define TC_CTRL_POLYT_REG                (TC_CTRL_REG_BASE+0x02A4)
#define TC_CTRL_POLYF_REG                (TC_CTRL_REG_BASE+0x02A8)
#define TC_CTRL_ADCCDDE_REG              (TC_CTRL_REG_BASE+0x02AC)

//Aux ADC Setting
#define TC_CTRL_AUXADC_CFG_REG           (TC_CTRL_REG_BASE+0x02F8)


#ifdef REG32
#undef REG32
#endif
#define REG32(ADDR)      *(volatile unsigned int*)(ADDR)

#define HW_REG_WRITE_32(_pHW, val)   (REG32((UINT32)(_pHW)) = ((UINT32)(val)))
#define HW_REG_READ_32(_pHW)         ( REG32((UINT32)(_pHW)))


/*******************************************************************************
TC Controller  definition
*******************************************************************************/
//TC_CTRL_EN_REG
#define TC_CTRL_EN_BIT_OFFSET 0
#define TC_CTRL_EN_REAL_BIT_OFFSET 1

//TC_CTRL_CFG_REG
#define TC_CTRL_CFG_ADC_MD_BIT_OFFSET 0
#define TC_CTRL_CFG_MODE_BIT_OFFSET 1
#define TC_CTRL_CFG_ADCOFFSET_BIT_OFFSET 8
#define TC_CTRL_CFG_VTILEN_BIT_OFFSET 16

//TC_CTRL_COEF0_REG
#define TC_CTRL_COEF0_DS_BIT_OFFSET 0
#define TC_CTRL_COEF0_ALPHA_BIT_OFFSET 4
#define TC_CTRL_COEF0_MARGIN_BIT_OFFSET 8
#define TC_CTRL_COEF0_T0_BIT_OFFSET 16

enum
{
    TSX_TC_INIT_STAGE = 0,
    TSX_TC_CALI_STAGE = 1,
    TSX_TC_POYLYFIT_STAGE_1 = 2, //First range fit
    TSX_TC_POYLYFIT_STAGE_2 = 3, //Second range fit
    TSX_TC_POYLYFIT_STAGE_3 = 4, //Third range fit
    TSX_TC_POYLYFIT_STAGE_4 = 5, 
    TSX_TC_POYLYFIT_STAGE_5 = 6, 
};

enum
{
    TF_SCORE_RANGE0 = 800+800,
    TF_SCORE_RANGE1 = 1600+800,
    TF_SCORE_RANGE2 = 3200+800,
    TF_SCORE_RANGE3 = 4000+800,
    TF_SCORE_RANGE4 = 4800+800,
    TF_SCORE_RANGE5 = 5600+800,
    TF_SCORE_RANGE6 = 6400+800,
    TF_SCORE_RANGE7 = 7200+800,
    TF_SCORE_RANGE8 = 8000+800,
};


enum
{
    SYS_START = 0,
    SYS_STOP = 1,
    POLY_FIT = 2,
    POLY_SET = 3,
    UPDATE_LUT = 4,    
};

enum
{
    TSX_TC_TASK_BIT = 1<<0,
    TSX_NVM_TASK_BIT = 1<<1,
    TSX_TASK_MASK = 0xFF,
};


typedef signed long long            INT64;


//ICAT EXPORTED STRUCT
typedef struct
{
    INT32 DeltaF[10]; //Q24, unit in PPM, Ascending order
    UINT16 DacCode[10];
}F2C_LUT;

//Polynomial Interface
//ICAT EXPORTED STRUCT
typedef struct
{
    INT16 T0;//Q8
    INT32 C0;//Q24
    INT32 C1;//Q24
    INT32 C2;//Q24
    INT32 C3;//Q24
}STRUCT_3RD_ORDER_POLY_PARAM;

//Temp data 
//ICAT EXPORTED STRUCT
typedef struct
{
    //INT16 T;
    UINT8 ReliableInd;//0-unreliable, 1-reliable by cellular, 2-reliable by GNSS
    UINT8  Month;
    UINT16 Year;
    INT32 FreqOff;//ppb
}STRUCT_FREQOFFSET_INF;

//ICAT EXPORTED STRUCT
typedef struct
{
    //INT16 T;
    UINT8 Stage;//
    UINT8  Month;
    UINT16 Year;
}STRUCT_POLY_STATE;

//ICAT EXPORTED STRUCT
typedef struct
{
    UINT8 Valid; //1-DB valid, otherwise invalid
    UINT8 Aligned; //1- PolyCoef is same with HW, otherwise not.
    UINT16 Coef0;
    UINT32 TCCFG;
    STRUCT_3RD_ORDER_POLY_PARAM PolyCoef;
    STRUCT_FREQOFFSET_INF FreqOffInf[TSX_FREQ_OFF_INF_LENGTH];
    STRUCT_POLY_STATE PolyState;
    UINT32 V2TTable[TSX_FREQ_OFF_INF_LENGTH]; //Index is Temp+40, Value is VTOut Descending order
    UINT16 Reserved2;
}STRUCT_TSXCTRL_DB;

enum
{
    TSX_RCV_DB_CFM,
    TSX_DB_REQ,
    TSX_TF_REPORT
};


//void TSXCtrlPolyFitting(INT64 coef[], INT16 inputX[],INT16 inputY[],INT16 length);
INT8 TSXCtrlPolyFitting(INT32 coef[], INT16 inputX[],INT16 inputY[],INT16 length);
INT32 TSXCtrlFreqOffsetCalc(INT16 Temp);
unsigned char TSXCtrl_WriteNVMFile(STRUCT_TSXCTRL_DB *pTsxTcCtrlData);    
unsigned char TSXCtrl_ReadNVMFile(STRUCT_TSXCTRL_DB *pTsxTcCtrlData);
UINT8 TSXTCCtrlNvmFileManage(void);
void TSXCtrlSendFreqOffsetInfo(UINT16 OpCode);
void TSXCtrlUpdateDB(UINT8 Type, INT16 Temp, INT32 FreqOffset);
void TSXCtrlManger(UINT8 Src);

void TSXCali_ReadNVMFile(void);
void TSXAuxADCEn(UINT8 En);

extern STRUCT_TSXCTRL_DB g_str_TSXCtrlDB;
#endif
