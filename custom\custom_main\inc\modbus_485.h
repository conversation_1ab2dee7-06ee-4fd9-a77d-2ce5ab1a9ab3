/*
 * Copyright (C) 2019-2030 tengen Group Holding Limited
 *@modbus_gw.h: tengen smart gateway modbus protocol define
 *@function:
 *      1.read para from breaker;
 *      2.read rtdata from breaker;
 *      3.remote control breaker;
 *      4.auto dispatch breaker address;
 *      5.breaker communicating status manager;
 */
#ifndef __MODBUS_485_H__
#define __MODBUS_485_H__

#include "topic_manage.h"

#define LOG_485 1

typedef struct {
   uint32_t regCount;               //需要读取的寄存器个数
    uint8_t reg[2];                 //需要读取的寄存器首地址
    uint8_t regval[MAX_REGNUM];     //需要读取的寄存器值
    uint8_t addr;                   //微断表地址
}S_HOLD_PARA;

typedef enum{
    MB_READ_HOLD_REG_VAL,       //读保持寄存器
    MB_READ_SIN_COIL_STATUS,    //读单个线圈
    MB_WRITE_SIN_COIL_STATUS,    //写单个线圈，控制分合闸
    MB_WRITE_SIN_FLE_REG_VAL,    //写单个寄存器
    MB_WRITE_REG_VAL,            //写多个寄存器
    MB_ACK_ERR,                    //
    MB_IDLE,                    //
    MB_SET_ADDR_OK,                //设置微断首地址成功
    MB_READ_ADDR,                //读微断首地址
    MB_SET_BAUD_OK				//设置网关485口波特率
}SubModbusStatus;

typedef struct{
    int32_t byteCnt;//objval count
    uint8_t regdata[MAX_REGNUM];//breakaddr
}S_ZF_OBJ_DATA;
//data define(0x20-0x44)
#define YC_BASE 0x0020
#define YC_MAX 0x0044
typedef enum
{
	MB_OBJMAP_BKSTATE = 0,
	MB_OBJMAP_LEAKI,
	MB_OBJMAP_ALARM1_HIGH,
	MB_OBJMAP_ALARM1_LOW,
	MB_OBJMAP_IA,
	MB_OBJMAP_TEMPA,
	MB_OBJMAP_UA,
	MB_OBJMAP_PA,
	MB_OBJMAP_KWHA_HIGH,
	MB_OBJMAP_KWHA_LOW,
	MB_OBJMAP_COSA,
	MB_OBJMAP_IN,//
	MB_OBJMAP_BK1,
	MB_OBJMAP_TEMPN,
	MB_OBJMAP_BK2,
	MB_OBJMAP_BK3,
	MB_OBJMAP_IB,
	MB_OBJMAP_TEMPB,
	MB_OBJMAP_UB,
	MB_OBJMAP_PB,
	MB_OBJMAP_KWHB_HIGH,
	MB_OBJMAP_KWHB_LOW,
	MB_OBJMAP_COSB,
	MB_OBJMAP_COSC,//
	MB_OBJMAP_IC,
	MB_OBJMAP_TEMPC,
	MB_OBJMAP_UC,
	MB_OBJMAP_PC,
	MB_OBJMAP_KWHC_HIGH,
	MB_OBJMAP_KWHC_LOW,
	MB_OBJMAP_KWHSUM_HIGH,
	MB_OBJMAP_KWHSUM_LOW,
    MB_OBJMAP_COSSUM,//
	MB_OBJMAP_HZ,
	MB_OBJMAP_POWER_DIREC,
	MB_OBJMAP_ALARM2_HIGH,
	MB_OBJMAP_ALARM2_LOW,
} E_BK_DATATYPE;
//paradefine
//YT(0x07-0x13)
//addr(0x16)
//YK(0x18-0x1F) 
//threadholds-2(0x64-0x68)
//new function1 for sifang(0x0100-0x010F)
//new function2 for sifang(0x0200-0x0210)
#define PARA_BASE       0x0000
#define PARA_MAX        0x0210
typedef enum
{
	MB_OBJMAP_HIGH_U = 7,
	MB_OBJMAP_LOW_U,
	MB_OBJMAP_ADDR = 22,
	MB_OBJMAP_HIGH_TEMP=24,
	MB_OBJMAP_RATED_I,
	MB_OBJMAP_LEAK_TEST,
	MB_OBJMAP_ONLINE_OTA,
	MB_OBJMAP_PROTECT_EN,
	MB_OBJMAP_I_LIMITS,
	MB_OBJMAP_POWER_LIMITS,
	MB_OBJMAP_T_HIGH_U=100,
	MB_OBJMAP_T_LOW_U,
	MB_OBJMAP_T_IA,
	MB_OBJMAP_T_IB,
	MB_OBJMAP_T_IC,
	MB_OBJMAP_T_IN,
	MB_OBJMAP_T_LEAKI,
	MB_OBJMAP_T_TEMP,
	MB_OBJMAP_INTVTIME1,
	MB_OBJMAP_INTVTIME2,
	MB_OBJMAP_INTVTIME3,
	MB_OBJMAP_INTVTIME4,
	MB_OBJMAP_INTVTIME5,
	MB_OBJMAP_T_PWA,
	MB_OBJMAP_T_PWB,
	MB_OBJMAP_T_PWC,
	MB_OBJMAP_C_IA=256,
	MB_OBJMAP_C_IB,
	MB_OBJMAP_C_IC,
	MB_OBJMAP_C_POWERA,
	MB_OBJMAP_C_POWERB,
	MB_OBJMAP_C_POWERC,
	MB_OBJMAP_C_HIGH_UA,
	MB_OBJMAP_C_HIGH_UB,
	MB_OBJMAP_C_HIGH_UC,
	MB_OBJMAP_C_LOW_UA,
	MB_OBJMAP_C_LOW_UB,
	MB_OBJMAP_C_LOW_UC,
	MB_OBJMAP_C_TEMPA,
	MB_OBJMAP_C_TEMPB,
	MB_OBJMAP_C_TEMPC,
	MB_OBJMAP_C_TEMPN,
	MB_OBJMAP_DEV_TYPE=512,
} E_BK_PARATYPE;


typedef enum{
	MB_OBJMAP_DEVICETYPE = 0,
	MB_OBJMAP_DEVICEID_HIGH,
	MB_OBJMAP_DEVICEID_LOW,
	MB_OBJMAP_MANUFACTURER_HIGH,
	MB_OBJMAP_MANUFACTURER_LOW,
	MB_OBJMAP_PRODUCTIONDATA_HIGH,
	MB_OBJMAP_PRODUCTIONDATA_LOW
}E_BK_INFO;

//info define not use for sifang
#define MAX_SEND_BUF 220
#define FIX_RCVL_LEN 6
//基址
#define REG_BASE 0x0000 //0x03基址(0x20-0x44)
#define REG_MAX  0x0210 //0x03基址(0x20-0x44)
//#define YT_BASE 0x0018 //0x06基址(0x18-0x1F)
//#define YK_BASE 0x0017 //0x06基址(0x17)
//命令码
#define CMDCODE_READ_COIL_STA           0x01 //读单个线圈(单用于读取分合闸状态)
#define CMDCODE_READ_INPUT_STA          0x02 //读离散量输入
#define CMDCODE_READ_HOLD_REG_VAL       0x03 //读保持寄存器
#define CMDCODE_READ_INPUT_REG_VAL      0x04 //读输入寄存器
#define CMDCODE_WRITE_COIL_STA          0x05 //写单个线圈
#define CMDCODE_WRITE_SINGLE_REG_VAL    0x06 //写单个寄存器
#define CMDCODE_WRITE_REG_VAL           0x10 //写多个寄存器
//错误码
#define ERR_NULL                        0x00//无错误
#define ERR01_INVALID_FUNC              0x01//非法功能
#define ERR02_INVALID_ADDR              0x02//非法数据地址
#define ERR03_INVALID_VAL               0x03//非法数据值
#define ERR04_FAULT_DEV                 0x04//从站设备故障
#define ERR05_INVALID_ACK               0x05//确认，防止客户机等待超时
#define ERR06_SLAVE_BUSY                0x06//从设备忙
#define ERR08_ODD_EVEN                  0x08//存储奇偶性差错
#define ERR0A_GW_CFG                    0x0A//网关路径不可用
#define ERR0B_SUBDEV_FAILACK            0x0B//网关目标设备响应失败

int readComdata_485(uint8_t *data);
int search_head_485(uint8_t *buf,int len);
void analyze_data_485(uint8_t *data,const int len);
bool GetObjBuf(const S_HOLD_PARA para, S_ZF_OBJ_DATA *data);
int modbus_tx_485(PROTO_PARA_TX* tx);
int modbus_rx_485(PROTO_PARA_RX* rx);
void ProcWriteReg(void);
void ProcWriteCoil(void);
void modbus_loop_485(void *paras);
uint32_t get_baud_rate_485(void);
#endif