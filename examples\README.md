# example用例介绍 简介

## **用例支持情况**

|                     APP\模组型号                       | ML307A-DCLN | ML307A-GCLN | ML307A-DSLN | ML307A-DSLN（科大讯飞TTS版） | ML307A-GSLN | ML307R-DC | ML307C-DC-CN |
| :----------------------------------------------------: | :---------: | :---------: | :---------: | :--------------------------: | :---------: | :-------: | :----------: |
|             [aliyun](./aliyun/README.md)               |      √      |      √      |      √      |              √               |      √      |     √     |      √       |
|            [asocket](./asocket/README.md)              |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|          [breathled](./breathled/README.md)            |      √      |      √      |      ×      |              ×               |      ×      |     √     |      √       |
|                       camera                           |      ×      |      ×      |      √      |              √               |      √      |     ×     |      ×       |
|            [ctwing](./ctwing/README.md)                |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|              [dmp](./dmp/README.md)                    |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|         [filesystem](./filesystem/README.md)           |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|          [i2ceeprom](./i2ceeprom/README.md)            |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|            [keypad](./keypad/README.md)                |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|              [lcd](./lcd/README.md)                    |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|        [mqtt_onenet](./mqtt_onenet/README.md)          |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|        [mqtt_unicom](./mqtt_unicom/README.md)          |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|          [network](./network/README.md)                |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|            [onenet](./onenet/README.md)                |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|                [os](./os/README.md)                    |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [recorder](./recorder/README.md)               |      ×      |      ×      |      ×      |              √               |      ×      |     ×     |      ×       |
|         [spiflash](./spiflash/README.md)               |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|       [statemachine](./statemachine/README.md)         |      √      |      √      |      ×      |              ×               |      ×      |     √     |      √       |
| [timer_block_support](./timer_block_support/README.md) |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|               [usb](./usb/README.md)                   |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|        [audio_pwm](./audio_pwm/README.md)              |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|       [audio_es8311](./audio_es8311/README.md)         |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      ×       |
|         [gnss_td1030](./gnss_td1030/README.md)         |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [call](./call/README.md)                       |      ×      |      ×      |      √      |              √               |      √      |     x     |              |
|         [http](./http/README.md)                       |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [http_file](./http_file/README.md)             |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [oneos_lbs](./oneos_lbs/README.md)             |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [sms](./sms/README.md)                         |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|         [uart](./uart/README.md)                       |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |              |
|           [i2cM117B](./i2ceeprom/README.md)            |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
|         [ymodem_fota](./ymodem_fota/README.md)         |      ×      |      ×      |      ×      |              ×               |      ×      |     √     |      √       |
---
