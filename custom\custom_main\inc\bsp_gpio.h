/*********************************************************
*  @file    bsp_gpio.h
*  @brief   ML307 OpenCPU gpio file
*  Copyright (c) 2023 TENGEN.
*  All rights reserved.
*  created by Delei 2023/03/27
********************************************************/
#ifndef __BSP_GPIO_H__
#define __BSP_GPIO_H__

#define FIREALM_PIN     CM_IOMUX_PIN_54 //火灾告警输入
#define FIREALM_GPIO_ID 5


#define BTN_PIN         CM_IOMUX_PIN_16 //BTN输入
#define BTN_GPIO_ID     0


#define NET_LED_PIN     CM_IOMUX_PIN_50 //NET灯
#define NET_LED_GPIO_ID 3
// #define STA_LED_PIN     CM_IOMUX_PIN_51 //STA灯
// #define STA_LED_GPIO_ID 4

////一体
#define STA_LED_PIN     CM_IOMUX_PIN_50 //STA灯
#define STA_LED_GPIO_ID 3
#define POWR_EN_PIN     CM_IOMUX_PIN_57 //power en
#define POWR_EN_GPIO_ID 8


#define NET_LED_PEROD   5 //NET灯的半周期计数
#define STA_LED_PEROD   5 //STA灯的半周期计数
#define STA_LED_FST     1 //STA灯快闪模式下的半周期计数
#define STA_LED_FST_NUM 3 //STA灯快闪的次数

#define RTC_RST_PIN     CM_IOMUX_PIN_51 //H1302的RST引脚
#define RTC_RST_GPIO_ID 4
#define RTC_CLK_PIN     CM_IOMUX_PIN_54 //H1302的CLK引脚
#define RTC_CLK_GPIO_ID 5
#define RTC_DAT_PIN     CM_IOMUX_PIN_55 //H1302的DAT引脚
#define RTC_DAT_GPIO_ID 6

//由于一体化网关仅有一个小灯，所以定制了以下的闪灯模式
typedef enum {
    STA_S_L_S, //短-长-短，上电初始状态
    STA_L_L_L, //长-长-长，没有检测到三元组
    STA_L_L_S, //长-长-短，检测到三元组，但是初始化4G网络失败    
    STA_S_S_L, //短-短-长，初始化时还没有检测到网络，正在检测网络、SIM卡
    STA_S_S_S, //三次短闪，正常工作
    STA_L_S_S, //长-短-短，ModBusCRC校验错误，或者没有检查到微断
    STA_S_L_L, //短-长-长，已经连接上，但是目前由于网络等原因，断开了连接，正在尝试重新连接
    STA_L_S_L, //长-短-长，表示当前为测试模式或者透传模式
    STA_OFF,   //常灭(未使用)
    STA_ON,    //常亮(刚上电时使用)
    STA_MAX
}led_state_t;

void bsp_gpio_init(void);

#endif