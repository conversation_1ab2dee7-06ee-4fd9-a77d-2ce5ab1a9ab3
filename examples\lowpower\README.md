# LOWPOWER低功耗测试 简介 

## LOWPOWER低功耗介绍**
作为主机的嵌入式组件，模组消耗的功率会增加主机功耗。用户根据实际使用环境，合理设置模组功耗
模式，可有效降低模组平均功耗。但是功耗与性能是相辅相成的，因此需要开发者根据实际情况，让模组在
NORMAL和LP两种模式间灵活切换。

功耗模式：
▪ NORMAL：普通模式。
▪ LP：低功耗模式。

唤醒方式
临时唤醒：通过与基站通信，中断等操作实现
永久唤醒：通过持锁方式实现

进入休眠的条件
▪ 模组处于空闲状态。
▪ 模组当前无阻止休眠的其他条件，如未释放资源锁等。
▪ 模组未外接USB。

## **实现功能**
1. 初始化PM功能，也就是模组的低功耗功能，并注册好回调
2. 初始化1个GPIO，并利用该GPIO的中断功能，上升沿进入休眠，下降沿退出休眠
3. 为了测试更加准确，GPIO加入了消抖功能
4. 使用本示例，可通过GPIO的拉高和拉低操作，进行休眠和唤醒，并可通过电源仪直观查看休眠电流变化

## **APP执行流程**
1. 初始化GPIO，并开启一个线程，一个消息队列，一个定时器用于消抖。
2. 创建低功耗测试主线程及主函数，创建1个消息队列，用于等待GPIO中断的有效触发
3. GPIO默认为高电平，并默认解锁了休眠，将GPIO拉低，触发下降沿中断，触发中断回调，获取当前的电平，并发送消息至队列中，同时开启定时器
4. 当定时器到达时，即经历过消抖时间(本示例中是100ms)再次判断电平，当电平与开始时相同，则认为是有效触发，发送唤醒事件至主线程中，锁定睡眠。
5. 当定时器到达时，若电平与开始不相同，则认为是无效触发，不触发任何事件。
6. GPIO拉高，触发上升沿再次进入休眠，同理。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 问题：为什么模组不能进入休眠？
  回答：首先检查是否接入USB，特别是USB_DET管脚；其次模组解锁休眠后，会根据网络及自身情况自动进入休眠，因此调用解锁休眠接口后并不能立即进入休眠。还需注意是否在入网或者有数据业务交互。最后，程序不能有轮询类业务，即解锁休眠后，需用osDelay或者osWaitForever使模组程序整体进入空闲等待的状态，此时模组才有可能进入休眠。

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/11 18:08
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------