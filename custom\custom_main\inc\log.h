/*
 * @Author: <PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON>@tengen.com.cn
 * @Date: 2023-06-20 11:55:21
 * @LastEditors: jiaw<PERSON>jie <EMAIL>
 * @LastEditTime: 2023-08-17 16:18:13
 * @FilePath: \ml307_dcln - 副本 (2)\custom\custom_main\inc\log.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef LOG_H
#define LOG_H

#define _DEBUG
#define _INFO
#define _WARN

#ifdef _DEBUG
#define LOG_DEBUG(fromat,...)   cm_log_printf(0, "[line:%d] [func:%s] [debug]:"fromat"\n", __LINE__,__FUNCTION__,##__VA_ARGS__)
#else 
#define LOG_DEBUG(fromat,...)
#endif

#ifdef _WARN
#define LOG_WARN(fromat,...)   cm_log_printf(0, "[line:%d] [func:%s]  [warn]:"fromat"\n", __LINE__,__FUNCTION__,##__VA_ARGS__)
#else 
#define LOG_WARN(fromat,...)
#endif

#ifdef _INFO
#define LOG_INFO(fromat,...)   cm_log_printf(0, "[info]:"fromat"\n", ##__VA_ARGS__)
#else 
#define LOG_INFO(fromat,...)
#endif

#endif