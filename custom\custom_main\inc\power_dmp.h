/*
 * @Author: <PERSON><PERSON><PERSON> l<PERSON>jun<PERSON><PERSON>@tengen.com.cn
 * @Date: 2023-08-15 09:59:13
 * @LastEditors: <PERSON>ji<PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 10:07:48
 * @FilePath: \cogiot_485_im20\src\APP\power_dmp.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
 */
#ifndef POWER_DMP_H
#define POWER_DMP_H

#include <stdint.h>

#if defined __cplusplus
extern "C" {
#endif

#define FILE_DMP_CONCIG "/dmp_config.json"

void power_dmp_loop_check(void); 
int judge_power_outage_state(int modbus_runFlag, uint32_t Voltage_state, float PhV_phsV1, float PhV_phsV2, float PhV_phsV3);
void file_save(const char *filename, const char * buff_content);
void load_dmp_config(const char *filename);
#if defined __cplusplus
}
#endif

#endif /* POWER_DMP_H */
