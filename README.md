# 中移物联ML307-DCLN网关（科舸网关电源一体式）

本网关基于ML307配套的SDK配置，具体如下：<br/>
SDK版本： `ML305A_ML307A_OpenCPU_Standard_1.4.2.2023062518_release`<br/>
MQTT版本：`阿里LinkSDK`
## 1. 历史记录
| Name | Date | Func |
| :---: | :------: | :----- |
|Songqiming|20250603|1. 建立初版 v1.0.0。<br/>2. 修改readme.md文件。 |


## 2. 使用方法
### 2.1 安装环境
python 3.7及以上版本（OpenCPU SDK开发包，仅支持在Windows 7/Windows 10/11 X64环境下开发和编译。）
安装完python后，打开命令行执行pip install scons或执行pip3 install scons安装scons工具。


### 配置工程
打开Windows的CMD窗口，并进入到ML307C文件夹下，之后输入以下命令开始编译。
```
scons -c
scons .\
```


### 编译和烧写

编译工程之后烧写、监测串口2输出。

+ 烧写工具目前仅支持 aboot.exe
+ 串口0作为网关与微断的通信口，波特率为38400bps;串口1同时作为TypeC的产测功能与485的级联功能，波特率19200bps;串口2专门用作Debug口，波特率115200bps.

## 3. FOTA需要注意的点
+ 如果更改不大，不建议经常OTA升级，因为ML307的差分机制做的不好，即使改动非常小，生成的差分包也有700多KBytes，所以升级比较耗流量。
+ 固件包一旦发布，由于内部含有时间戳，而新的差分包是要依赖时间戳的。所以后面发行的新固件包必须依照发布的那一个固件包来生成差分包，且老的固件包不可以从新编译生成（时间戳会变化）。
制作升级包的指令如下：
```
adiff.exe  system_old.img  system_new.img  system_patch.bin -a1 user_app user_app.bin -m
```
解压或者直接打开旧的固件(一个压缩包)，找到system.img文件，命名为`system_old.img`。然后，解压或者直接打开新的固件(一个压缩包)，找到`system.img`文件和`user_app.bin`文件。最后将这些文件与adiff.exe工具放在一个目录里，然后在该目录打开cmd命令窗口，输入上述指令，记得得到一个`system_patch.bin`的差分包。


## 4. 工程Log输出

如果模组连接4G成功，UART2打印输出:
```
[17:48:30.487]收←◆[INFO: CPU       ] CHIP_ID: 0x1606, REV_ID: 0xA0
[INFO: CPU       ] PLATFORM: CRANEL (SILICON)
[INFO: Main      ] 
[INFO: Main      ] Starting AROM-CRANE (Version: 2021.10.16)
[INFO: ClkDrv    ] QSPI clock configured at 13 MHz
[INFO: QspiFlash ] QSPI clock configured at level 1
[INFO: QspiFlash ] Detecting QSPI flash devices...
[INFO: QspiFlash ] Found a known spi flash device "GD25LE32E"
[INFO: QspiFlash ] SPI nor flash: Manufacturer ID: 0xC8, Device ID: 0x6016
[INFO: UbiSimple ] Detected peb size is 4096, the first good peb number is 0
[INFO: Crypto    ] Initializing crypto library...
[INFO: Main      ] Running in normal booting mode.
[INFO: Main      ] Loading...
[INFO: Bl1Main   ] Welcome to boot rom.
[INFO: Bl1Main   ] ### Non-trusted boot mode. ###
[INFO: Bl1Main   ] Found preboot volume, size is 19072 bytes
[INFO: Bl1Main   ] BL1: Trying to load and verify BL2 image from volume preboot...
[INFO: BlCommon  ] Loading image from #0xd1f0d818 to #0x80 with size 31004 successfully.
[INFO: Bl1Main   ] BL1: OKay.
[INFO: Freq      ] Freq change done: CR5 416MHz -> 624MHz, AXI 156MHz -> 208MHz
[PRI : Preboot   ] Executing preboot application...
[PRI : Preboot   ] Preboot version: 2021.10.16
[PRI : PMIC      ] Found PMIC with Id: 0x18
[PRI : PMIC      ] [BP_0xe5] = 0x4
[PRI : PMIC      ] [BP_0xe6] = 0x0
[PRI : Preboot   ] pmic_get_restart_cmd=0x0, smux_running_mode_get=0
[PRI : Preboot   ] Power_up_reason=0x2.
[WARN: Preboot   ] Warning: power_up_reason unknown.
[PRI : Psram     ] [PSRAM] psram init Crane common.
[PRI : Psram     ] WB_4M
[PRI : EFuse     ] No embedded flash
[INFO: Bl1Main   ] Found bootloader volume, size is 38656 bytes
[INFO: Bl1Main   ] BL1: Trying to load and verify BL2 image from volume bootloader...
[INFO: BlCommon  ] Loading image from #0x7e000128 to #0x80 with size 13252 successfully.
[INFO: Bl1Main   ] BL1: OKay.
[WARN: Boot2     ] usb is not connected
[ERR : SecureBoot] Flash init fail!
[INFO: BlCommon  ] Loading image from #0x7e002418 to #0x7e100000 with size 39846 successfully.

[BOOT33]VB_VERSION_DATE   :[20220719]

[17:48:30.739]收←◆[LOGO] VERSION_DATE   :[20220719]

[17:48:31.027]收←◆
 ** PC      : 0x8002a000
Version: SDK_1.011.078_02 Apr  7 2023 15:59:11
Ac 4, Board 2, DKB 1
Cinit 2
Flash type: nvm 0, factory 0, fota 0
Phase1Inits
diag Phase1 Init
SVT 122
profile num = 3
CRNL_SVC_FP[0] = 0x19
CRNL_SVC_FP[1] = 0x1b
CRNL_SVC_FP[2] = 0x1d
CRNL_SVC_FP[3] = 0x23
PSRAM fuse: 13

Bootup not silent
[ELOOP] __cm_eloop_init 0x7e246118,128

[17:48:31.136]收←◆[ELOOP] eloop_wait eloop 0x7e246118,4294967295
[ELOOP] rdy_list eloop=0x7e246118
[ELOOP] eloop millsec=4294967295 tick=4294967295
InitTask started
Chip ID: 0xa01606
Crane mpu configure
start file system init
SPI Nor/NAND flash init success

LfsAddress[PTN 0]: 0x3b8000 ~ 0x3f7fff, block: 952 ~ 1015, block cnt: 64, lookahead 8
LfsGrpCnt 3
lfs.c:1249:error: Corrupted dir pair at {0x0, 0x1}
lfs_api.c:910:error: Fail to mount in PTN 0, err -84

[17:48:31.224]收←◆lfs[PTN 0] init success

[17:48:31.254]收←◆sulog Disable:0x4a656515,0x55
sulog Enable:0x45555515,0x55

[17:48:31.377]收←◆OnKeyPoweroff_Task
eeHandlerPhase2Init

[17:48:32.075]收←◆RD WRONG validBufferStamp =[ffffffff]
Fail to unpack factory data.

RF ADC value 323@400mV, 651@1600mV


[17:48:32.163]收←◆[ cmlog ]60,__cm_nv_def_fota_load read open failed, maybe not inital
[ cmlog ]123,__cm_nv_def_fota_cfg_init entry, init all

[17:48:32.270]收←◆[ cmlog ]netcard type = 16 cfg = 0
Usb mode 0, descriptor 70
ACMSetAudioConfigPara: audio_PCM_Print:1, ACMCOMM_CombinedSPK=0, audio_IsBypassPM813PA=0
ACMSetAudioConfigPara: audio_disableHSDetect=1, audio_pm802_use_codec=0, audio_hs_normal_closed=0

[17:48:32.445]收←◆ACMSetAudioConfigPara: audio_PCM_Print:1, ACMCOMM_CombinedSPK=0, audio_IsBypassPM813PA=0
ACMSetAudioConfigPara: audio_disableHSDetect=0, audio_pm802_use_codec=1, audio_hs_normal_closed=0
power_up_reason: 0x2, power_down_reason: 0x0

[17:48:32.743]收←◆InitIPCControl: data:0x5

[17:48:33.466]收←◆AT command server ready!
Booting done
[ cmlog ]sim0 cmiot send AT+CPMS 

[ cmlog ]sim0 cmiot send AT+COPS

[ cmlog ]sim0 cmiot send AT+CGDCONT=1,"IPV4V6"


[17:48:33.618]收←◆[ cmlog ]cmiot setConnectMode 1(0-manual 1-auto)
SW restart reason: Charging in powering off
[ cmlog ]dm nv read err
[ cmlog ]isSetTimeout [0], info->failure [0], time [0 0 0 0 0 0 0]
[CM-OC] func:cm_opencpu_start line:209,core_stub=0x80261e35
[CM-OC] func:__oc_app_load_from_flash line:100,app info:name=user_app,start addr=0x80338000,size=0x80000,vsize=0x45800
[CM-OC] func:__oc_app_load_from_flash line:110,magc_num:0x5a5aa5a5,vma:0x7e1b0000,lma:0x80338000,sz:0x45800
[CM-OC] func:__oc_app_load_from_flash line:128,type:0,offset:0x78,vma:0x80338078,lma:0x80338078,sz:0x3c200
[CM-OC] func:__oc_app_load_from_flash line:128,type:1,offset:0x3c278,vma:0x80374278,lma:0x80374278,sz:0x83a4
[CM-OC] func:__oc_app_load_from_flash line:128,type:2,offset:0x44d80,vma:0x7e1b0758,lma:0x8037cd80,sz:0xa7c
[CM-OC] func:__oc_app_load_from_flash line:128,type:3,offset:0xfde791d8,vma:0x7e1b11d8,lma:0x7e1b11d8,sz:0x2be0
[CM-OC] func:__oc_app_load_from_flash line:128,type:4,offset:0x44628,vma:0x7e1b0000,lma:0x8037c628,sz:0x758
[ cmlog ][OPENCPU]:gpio set_pin_func ret:0


[ cmlog ]uart irq num:59

[ cmlog ]uart irq num:34

[ cmlog ][OPENCPU]:





[ cmlog ][OPENCPU]:============== CM OpenCPU Starts ==============

[ cmlog ][OPENCPU]:SDK VERSION:ML307A_OpenCPU_Standard_1.3.1.2304071557_release

[ cmlog ][OPENCPU]:fs total:262144,remain:184320

[ cmlog ][OPENCPU]:heap total:1310716,remain:657888


[ cmlog ][OPENCPU]:Gateway software ver: TeB7EC-4G4_GW_2.0.3_R_20230428

[ cmlog ][OPENCPU]:Gateway hardware ver: TeB7EP-100Y_T_V1.0_20221227
```

