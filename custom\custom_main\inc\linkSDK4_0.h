﻿/*********************************************************
*  @file    linkSDK4_0.h
*  @brief   ML302 OpenCPU aliyun c-link4.0 file
*  Copyright (c) 2021 TENGEN.
*  All rights reserved.
*  created by Delei 2021/03/25
********************************************************/

#ifndef __LINKSDK4_0_H__
#define __LINKSDK4_0_H__

// #include "aiot_logpost_api.h"

#define APP_RN_VER                  "TeB7EP-4G6C_3.0.4_R_20250529"        //当前的软件版本
#define PCB_RN_VER                  "TeB7EP-4G-R_V1.4_20230625"           //当前的硬件版本
#define TEST_ML307_DCLN_VER         0x06                                  //电源网关一体式

#define HEARTBEAT_INTERVAL_VAL      40*1000                                 //心跳包发送间隔：40s
#define HEARTBEAT_MAX_LOST          6                                       //心跳包最大连续丢失数：6次
#define YUN_KEEP_ALIVE_TIME         240                                     //网关在云端的保活时间
#define MQTT_RECONN_MAX_NUM         10                                      //MQTT已经建立连接，由于干扰等外部因素导致MQTT重新连接，连接的最大尝试次数

//固件包，每包数据
#define FMIR_PACK_LEN               128//这里定义为128Bytes每包，也可定义为1024
#if(FMIR_PACK_LEN == 128)
    #define FEATURE_CODE            0x01
#elif(FMIR_PACK_LEN == 1024)
    #define FEATURE_CODE            0x02
#else
    #error "Define FMIR_PACK_LEN illega !"
#endif

void  linkkit_start(void);
void deal_with_task(void *handle);
// int  send_log(aiot_logpost_level_t log_level, char *str, ...);
void disp_boot_cause(void);

#endif
