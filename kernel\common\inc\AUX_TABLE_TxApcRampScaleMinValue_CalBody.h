/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*********************************************************************
* Filename:     AUX_TABLE_TxApcRampScaleMinValue_CalBody.h
* Created By:   <PERSON><PERSON> meltser on 6/18/2008
* Programmers:  El<PERSON> Goldman
* Module:       Generic RF Module - Data Base
* RF Type:      SKY_Hel3
* DB Ver.:      0.9
* Doc Ver.:     2.94
* GUI Ver.:     7.26
*
* Description:  Contains the "AUX_TABLE_TxApcRampScaleMinValue" CalBody
*
* Notes:        This file is automatically generated by WIZARD.
*********************************************************************/

/*
 * each line in the file contain the data of 2*Int16:
*/
//|-RSMV_LPZ---------------|-RSMV_HPZ---------------|-fixedIndex2------------|-fixedIndex1------------|-fixedIndex0------------|
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // EGSM_BAND,                GMSK_MODULATION,          PSK8_TO_GMSK,            
   64,                      0,                       // EGSM_BAND,                GMSK_MODULATION,          PSK8_TO_PSK8,            
   25,                      0,                       // EGSM_BAND,                GMSK_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // EGSM_BAND,                PSK8_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 GMSK_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // DCS_BAND,                 PSK8_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 GMSK_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // PCS_BAND,                 PSK8_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             GMSK_MODULATION,          PSK8_TO_NONE,            

   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          NONE_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          NONE_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          GMSK_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          GMSK_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          GMSK_TO_NONE,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          PSK8_TO_GMSK,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          PSK8_TO_PSK8,            
   0,                       0,                       // GSM_850_BAND,             PSK8_MODULATION,          PSK8_TO_NONE,            

