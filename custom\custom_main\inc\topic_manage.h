/*********************************************************
*  @file    topic_manage.h
*  @brief   ML302 OpenCPU topic manage file
*  Copyright (c) 2021 TENGEN.
*  All rights reserved.
*  created by Delei 2021/03/25
********************************************************/
#ifndef _TOPIC_MANAGE_H_
#define _TOPIC_MANAGE_H_

#include "core_mqtt.h"
#include "topic_custom.h"

#define TOPIC_MODULE_NAME              "TOPIC"

#define ALIYUN_PRODUCT_KEY_LEN         (20)   /* IoTx product key length  */
#define ALIYUN_PRODUCT_SECRET_LEN      (64)   /* IoTx product secret length */
#define ALIYUN_DEVICE_NAME_LEN         (32)   /* IoTx device name length */
#define ALIYUN_DEVICE_SECRECT_LEN      (64)   /* IoTx device secrect length */

#define MAX_CB_NUM                     32
#define MAX_TASK_NUM                   256
#define MAX_LINK_TASK_NUM              64
#define FIXED_POINT_FRACTIONAL_BITS    2
//period contrl
#define PERIOD_ONENABLE                "ontimeEnable"
#define PERIOD_OFFENABLE               "offtimeEnable"
#define PERIOD_OPENBEGINTIME           "open_beginTime"
#define PERIOD_OPENENDTIME             "open_endTime"
#define PERIOD_CLOSEBEGINTIME          "close_beginTime"
#define PERIOD_CLOSEENDTIME            "close_endTime"
#define PERIOD_HOUR                    "hour"
#define PERIOD_MINUTE                  "minute"
#define PERIOD_SECOND                  "second"

//para property
#define PROPERTY_BKTYPE                "CircuitBreakerType"
#define PROPERTY_ADCTYPE               "adcType"
#define PROPERTY_MAXTEMPRATURE         "fMaxTemprature"//冉能平台是fMax
#define PROPERTY_MAXTEMPRATURE_SIFANG  "iMaxTemprature"//冉能平台是fMax
#define PROPERTY_PROTECTSTATE          "iProtectState"
#define PROPERTY_CURRENTLIMIT          "iCurrentLimit"//电流一级限定值
#define PROPERTY_POWERLIMIT            "iPowerLimit"//功率一级限定值
#define PROPERTY_ADDR                  "CircuitBreakerAddress"
#define PROPERTY_ID                    "BreakId"
#define PROPERTY_DATA                  "ProductDate"
#define PROPERTY_FACTORY               "ProductFactory"
//alarm    property
#define PROPERTY_ALARM                 "Error"    
#define PROPERTY_VAL                   "Val"    
//time property
#define PROPERTY_TIME                  "ts"
//NTP property
#define DEVICE_NTP_TIME                "deviceSendTime"
#define CLIENT_ID				      "ClientId"//北京四方新增
//rtdata property
#define PROPERTY_BKSTATE               "CircuitBreakerReclosingState"//断路器分合闸：1合闸；0分闸
//分合闸原因分为大类和小类
#define ON_RES                         "onRes"//合闸原因(小类)
#define OFF_RES                        "offRes"//分闸原因(小类)
#define ON_RES_BIG                     "onResBig"//合闸原因(大类)
#define OFF_RES_BIG                    "offResBig"//分闸原因(大类)

#define PROPERTY_IA                    "fIa"
#define PROPERTY_IB                    "fIb"
#define PROPERTY_IC                    "fIc"
#define PROPERTY_IN                    "fIn"
//电压，精度0.0
#define PROPERTY_UA                    "iOrgUa"
#define PROPERTY_UB                    "iOrgUb"
#define PROPERTY_UC                    "iOrgUc"
//温度，精度0.0
#define PROPERTY_TEMPRATURE_A          "fTempPha"
#define PROPERTY_TEMPRATURE_B          "fTempPhb"
#define PROPERTY_TEMPRATURE_C          "fTempPhc"
#define PROPERTY_TEMPRATURE_N          "fTempPhn"
//功率，精度0.00
#define PROPERTY_POWER_A               "fPhaP"
#define PROPERTY_POWER_B               "fPhbP"
#define PROPERTY_POWER_C               "fPhcP"
#define PROPERTY_POWER_SUM             "fPsum"
//功率因素
#define PROPERTY_POWERFACTOR_A         "fPFa"
#define PROPERTY_POWERFACTOR_B         "fPFb"
#define PROPERTY_POWERFACTOR_C         "fPFc"
#define PROPERTY_POWERFACTOR_SUM       "fPFsum"
//电量，精度0.000
#define PROPERTY_ENERGY_A              "fKwhPha"
#define PROPERTY_ENERGY_B               "fKwhPhb"
#define PROPERTY_ENERGY_C               "fKwhPhc"
#define PROPERTY_ENERGY_SUM             "fKWHsum"
//漏电，精度0.0
#define PROPERTY_LEAKI                  "fLeakCurrent"
#define PROPERTY_VERSION                "version"
#define PROPERTY_ONLINESTATE            "Online"
#define PROPERTY_HIGHU                  "iHighU"//过压一级限定值
#define PROPERTY_LOWU                   "iLowU"//欠压一级限定值
#define PROPERTY_HIGHLEAK               "iHighLeakI"//漏电流一级阈值(设置0等同于关闭)
#define PROPERTY_RATED                  "iRatedI"//额定电流
//二级阈值
#define PROPERTY_THIGHU                 "tHighU"
#define PROPERTY_TLOWU                  "tLowU"
#define PROPERTY_TIA                    "tIa"
#define PROPERTY_TIB                    "tIb"
#define PROPERTY_TIC                    "tIc"
#define PROPERTY_TIN                    "tIn"
#define PROPERTY_TLEAKI                 "tLeakI"
#define PROPERTY_TTEMP                  "tTemp"
#define PROPERTY_LOCK                   "lock"


//新增功率(过载)二级阈值(定制)
#define PROPERTY_TPWA				"tPwA"	//设置A相功率(过载)二级阈值(定制功能)
#define PROPERTY_TPWB				"tPwB"	//设置B相功率(过载)二级阈值(定制功能)
#define PROPERTY_TPWC				"tPwC"	//设置C相功率(过载)二级阈值(定制功能)

//一级阈值(定制)
#define PROPERTY_CIA                "cIA"    //设置A相电流一级阈值(定制功能)
#define PROPERTY_CIB                "cIB"    //设置B相电流一级阈值(定制功能)
#define PROPERTY_CIC                "cIC"    //设置C相电流一级阈值(定制功能)
#define PROPERTY_CPWA               "cPwA"   //设置A相功率一级阈值(定制功能)
#define PROPERTY_CPWB               "cPwB"   //设置B相功率一级阈值(定制功能)
#define PROPERTY_CPWC               "cPwC"   //设置C相功率一级阈值(定制功能)
#define PROPERTY_CHIGHUA            "cHighUA"//设置A相过压一级阈值(定制功能)
#define PROPERTY_CHIGHUB            "cHighUB"//设置B相过压一级阈值(定制功能)
#define PROPERTY_CHIGHUC            "cHighUC"//设置C相过压一级阈值(定制功能)
#define PROPERTY_CLOWUA             "cLowUA" //设置A相欠压一级阈值(定制功能)
#define PROPERTY_CLOWUB             "cLowUB" //设置B相欠压一级阈值(定制功能)
#define PROPERTY_CLOWUC             "cLowUC" //设置C相欠压一级阈值(定制功能)
#define PROPERTY_CTEMPA             "cTempA" //设置A相过温一级阈值(定制功能)
#define PROPERTY_CTEMPB             "cTempB" //设置B相过温一级阈值(定制功能)
#define PROPERTY_CTEMPC             "cTempC" //设置C相过温一级阈值(定制功能)
#define PROPERTY_CTEMPN             "cTempN" //设置N相过温一级阈值(定制功能)

//漏电重合闸时间
#define PROPERTY_INTVTIME_1             "intvTim1"
#define PROPERTY_INTVTIME_2             "intvTim2"
#define PROPERTY_INTVTIME_3             "intvTim3"
#define PROPERTY_INTVTIME_4             "intvTim4"
#define PROPERTY_INTVTIME_5             "intvTim5"
//过压重合闸时间
#define PROPERTY_INTVTIME_OV1           "intvTimOV1"
#define PROPERTY_INTVTIME_OV2           "intvTimOV2"
#define PROPERTY_INTVTIME_OV3           "intvTimOV3"
//欠压重合闸时间
#define PROPERTY_INTVTIME_LV1           "intvTimLV1"
#define PROPERTY_INTVTIME_LV2           "intvTimLV2"
#define PROPERTY_INTVTIME_LV3           "intvTimLV3"

//漏电跳闸使能
//漏电流值调整使能位
//0:不支持(默认值)
//1:支持两档可调，"leakI"是0和3
//2:支持四档可调，"leakI"是0和1和2和5
#define LEAK_EN                     "leakIEn"

// 漏电流阈值设置
// 0:小于等于 30mA
// 1:大于 30mA 且小于等于 50mA
// 2:大于 50mA 且小于等于 100mA
// 3:大于 100mA 且小于等于 250mA
// 4:大于 250mA 且小于等于 500mA
// 5:大于 500mA 且小于等于 1000mA
// 6:大于 1000mA
// 9: 不限制
#define LEAK_I                      "leakI"

//漏电流一级阈值，mA
#define LEAK                        "leak"



#define PROPERTY_DMP_EN    "dmp_en"
#define PROPERTY_DMP_TM    "dmp_tm"

//alarm    property
#define PROPERTY_ALARM_Level2           "Alarm"
#define PROPERTY_ALARM                  "Error"    
#define PROPERTY_VAL                    "Val"
#define PROPERTY_SET_LOCK               "Setlock"
// gateway time 
#define T_SN                            "SN"
#define T_YEAR                          "year"
#define T_MON                           "mon"
#define T_DAY                           "day"
#define T_HOUR                          "hour"
#define T_MIN                           "min"
#define T_TYPE                          "Type"
#define T_VAL                           "Val"
#define T_ENABLE                        "enable"
#define DEADBAND_U                      "dead_band_U"
#define DEADBAND_I                      "dead_band_I"
#define DEADBAND_LEAKI                  "dead_band_LeakI"
#define DEADBAND_KWH                    "dead_band_Kwh"
#define DEADBAND_P                      "dead_band_P"
#define DEADBAND_T                      "dead_band_T"
#define TIMES_CIRCLE_SEND               "send_circle_count"
#define TIMES_CHANGE_SEND               "send_change_count"
#define TIMES_SCAN_SEND                 "send_scan_time"


//SIM卡的ICCID以及信号强度RSSI(科舸)
#define SIM_ICCID				    "ICCID"
#define RSSI					    "RSSI"
#define RSRP                        "RSRP"
#define RSRQ                        "RSRQ"
#define SINR                        "SINR"


#define HEART_BEAT                 "heartbeat"//增强型心跳Topic

#define TYPE_DEVICE_INFO                            0
#define TYPE_CIRCUIT_BREAKER_PARA                   1
#define TYPE_CIRCUIT_BREAKER_PARA_CHANGE            2
#define TYPE_CIRCUIT_BREAKER_DATA                   3
#define TYPE_CIRCUIT_BREAKER_DATA_CHANGE            4
#define TYPE_CIRCUIT_BREAKER_ALARM                  5
#define TYPE_CIRCUIT_BREAKER_PARA_MODIFY            6
#define TYPE_CIRCUIT_BREAKER_CONTROL                7
#define TYPE_CIRCUIT_BREAKER_CHECK                  8
#define TYPE_CIRCUIT_BREAKER_CONTROL_TIME           9
#define TYPE_CIRCUIT_BREAKER_CHECK_TIME             10
#define GATEWAY_RESTART                             11

#define CB_EVENT_SLEEP 20 /*send event sleep times,ms*/
#define TICK_DELAY 1000    /*send cloud data loop sleep time(ms)*/
#define CIRCLE_TICK_DELAY_COUNT 300
#define CHANGE_TICK_DELAY_COUNT 1
#define HISTORY_TICK_DELAY_COUNT 300
#define CONNECT_TICK_DELAY_COUNT 1
#define CONNECT_CHECK_DELAY_COUNT 20
#define TIME_SYNCHRONIZATION_DELAY_COUNT 3600

//task or protocol type define
typedef enum E_TASKTYPE
{
    TASK_NULL = -1,
    E_READ_DEV_INFO,         //读取设备信息
    E_READ_IOT_THREELEMENTS, //read aliyun three elements
    E_READRTDATA,            //read rtdata
    E_WRITE_PARA,            //write para
    E_YK,                    //remote control
    E_DISPATCH_OVER,         //dispatch address over
    E_AUTODISPATCH_ADDR,     //dispatch address
    E_LEAKCHECK,             //leak current check
    E_WAITLEAK,
    E_CHG_HIGH_TEMPRATURE,  //板载过温一级限定值
    E_CHG_RATED_CURRENT,    //额定电流(过流一级限定值)
    E_UPDATE_FIRMWARE,      //微断进入、退出升级状态
    E_ENABLE_PROT_FUNCTION, //修改保护位寄存器1
    E_CHG_POWER_LIMITS,
    // E_CHG_POWER_LIMITS_A,     //A相功率一级限定值
    // E_CHG_POWER_LIMITS_B,     //B相功率一级限定值
    // E_CHG_POWER_LIMITS_C,     //C相功率一级限定值
    E_CHG_CURRENT_LIMITS,   //A相电流一级限定值
    // E_CHG_CURRENT_LIMITS_A,   //A相电流一级限定值
    // E_CHG_CURRENT_LIMITS_B,   //B相电流一级限定值
    // E_CHG_CURRENT_LIMITS_C,   //C相电流一级限定值
    E_EXITMODBUS,
    E_READ_THRESHOLD,       //读二级阈值的寄存器
    E_READ_ALARM_EVENT, //读取告警事件寄存器(6FH-7DH)带告警时间记录
    E_THRESHOLD_HIGHU,      //设置过压二级阈值
    E_THRESHOLD_LOWU,       //设置欠压二级阈值
    
    E_THRESHOLD_IA,         //设置A相电流二级阈值
    E_THRESHOLD_IB,         //设置B相电流二级阈值
    E_THRESHOLD_IC,         //设置C相电流二级阈值
    E_THRESHOLD_IN,         //设置N相电流二级阈值
    E_THRESHOLD_LEAKI,      //设置漏电流二级阈值
    E_THRESHOLD_TEMP,       //设置温度二级阈值
    E_THRESHOLD_PWA,   //设置A相功率二级阈值(定制功能)
	E_THRESHOLD_PWB,   //设置B相功率二级阈值(定制功能)
	E_THRESHOLD_PWC,   //设置C相功率二级阈值(定制功能)
    E_LOCK,                 //设置闭锁功能
    E_SET_INTVTIME_1,       //设置第1次漏电重合时间
    E_SET_INTVTIME_2,       //设置第2次漏电重合时间
    E_SET_INTVTIME_3,       //设置第3次漏电重合时间
    E_SET_INTVTIME_4,       //设置第4次漏电重合时间
    E_SET_INTVTIME_5,       //设置第5次漏电重合时间
    E_SET_OVER_V1,          //设置第1次过压重合时间
    E_SET_OVER_V2,          //设置第2次过压重合时间
    E_SET_OVER_V3,          //设置第3次过压重合时间
    E_SET_LESS_V1,          //设置第1次欠压重合时间
    E_SET_LESS_V2,          //设置第2次欠压重合时间
    E_SET_LESS_V3,          //设置第3次欠压重合时间
    E_PERIOD,               //时段控制分合闸
    E_CLK,                  //定时控制分合闸
	E_MB_TRANSMIT,       // modbus 命令转发给微断
    E_WRITE_SINFLEREGVAL,   //485写寄存器
    E_WRITE_SINCOIL,        //write single coils for breaks
    E_WRITE_REGVAL,         //write muti register for breaks
    
    E_READ_CUSTS,      //读取一级阈值的寄存器(定制功能)
    E_WRIT_CUSTS,      //一次设置下面所有一级阈值(定制功能)
    E_CUST_IA,         //设置A相电流一级阈值(定制功能)
    E_CUST_IB,         //设置B相电流一级阈值(定制功能)
    E_CUST_IC,         //设置C相电流一级阈值(定制功能)
    E_CUST_PWA,        //设置A相功率一级阈值(定制功能)
    E_CUST_PWB,        //设置B相功率一级阈值(定制功能)
    E_CUST_PWC,        //设置C相功率一级阈值(定制功能)
    E_CUST_HIGHA,      //设置A相过压一级阈值(定制功能)
    E_CUST_HIGHB,      //设置B相过压一级阈值(定制功能)
    E_CUST_HIGHC,      //设置C相过压一级阈值(定制功能)
    E_CUST_LOWA,       //设置A相欠压一级阈值(定制功能)
    E_CUST_LOWB,       //设置B相欠压一级阈值(定制功能)
    E_CUST_LOWC,       //设置C相欠压一级阈值(定制功能)
    E_CUST_TEMPA,      //设置A相过温一级阈值(定制功能)
    E_CUST_TEMPB,      //设置B相过温一级阈值(定制功能)
    E_CUST_TEMPC,      //设置C相过温一级阈值(定制功能)
    E_CUST_TEMPN,      //设置N相过温一级阈值(定制功能)
    E_SET_LEAKEN,       //设置漏电跳闸保护功能的开与关
    E_TYPE_MAX
}TASKTYPE;

#define TASK_STATUS_SENDING 1
#define TASK_STATUS_WAITING 0
//保护位重映射方向
#define REMAP_GW2RANNENG    0   //网关保护位映射到冉能平台保护位
#define REMAP_RANNENG2GW    1   //冉能平台保护位映射到网关保护位

/* -------------------订阅的Topic----------------------- */
//OTA的Topic,因为使用的是自定义Topic，所以这里需要订阅
#define OTA_TOPIC_GATEWAY       "user/ota/gateway/upgrade"
#define OTA_TOPIC_SUBDEV        "user/ota/upgrade"
//普通Topic
#define SUB_SET_PARA            "user/setpara"                  //断路器参数修改
#define SUB_CTRL                "user/ctrl"                     //断路器分合控制，变化上送，需要向云端回执变化后的结果
#define SUB_LEAK_CHECK          "user/leakcheck"                //断路器漏电自检，变化上送
#define SUB_LOCAL_TIMER_CTRL    "user/localtimer/ctrl"          //断路器本地定时控制,定时器类型：0，单次定时，最高位使能;1,周1~7,按位定义，最高位使能
#define SUB_LOCAL_TIMER_LC      "user/localtimer/leakcheck"     //断路器本地定时自检,定时器类型：0，单次定时，最高位使能;1,周1~7,按位定义，最高位使能
#define SUB_GATEWAY_CTRL        "user/gatewayctrl"              //网关控制
#define SUB_PERIOD_CTRL         "user/periodctrl"               //子设备时段控制信息下发
// 变动更改后需要发送的Topic
#define SUB_GATEWAY_PRI_CTRL    "user/gateway/periodctrl"       //网关时段控制信息下发(目前仅支持一个时段)
#define SUB_CMD_SEND            "user/mccb/cmd"                 //云端命令下发（待启用）
#define SUB_HEART_BEAT         "command.%s.%s.heartbeat"            //增强型双向心跳专用Topic
/* -------------------发布的Topic----------------------- */
// 开机发布一次的Topic

// 周期发送的Topic

// 其他Topic

uint32_t sub_topics(void *handle);
uint32_t unsub_topic(void *handle);
uint32_t pub_topics_OnlyOnes(void *handle);
void bubbleSort(int *arr, int n);
void CircuitBreakerPeriodPublish(const int addr);
void GatewayPeriodPublish(void);
uint32_t SendMessageToAliyun(char *topicStr, char *data, uint8_t qos);
void DeviceInformationPublish(const int addr);
void CircuitBreakerDataPublish(const int addr);
void CircuitBreakerStateChangePublish(const int addr,int cmd);
void CircuitBreakerDataChangePublish(const int addr);
void CircuitBreakerParameterChangePubilsh(const int addr);
void CircuitBreakerAlarmPublish(const int addr, const int errcode);
void CircuitBreakerParameterPublish(const int addr);
uint8_t ReadDevInfo(uint8_t addr);
void AddGWlogCode(int num);
void LocalTimePublish(const int TimeSn);
void sendOneKeyCtrlTopic(int CtrlVal);
aiot_mqtt_recv_handler_t  GetRecvHandler(topic_custom_product_e  product ,topic_custom_subtable_e sub);
void addHistory2Flash(void);
void PubTopic_gateway_info(void);
void sendHistoryInTopic(uint32_t breakId, uint32_t timeBefore, uint32_t timeAfter);
void enhanceHearbeatTopicSend(void);

void syncTempcVal(int addr);
void syncfCloudKwhVal(int addr);
void sendErrorClean(int addr);

#endif // !_TOPIC_MANAGE_H_
