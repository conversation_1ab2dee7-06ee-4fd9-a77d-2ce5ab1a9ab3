/*
 * embedded IPsec
 * Copyright (c) 2003 <PERSON><PERSON> and <PERSON>, HTI Biel/Bienne
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice,
 *    this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
 * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
 * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 * OF SUCH DAMAGE.
 *
 */

/** @file des.h
 *  @brief Header of DES and 3DES cipher
 *
 *  <AUTHOR> Schild <<EMAIL>>
 *
 * This document is part of <EM>embedded IPsec<BR>
 * Copyright (c) 2003 Niklaus Schild and Christian Scheurer, HTI Biel/Bienne<BR>
 * All rights reserved.<BR>
 * This file contains code from the OpenSSL Project<BR>
 * portions Copyright (c) 1998-2003 OpenSSL (www.openssl.org)
 *</EM><HR>
 */

#ifndef __IPSEC_DES_H__
#define __IPSEC_DES_H__

#include "ipsec_types.h"

#if LWIP_IPSEC

#ifndef NO_SUPPORT_TLS
#include "mbedtls/des.h"
#include "mbedtls/compat-1.3.h" // mbedtls
#else
/*
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 * ! DES_LONG has to be at least 32 bits wide.                    !
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 */
#define DES_LONG __u32

typedef unsigned char DES_cblock[8];
typedef const unsigned char const_DES_cblock[8];

typedef struct DES_ks
{
    union
	{
		DES_cblock cblock;
		DES_LONG deslong[2]; /* make sure things are correct size on machines with 8 byte longs */
	} ks[16];
} DES_key_schedule;

#define DES_ENCRYPT	1							/**< defines encryption for the des function */
#define DES_DECRYPT	0							/**< defines decryption for the des function */
#endif

//int DES_set_key_checked(const_DES_cblock *key,DES_key_schedule *schedule);
//void DES_set_key_unchecked(const_DES_cblock *key,DES_key_schedule *schedule);
void ipsec_cipher_3des_cbc(unsigned char*, int, unsigned char*, unsigned char*, int, unsigned char*);

#endif
#endif
