/*
 * @FilePath: \ml307_dcln\custom\custom_main\inc\topic_mccb.h
 * @Description: 
 * @Author: ji<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-18 08:44:40
 * @LastEditors: ji<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-18 09:07:35
 * 
 * Copyright (c) 2023 by Zhejiang Tengen Electric Co., Ltd., All Rights Reserved. 
 */
#ifndef TOPIC_MCCB_H
#define TOPIC_MCCB_H

/* --------------------topic定义-------------------------*/
//发布
/* --------------------字段定义-------------------------*/

// 塑壳相关变量定义
#define POOPERTY_FREP "freq"
#define PROPERTY_FREP_A "freqPha"
#define PROPERTY_FREP_B "freqPhb"
#define PROPERTY_FREP_C "freqPhc"	

#define PROPERTY_TEMPRATURE_DEV "fTempDev"
#define PROPERTY_TEMPRATURE_A "fTempPha"
#define PROPERTY_TEMPRATURE_B "fTempPhb"
#define PROPERTY_TEMPRATURE_C "fTempPhc"
#define PROPERTY_TEMPRATURE_N "fTempPhn"

// 塑壳累计记录查询
#define PROPERTY_TOTAL_CLEAR "TotalClear"
#define PROPERTY_TOTAL_TRIP "TotalTrip"
#define PROPERTY_IRESBLOCK_TRIP "IresBlockTrip"
#define PROPERTY_IRESPROTECT_TRIP "IresProtectTrip"
#define PROPERTY_OVERLOADPROTECT_TRIP "OverloadProtectTrip"
#define PROPERTY_OVERVOLPROTECT_TRIP "OvervolProtectTrip"
#define PROPERTY_MANUAL_TRIP "ManualTrip"
#define PROPERTY_ZEROMISSPROTECT_TRIP "ZeroMissProtectTrip"
#define PROPERTY_TEST_TRIP "TestTrip"
#define PROPERTY_SCSTD_TRIP "SCSTDTrip"
#define PROPERTY_SCI_TRIP "SCITrip"
#define PROPERTY_UNDERVOLPROTECT_TRIP "UndervolProtectTrip"
#define PROPERTY_OPENPHASEPROTECTION_TRIP "OpenPhaseProtectionTrip"
#define PROPERTY_TOTALRUN_TIMER "TotalRunTimer"

/* 塑壳事件记录 */
#define PROPERTY_EVENT_SUM "EventSum"
#define PROPERTY_EVENT_INDEX "EventIndex"
#define PROPERTY_EVENT_TYPE "EventType"
#define PROPERTY_EVENT_YEAR "EventYear"
#define PROPERTY_EVENT_MON "EventMon"
#define PROPERTY_EVENT_DAY "EventDay"
#define PROPERTY_EVENT_HOUR "EventHour"
#define PROPERTY_EVENT_MIN "EventMin"
#define PROPERTY_EVENT_SEC "EventSec"
#define PROPERTY_EVENT_INFO "EventInfo"
#define PROPERTY_EVENT_UA "EventUa"
#define PROPERTY_EVENT_UB "EventUb"
#define PROPERTY_EVENT_UC "EventUc"
#define PROPERTY_EVENT_IA "EventIa"
#define PROPERTY_EVENT_IB "EventIb"
#define PROPERTY_EVENT_IC "EventIc"
#define PROPERTY_EVENT_LEAK "EventLeak"



void MCCBDeviceInfoPublish(const int addr);
void MCCBDataPublish(const int addr);
void MCCBRecordPublish(const int addr);
void MCCBChangedDataPublish(const int addr);
void MCCBEventPublish(const int addr);
void MCCBStateChangePublish(const int addr, int cmd);


#endif