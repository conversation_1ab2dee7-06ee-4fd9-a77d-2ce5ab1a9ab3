# 支持阻塞操作的简易定时器APP 简介 

## **定时器介绍**
通信模组操作系统均提供定时器功能，但不同平台定时器使用存在差异。部分平台定时器功能不支持阻塞操作，若存在阻塞操作会造成系统死机。对于这类平台的产品，可通过线程的方式模拟出支持阻塞操作的定时器，相关阻塞操作在线程中实现。

## **实现功能**
实现模组定时检测网络信号并在LCD上显示网络信号值，包括以下子功能：
1. 基于操作系统中定时器和任务接口实现一组支持在定时器定时回调函数中执行阻塞操作的模拟定时器接口。包括：`app_timer_create` 、`app_timer_start`、`app_timer_stop`、`app_timer_destroy`；
2. 定时检测PDP激活状态和CSQ值（图标计算仅为示例）并通过st7735s显示网络信号图标。

## **APP执行流程**
1. 设备上电，执行主任务`timer_demo_appimg_enter`；
2. LCD初始化；
3. LCD自检（轮询显示各个信号值图标）；
4. 启动定时器；
5. 定时器循环检测网络数据并同时LCD现实对应的信号值图标。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC/ML307C-DC-CN
- 支持的SDK版本：ML307AR OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.0版本及其后续版本
- 是否需要外设支撑：模组LCD硬件接口与st7735s LCD屏幕连接
- 使用注意事项：APP使用st7735s LCD显示屏，未使用背光控制，使用CM_GPIO_NUM_2作为复位脚；
- st7735s LCD显示屏图标颜色或位置可通过宏black、white、x_start、x_end、y_start、y_end修改
- APP使用前提：开发板、LCD显示屏（显示图标）、SIM卡（APP需要上网）。默认最多支持20个模拟定时器，可通过宏APP_TIMER_NUN修改。

## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 11:32
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/11/1 16:00
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------