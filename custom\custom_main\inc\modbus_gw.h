/*
 * Copyright (C) 2019-2030 tengen Group Holding Limited
 * @ modbus_gw.h: tengen smart gateway modbus protocol define
 * @ function:
 *      1.read para from breaker;
 *      2.read rtdata from breaker;
 *      3.remote control breaker;
 *      4.auto dispatch breaker address;
 *      5.breaker communicating status manager;
 */
#ifndef __MODBUS_GW_H__
#define __MODBUS_GW_H__

#include "topic_manage.h"

//ctrol para
typedef struct
{
    int      iCtrlAddress;
	uint8_t func;
	uint16_t regAddr;
    uint8_t         ucVval;
}CTRL_DATA;
//protocol command
typedef struct
{
    S_WRITE_PARA    writeVal;
    TASKTYPE        type;       //type
    char            flag;       //state
    union
    {
        CTRL_DATA   yk;     //remote comtrol
    }data;
    uint32_t        iParaVal;//set para,change high temprature,rated current,current limits,power limits
    int             lasttime; //latest time,exist this time,cancel control
}FERT_COMMAND;

//ProtocolRx para
typedef struct
{
    uint8_t    saveflag;//save flag
    uint16_t    subaddr;//sub address
}PROTO_PARA_RX;

//ProtocolTx para
typedef struct
{
    uint8_t    saveflag;                   //save flag
    struct
    {
        uint16_t    subaddr;                //表地址
    }channel;
    FERT_COMMAND    rxcommand;        //command
}PROTO_PARA_TX;

void changeProtocolState(void);
int readComdata(uint8_t *data);
int search_head(uint8_t *buf,const int len);
int analyze(uint8_t *buf,const int len);
//void analyze(const uint8_t *buf,const int len);
void analyze_para(uint8_t *data,const int len);
void analyze_devname(uint8_t *data,const int len);
void analyze_data(uint8_t *data,const int len);
bool analyze_leak(uint8_t *data,const int len);
void analyze_custVals(uint8_t *data,const int len);
void analyze_productsec(uint8_t *data,const int len);
void analyze_productkey(uint8_t *data,const int len);
void analyze_devsec(uint8_t *data,const int len);
//bool get_Iotinfo(uint8_t *data,const int len);
int modbus_tx(PROTO_PARA_TX* tx);
int modbus_rx(PROTO_PARA_RX* rx);
uint16_t modbus_crc(uint8_t *ptr,uint8_t size);
uint8_t* CRC16_C(uint8_t* data,int x);
//modbus auto dispatch address
void register_irq(void);
void judgeDispach(void);
//other function
uint8_t HexToChar(uint8_t bChar);
void check_three_metal(void *paras);
void modbus_loop(void *paras);

#endif
