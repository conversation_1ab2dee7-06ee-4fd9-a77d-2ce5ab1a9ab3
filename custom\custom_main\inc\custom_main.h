/*********************************************************
*  @file    cm_main.h
*  @brief   ML302 OpenCPU main header file
*  Copyright (c) 2019 China Mobile IOT.
*  All rights reserved.
*  created by XieGangLiang 2019/10/08
********************************************************/
#ifndef __CM_CUSTOM_MAIN_H__
#define __CM_CUSTOM_MAIN_H__

#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include <string.h>
#include <math.h>

#include "cm_gpio.h"
#include "cm_uart.h"
#include "cm_iomux.h"
#include "cm_os.h"
#include "cm_fs.h"
#include "cm_mem.h"
#include "cm_sys.h"
#include "cm_sim.h"
#include "cm_virt_at.h"
#include "cm_rtc.h"
#include "cm_ntp.h"
#include "cm_modem.h"
#include "cm_adc.h"
#include "cm_pm.h"
#include "cm_fota.h"

int cm_opencpu_entry(void *param);

#endif
