# DUMP死机场景测试 简介 

## DUMP死机场景介绍**
/*本示例中代码是故意制造死机现场，请谨慎测试*/
本示例中旨在说明几类常见的死机DUMP情况,并给出了正确写法，因此运行示例代码会触发死机,遇见软件死机问题无需担心,按照以下三大类问题进行排查：
（1）内存不足：
1、线程开辟的栈过小，由于线程中常会使用局部变量，函数递归等都需要耗费栈的空间，因此栈开辟过小，容易导致内存不足死机
2、内存泄漏，常见为使用cm_malloc动态申请内存后，未使用cm_free及时释放，反复多次后，导致内存越来越少，最终死机
（2）内存越界：
1、数组越界，比如一个只有10个元素长度的数组，使用memcpy拷贝了一个远超过数组大小的内存，导致越界死机
2、变量传递错误，比如形参要求传入指针类型，实际传入了变量，变量值被误认为是内存地址导致死机
（3）回调阻塞：
1、定时器回调阻塞：常见软件定时器osTimer到期后，进入回调，在回调中加入osDelay、打印、复杂运算等，回调不能及时退出，导致死机
2、GPIO/串口等中断回调阻塞：同理，在硬件中断中做复杂操作，不能及时退出，导致死机
3、面对以上确实需要在中断中执行应用的场景，正常写法应该是合理使用异步接口如：消息队列/事件组/信号量，回调中将中断事件发送到单一线程中处理

死机问题多为复合型或隐藏在代码中，若检查完以上场景仍然未找到死机代码，有以下方法可以参考：
确认是否存在死机：cm_pm_get_power_on_reason() 返回值为5，则确认存在死机问题
1、使用cm_log_printf打印函数 逐步打印，适用于已确认初步的死机范围，每行代码逐步打印，结合注释前后做对比测试
2、使用cm_mem_get_heap_stats 循环打印，检测内存情况，适用于大范围内排查，确认是否存在内存泄漏
3、查看DBG管脚log，通过字样EE LOG: DESC: stack of dump_dem overflow by itself 可能提示部分函数名或线程,但该方法不是百分之百提示准确
4、抓取USB 模组底层log，递交FAE分析

## **实现功能**
1. 实现了五类常见的典型死机场景：①线程栈过小 ②内存泄漏 ③数组越界 ④变量传递错误 ⑤软件回调阻塞 ⑥硬件回调阻塞
2. 通过配置CM_DUMP_DEMO_TYPE 来进行相关的死机测试

## **APP执行流程**
1. 初始化死机测试主线程
2. 根据CM_DUMP_DEMO_TYPE 执行相应的死机代码
3. 线程栈过小：创建一个只有256字节的线程，进行5次函数递归，内存溢出死机
4. 内存泄漏：使用cm_malloc申请大内存，并故意不释放，最终内存耗尽死机
5. 数组越界：将 1024个字节的内容拷贝到10个字节的内存，造成数组越界，这里实际测试未死机，内存越界是存在死机风险，仅当越界的内存是不能被修改或者被底层使用时才会死机，但在编程过程中也需要防止出现该类情况
6. 变量传递错误： 设置一个函数需要传递指针，故意传递变量，导致非法修改内存地址死机
7. 软件回调阻塞： 在定时器回调中，加入一个延时，或者推送消息队列中timeout设置为非0 制造死机
8. 硬件回调阻塞： 在中断回调中，加入一个延时，或者推送消息队列中timeout设置为非0 制造死机

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**


## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/11 18:08
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------