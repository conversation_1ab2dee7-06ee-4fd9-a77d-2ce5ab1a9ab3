# 简易i2c操作M117B温度传感器芯片简介 

## **模组i2c功能介绍**
本功能模块通过i2c接口驱动M117B温度传感器芯片，主要目的是展示模组作为主设备i2c功能模块接口的使用方法;
本模组的i2c只能作为主设备，不能作为从设备，两线制（CLK+SDA),支持特定波特率和特定地址位数，详见cm_i2c.h；
本模组的i2c为标准协议，这里不再赘述。
M117B温度传感器相关操作，请仔细阅读其芯片手册，本示例仅简单涉及到对齐温度数据的获取，用以展示模组i2c接口的使用。

## **实现功能**
实现模组通过i2c与M117B的通讯，包括以下子功能：
1. 初始化i2c，包括引脚功能设置，i2c的参数设置等；
2. 软件复位M117B；
3. 启动M117B进行温度转换；
4. 获取M117B温度数据并进行处理；
5. 关闭i2c；

## **APP执行流程**
1. 初始化i2c，包括引脚功能设置，i2c的参数设置等；
2. 软件复位M117B；
3. 启动M117B进行温度转换；
4. 获取M117B温度数据并进行处理；
5. 关闭i2c；

## **使用说明**
- 支持的模组（子）型号：ML307R-DC/ML307C-DC-CN；
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.0版本及其后续版本；
- 是否需要外设支撑：必须接M117B温度传感器芯片，如果是其他外设，请阅读相关外设芯片手册调整相关配置参数；
- 使用注意事项：1.确保硬件连接无误；2.确保外设芯片与功能模块中的一致；
- APP使用前提：模组开发板及其外设M117B温度传感器芯片，请注意其电源的电压和接地情况；

## **FAQ**
- 问题：除了关注cm_i2c.h中的接口函数，还需要注意什么？
  答复：1.需调用cm_iomux.h文件引脚配置接口设置选择引脚为i2c功能；
        2.需确保模组和芯片供电稳定且在芯片规定的范围之内和引脚连接正确，可用示波器抓取波形进行相关参数分析；

## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 10:26
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/11/11 10:30
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------