#ifndef __MCCB_COLLECT_H__
#define __MCCB_COLLECT_H__

// #include "info.h"
#include "stdint.h"

// typedef bool BOOL;


#define MODBUS485_UART	 UART_NUM_2


#define MCCBMAXNUM 8  //允许塑壳接入最大数量 
#define MAXERRCOUNT 10 //最大错误次数

#define TIME_DATA_COUNT 1	   //数据查询间隔
#define TIME_RECORD_COUNT 20 //记录查询间隔
#define TIME_EVENT_COUNT 2	   //事件查询间隔
#define TIME_PARA_COUNT 3	   //参数查询间隔

//CRC 参数设置
#define INITCRC 0xFFFF //初始值
#define POLY 0x8005	   //多项式
#define XOROUT 0x0000  //异或值
//塑壳TeM5LC基地址定义
#define BASEADDCONTROL 0000		 //远程控制开合基地址
#define BASEADDRELEPARA 1000	 //基本电表量基地址
#define BASEADDRPARA 2000		 //基本参数基地址
#define BASEADDMODE 3000		 //功能选择基地址
#define BASEADDEVENT 4000		 //事件记录查询基地址
#define BASEADDREMOTE 4500		 //远程控制/查询基地址
#define BASEADDTOTALEVENT 5000	 //累计记录查询基地址
#define BASEADDENERGY 6000		 //电能类查询基地址
#define BASEADDENERGYFREEZE 7000 //整点冻结电能类查询


#define BASEADDRELEPARAMAX 1039	 //基本电表量基地址
#define BASEADDRPARAMAX  2029		 //基本参数基地址

#define BASEADDEVENTMAX  4019		 //事件记录查询基地址
#define BASEADDTOTALEVENTMAX  5015	 //累计记录查询基地址
#define BASEADDENERGYMAX  6011		 //电能类查询基地址




//定义数据类型
typedef unsigned char uint8;
typedef  char int8;
typedef unsigned short uint16;
typedef short int16;
typedef unsigned int uint32;
typedef int			sint32 ;
typedef short		sint16 ;
typedef int int32;
typedef float float32;

typedef unsigned int UINT;	/* int must be 16-bit or 32-bit */
typedef unsigned char BYTE; /* char must be 8-bit */
typedef uint16_t WORD;		/* 16-bit unsigned integer */
typedef uint32_t DWORD;		/* 32-bit unsigned integer */
typedef uint64_t QWORD;		/* 64-bit unsigned integer */
typedef WORD WCHAR;			/* UTF-16 character type */

typedef struct
{
	char status;
	uint16 len;
	char data[2048];
} UARTData;

//MODBUS RTU功能码
enum FunctionCode
{
	ReadCoil = 1,					//读取线圈状态
	ReadInput,						//读取输入状态
	ReadHoldRegister,				//读取保持寄存器
	ReadInputRegister,				//读取输入寄存器
	ForcedSingleCoil,				//强制单线圈
	PresetSingleRegister,			//预置单寄存器
	ReadExceptionStatus,			//读取异常状态
	LoopbackDiagnosticVerification, //回送诊断校验
	Program_484,					//编程（只用于 484）
	Inquiry_484,					//控询（只用于 484）
	ReadEventCount,					//读取事件计数
	ReadCommunicationEventRecord,	//读取通信事件记录
	Program,						//编程（184/384 484 584）
	Inquiry,						//探询（184/384 484 584）
	ForcedMultiCoil,				//强置多线圈
	PresetMultipleRegisters,		//预置多寄存器
	ReportSlaveIdentification,		//报告从机标识
	AnalogProgram,					//（884 和 MICRO 84）
	ResetCommunicationLink,			//重置通信链路
	ReadGeneralParameters,			//读取通用参数（584L）
	WriteGeneralParameters			//写入通用参数（584L）
									//22~64 保留作扩展功能备用
									//65~72 保留以备用户功能所用
									//73~119 非法功能
									//120~127  保留
									//128~225  保留
};

/***************************************************************************************
 **********************************塑壳TeM5LC的协议***********************************
 ***************************************************************************************/

//基本电表量查询
typedef struct
{
	WORD Ua;		   //A相电压    0.1V
	WORD Ub;		   //B相电压	0.1V
	WORD Uc;		   //C相电压	0.1V
	WORD Ires;		   //剩余电流	mA
	WORD Ia[2];		   //A相电流	0.1A
	WORD Ib[2];		   //B相电流	0.1A
	WORD Ic[2];		   //C相电流	0.1A
	WORD dir[3];	   //电流方向
	WORD STA;		   //开关状态
	int16 Psum;		   //总有功功率		0.01kW
	int16 Pa;		   //A相有功功率P1	0.01kW
	int16 Pb;		   //B相有功功率P2	0.01kW
	int16 Pc;		   //C相有功功率P3	0.01kW
	int16 Qsum[4];	   //无功功率
	WORD Ssum[4];	   //视在功率
	int16 PF[4];	   //功率因素
	WORD GFa;		   //A相电网频率
	WORD GFb;		   //B相电网频率
	WORD GFc;		   //C相电网频率
	WORD Asymmetry[2]; //电流、电压不对称度
	int16 Tdev;		   //设备温度
	int16 Ta;		   //进线A相温度		0.1
	int16 Tb;		   //进线B相温度		0.1
	int16 Tc;		   //进行C相温度		0.1
	int16 Tn;		   //进线N相温度		0.1
} BasicMeterQuantity;

//事件记录查询
typedef struct
{
	WORD EventSum;	 //事件总次数
	WORD EventIndex; //查询索引
	WORD EventType;	 //事件类型
	WORD EventYear;	 //事件-年
	WORD EventMon;	 //事件-月
	WORD EventDay;	 //事件-日
	WORD EventHour;	 //事件-时
	WORD EventMin;	 //事件-分
	WORD EventSec;	 //事件-秒
	WORD EventInfo;	 //事件相位信息	Bit0:A相 | Bit1:B相 | Bit2:C相 | Bit3:电压 | Bit4:电流 | Bit5:漏电｜　
	WORD EventUa;	 //事件电压A		0.1V
	WORD EventUb;	 //事件电压B		0.1V
	WORD EventUc;	 //事件电压C		0.1V
	WORD EventIa[2]; //事件电流A		0.1V 
	WORD EventIb[2]; //事件电流B		0.1V
	WORD EventIc[2]; //事件电流C		0.1V
	WORD EventLeak;	 //事件漏电   		mA

} EventRecord;

//累计记录查询
typedef struct
{
	WORD TotalClear[2];			  //数据清零总次数
	WORD TotalTrip;				  //总跳闸次数
	WORD IresBlockTrip;			  //剩余电流闭锁跳闸次数
	WORD IresProtectTrip;		  //剩余电流保护跳闸次数
	WORD OverloadProtectTrip;	  //过载保护跳闸次数
	WORD OvervolProtectTrip;	  //过压保护跳闸次数
	WORD ManualTrip;			  //手动跳闸次数
	WORD ZeroMissProtectTrip;	  //缺零保护跳闸次数
	WORD TestTrip;				  //试验跳闸次数（远程、按键）
	WORD SCSTDTrip;				  //短路短延时跳闸次数
	WORD SCITrip;				  //短路瞬时跳闸次数
	WORD UndervolProtectTrip;	  //欠压保护跳闸次数
	WORD OpenPhaseProtectionTrip; //缺相保护跳闸次数
	WORD TotalRunTime[2];		  //运行时间总累计(分)
} CumulativeRecords;

typedef struct
{
	uint8 addr;				   //塑壳modbus地址
	uint32 breakid;			   //塑壳设备地址
	uint8 type;				   //设备类型
	WORD ep_net[2];			   //电能查询  正向有功总电能
	WORD time[2];			   //设置时控的参数
	WORD runflag;			   //设备在线标志位
	WORD runerrcount;		   //运行错误计数
	WORD icloudSTA;			   //更新到云端的塑壳状态
	WORD icloudEventSum;	   //更新到云端事件总数
	WORD icloudTime[2];		   //更新到云端时控参数
	WORD icloudrunflag;		   //更新到云端设备在线状态
	BasicMeterQuantity data;   //基础电量信息
	EventRecord event;		   //事件记录
	CumulativeRecords records; //累计事件记录
} MCCB_INFO;
void ReadBasicMeter(uint8 addr);
void ReadEvent(uint8 addr);
void ReadRecord(uint8 addr);
void ReadEnergy(uint8 addr);
uint8 MCCBCtrl(uint8 addr,uint8 cmd);
uint8 ReadSetTime(uint8 addr);
uint8 SetBeginTimeCtrl(uint8 addr,WORD settime);
uint8 SetEndTimeCtrl(uint8 addr,WORD settime);
uint8 SetTimeCtrl(uint8 addr,WORD *settime);
uint8 SetIndex(uint8 addr,uint8 index);
void MccbCom (void * paras);
uint8 SetState(uint8 addr,uint8 state);
int uart_received_mccb(char *rxbuf);
uint8 WriteSingleCoil(uint8 addr, uint16 RegisterAddr, uint16 data);
uint8 WriteSingleRegister(uint8 addr, uint16 RegisterAddr, uint16 data);
unsigned short MODBUSCRC(unsigned char *ptr, unsigned char size);
#endif
