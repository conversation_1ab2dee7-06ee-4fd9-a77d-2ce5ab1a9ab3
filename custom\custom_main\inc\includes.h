/*********************************************************
*  @file    includes.h
*  @brief   ML302 OpenCPU main header file
*  Copyright (c) 2023 Tengen IOT.
*  All rights reserved.
*  created by Delei 2023/03/27
********************************************************/
#ifndef __INCLUDES_H__
#define __INCLUDES_H__

#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include <string.h>
#include <math.h>

#include "cm_gpio.h"
#include "cm_uart.h"
#include "cm_iomux.h"
#include "cm_os.h"
#include "cm_fs.h"
#include "cm_mem.h"
#include "cm_sys.h"
#include "cm_sim.h"
#include "cm_virt_at.h"
#include "cm_rtc.h"
#include "cm_ntp.h"
#include "cm_modem.h"
#include "cm_adc.h"
#include "cm_pm.h"
#include "cm_fota.h"

#include "bsp_gpio.h"
#include "bsp_uart.h"
#include "bsp_network.h"
#include "bsp_files.h"
#include "custom_main.h"
#include "linkSDK4_0.h"
#include "memory_gw.h"
#include "modbus_gw.h"
#include "modbus_485.h"
#include "periodctrl.h"
#include "test.h"
#include "ds1302.h"
#include "mccb_collect.h"
#include "topic_mccb.h"
#include "topic_im20.h"
#include "memory_im20.h"
#include "modbus_485_poll.h"
#include "RS485_custom.h"
#include "power_dmp.h"

#include "aiot_state_api.h"
#include "aiot_sysdep_api.h"
#include "aiot_ota_api.h"
#include "aiot_mqtt_api.h"
#include "cJSON.h"


 #define DEVELOP_MOD     0   //开发的时候，为了避免每次下载固件都要写入三元组，需要使能该宏

#endif