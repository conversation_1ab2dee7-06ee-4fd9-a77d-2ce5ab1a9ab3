/*********************************************************
*  @file    ds1302.h
*  @brief   ML302 OpenCPU ds1302 driver file
*  Copyright (c) 2021 TENGEN.
*  All rights reserved.
*  created by Delei 2021/03/24
********************************************************/
#ifndef _DS1302_H_
#define _DS1302_H_

// #include "cm/cm_gpio.h"

// #define RTC_SCLK                (CM_GPIO_9)
// #define RTC_IO                  (CM_GPIO_22)
// #define RTC_RST                 (CM_GPIO_17)

void rtc1302_init(void);
void rtc_set_datetime(uint8_t day, uint8_t mth, uint8_t year, uint8_t dow, uint8_t hr, uint8_t min, uint8_t sec);
void rtc_get_date(uint8_t* day, uint8_t* mth, uint8_t* year);
void rtc_get_time(uint8_t* hr, uint8_t* min, uint8_t* sec);
void enable_gpio_in(void);
void enable_gpio_out(void);

uint64_t GetProAbsSubSecond(void);

void get_os_time(struct tm* clock);
void setRTCTime(struct tm* ptr);
void print_os_time(void);
osStatus_t print_RTC_time(void);
void sysTime2RTC(void);
void RTC2SysTime(void);
void getRTCtm(struct tm* outClock);

#endif // !_DS1302_H_
