/*********************************************************
*  @file    bsp_gpio.h
*  @brief   ML307 OpenCPU gpio file
*  Copyright (c) 2023 TENGEN.
*  All rights reserved.
*  created by Delei 2023/03/27
********************************************************/
#ifndef __BSP_UART_H__
#define __BSP_UART_H__

#include "cm_uart.h"

#define UART_BK             CM_UART_DEV_0
#define UART_MODBUS         CM_UART_DEV_1

#define OPENCPU_UARTTX0_IOMUX CM_IOMUX_PIN_18, CM_IOMUX_FUNC_FUNCTION1
#define OPENCPU_UARTRX0_IOMUX CM_IOMUX_PIN_17, CM_IOMUX_FUNC_FUNCTION1
#define OPENCPU_UARTTX1_IOMUX CM_IOMUX_PIN_28, CM_IOMUX_FUNC_FUNCTION1
#define OPENCPU_UARTRX1_IOMUX CM_IOMUX_PIN_29, CM_IOMUX_FUNC_FUNCTION1
// #define OPENCPU_UARTTX2_IOMUX CM_IOMUX_PIN_50, CM_IOMUX_FUNC_FUNCTION3//当前SDK的UART2不可用，只能用于打印log
// #define OPENCPU_UARTRX2_IOMUX CM_IOMUX_PIN_51, CM_IOMUX_FUNC_FUNCTION3

void bsp_uart_init(void);
void bsp_uart0_reInit(void);
void bsp_uart1_Init(int baudrate);
int sendDataToUart(cm_uart_dev_e id, unsigned char *buff, int len, uint32_t baudRate);
int readDataFromUart(cm_uart_dev_e uartId, uint8_t *data);
#endif