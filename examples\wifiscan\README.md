# WIFISCAN接口测试 简介 

## WIFISCAN介绍**
WIFISCAN定位是一种通过扫描WiFi信号来实现定位的技术。它利用无线局域网（WLAN）的普及性和覆盖性，通过捕获并分析WiFi接入点（AP）的信号特征，实现对目标区域的定位。
应用：WIFISCAN定位技术广泛应用于室内导航、位置服务等领域。它能够在复杂的室内环境中提供相对准确的定位信息，帮助用户快速找到目的地或获取所需的位置服务。

## **实现功能**
/*WIFISCAN可以获取周围wifi接入点的BSSID信息、信号强度等，本实例调用高德地图智能硬件定位V1.0,返回相关经纬度及位置信息
/*高德地图智能硬件定位指导文档*/
https://lbs.amap.com/api/webservice/guide/api-advanced/hardware-location

## **APP执行流程**
1、关闭模组射频、开启WIFISCAN扫描(模组射频和WIFISCAN共用一个射频通路，因此使用WIFISCAN时需关闭射频)
2、获取WIFISCAN扫描结果
3、将扫描结果进行HTTP组包
4、恢复网络
5、使用HTTP访问高德地图智能硬件定位V1.0 API 解析平台返回的定位结果*/

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 问题: 为什么WIFISCAN存在多次扫描的情况？
  答复：①因为模组在非IDLE状态，即网络处于ACTIVE态时，WIFI通路与射频通路共用，因此WIFISCAN不能扫描成功，本示例采用CFUN=5，关闭射频功能，保障WIFISCAN扫描成功。
      ②WIFISCAN扫描个数与周围AP热点有关，本示例中，当扫描设备数小于2,则不能应用于WIFISCAN高德定位。
      ③当WIFISCAN数等于大于2，也有可能存在高德平台无法定位的问题，具体以高德平台返回结果为准。


## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/11 18:08
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------