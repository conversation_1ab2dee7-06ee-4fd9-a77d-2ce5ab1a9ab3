# 简易操作系统OS接口简介 

## **实时操作RTOS介绍**
实时操作系统（RTOS）是一种专为实时应用程序设计的操作系统。实时应用程序需要在特定时间内做出预测的响应，因此RTOS专注于提供对时间约束的强调，以确保系统能够满足实时性能要求。
操作系统一般包含如下功能：
- 多任务支持
- 任务调度
- 实时性能
- 内存管理
- 通信机制（如消息队列、信号量、互斥量）


## **实现功能**
实现操作系统中常用的线程、信号量、定时器、消息队列创建、回调触发功能，包括以下子功能：
1. 创建一个线程，永久等待获取信号量，信号量获取后，最多等待2S查看消息队列中是否有收到消息
2. 创建一个信号量，在定时器到达时回调函数中释放，使线程得以获取到该信号量，实现操作系统中的通信
3. 创建一个定时器，周期性定时，每30S触发一次，并在定时器到达时回调函数中释放信号量和发送消息至消息队列中。
4. 创建一个消息队列，在定时器到达时回调函数中发送一条数据，使线程可以从消息队列中收到该消息，实现操作系统中的通信。

## **APP执行流程**
1. 设备上电，等待os task start success 线程创建完毕
2. 等待30S，定时器到达第一次触发
3. 定时器回调中释放信号量，并发送一条消息至消息队列中
4. 线程获取信号量，打印获取信号量的次数；获取到消息队列中的消息，打印获取到的
5. 继续等待30S，定时器到达第二次触发，程序周期运行。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：操作系统中的osTimer接口属于软件定时器，非硬件定时器，掉电不运行。
- APP使用前提：无

## **FAQ**
- 问题：为什么定时器中加入打印函数会死机？
  答复：操作系统中的定时器属于软件定时器，在回调中应尽可能地快速退出回调，不可以加入任何耗时操作，如：打印，延时，阻塞，复杂计算等操作，建议使用信号量和消息队列来实现定时器事件到达的通知，注意osMessageQueuePut中的timeout应该配置为0。

- 问题：创建线程时的stack值应该如何设置？
  答复：该stack称为栈，内存分为栈和堆两种类型，在线程中所开辟的局部变量，函数递归调用都需要占用该栈空间，当内存超出时会导致内存溢出模组死机，因此用户需对线程所使用内存有大致评估，
  若遇到疑似内存栈溢出死机，可适当增加该参数。

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/5 17:30
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------