# 简易OneNET上报温度APP 简介 

## **OneNET平台及NTP协议介绍**
认识OneNET：中国移动物联网开放平台是中移物联网有限公司基于物联网技术和产业特点打造的开放平台和生态环境，适配各种网络环境和协议类型，支持各类传感器和智能硬件的快速接入和大数据服务，提供丰富的API和应用模板以支持各类行业应用和智能硬件的开发，能够有效降低物联网应用开发和部署成本，满足物联网领域设备连接、协议适配、数据存储、数据安全、大数据分析等平台级服务需求。

更多OneNET资料获取请访问OneNET门户网站 [OneNET门户网站](https://open.iot.10086.cn/)

网络时间协议，英文名称：Network Time Protocol（NTP）是用来使计算机时间同步化的一种协议，它可以使计算机对其服务器或时钟源（如石英钟，GPS等等)做同步化。NTP基于UDP报文进行传输，使用的UDP端口号为123。


## **实现功能**
实现模组定时采集本地信息并上报OneNET平台功能，包括以下子功能：
1. 网络检测与NTP授时，实现开机后检测网络连接、从NTP服务器获取时间功能
2. ADC采集，使用ADC获取温度传感器电压并转换为温度值功能
3. LwM2M协议功能，使用LwM2M协议连接OneNET平台（模组设备需先在OneNET平台网页侧完成产品与设备的创建），定时上报温度值，并应答OneNET平台的读温度请求

## **APP执行流程**
1. 设备上电，等待PDP激活；
2. NTP授时请求；
3. 登陆OneNET平台及订阅所需资源；
4. 每分钟上报温度数据（亦可每分钟进行NTP校准时间，该段代码默认注释）。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：（非必要，ADC0需与温度传感器连接，是否连接不影响APP程序运行）
- 使用注意事项：开发人员使用前需实现掌握OneNET基础概念及其网页侧操作，参见OneNET门户网站 [OneNET门户网站](https://open.iot.10086.cn/)
- APP使用前提：开发板、SIM卡（APP需要上网）、OneNET账号（模组设备需先在OneNET平台网页侧完成产品与设备的创建，模组IMEI确保与OneNET平台上的产品、设备信息一致，[OneNET产品创建](https://open.iot.10086.cn/console/product/own)）

## **FAQ**
- 问题：为什么NTP经常失败？
  答复：通常情况下NTP成功率可能与网络状况和使用时间服务器有关。根据NTP协议要求，NTP基于UDP报文进行传输，UDP为无连接的协议，不提供可靠性的传输。正如UDP协议特性，NTP存在一定程度失败的可能。但在网络良好的情况下，NTP成功率较高，如果出现经常性的NTP失败情况，通常与服务器未应答模组的NTP时间请求导致（可能原因为客户使用公共时间服务器，该时间段该服务器上存在大量的并发请求，处理能力不足导致未及时响应模组的NTP请求）。客户在NTP应用开发时，可采用错误重试、定时进行NTP服务等多次进行NTP请求的策略来保证能够获取到时间，或选用和连接更加稳定的时间服务器来提高获取时间的成功率。

- 问题：OneNET平台创建产品时，接入协议和数据协议如何选择？
  答复：本APP DEMO实现LwM2M协议接入OneNET平台，接入协议应选择LwM2M。数据协议中IPSO为通用性最强的标准协议，本APP DEMO代码中按照IPSO标准设计，数据协议应选IPSO。除IPSO标准数据协议外，其他数据协议可能为OneNET平台或其他第三方专有协议，客户有使用需求需要从OneNET平台上获取相应协议的资料。

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/1 10:30
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------