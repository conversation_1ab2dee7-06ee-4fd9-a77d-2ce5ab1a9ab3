# 播放PWM模拟音频应用 简介 

## **音频功能介绍**

### **音频编码格式**
通信模组中使用的音频功能通常包括播放和录音。其中，播放和录音的音频格式通常为数字音频信号流PCM数据，但也存在模拟信号的应用场景。
通信模组通常均支持PCM数据的播放和录音，同时，不同平台会集成一些常见的音频编解码功能，例如amr、mp3等。当模组内部集成这些编解码功能后，相应的音频格式也能够支持。
以ML307A系列模组为例，平台底层集成了amr编解码库和mp3解码库，支持pcm与amr两种编码格式的互转和mp3向pcm编码格式的解码，故ML307A系列模组能够支持amr音频的播放、录音功能和mp3音频的播放功能（不支持mp3的录音功能）。
以ML307R系列模组为例，平台底层集成了amr编解码库和mp3解码库，支持pcm与amr两种编码格式的互转和mp3向pcm编码格式的解码，故ML307R系列模组支持相应编解码格式的编解码能力。
常见的wav格式的音频文件，即为以wav文件格式封装的PCM数据（包括wav文件头和PCM数据），本质仍为PCM数据。因其未应用编解码技术进行压缩，使得wav文件相对于其他格式的音频文件要大很多。对于内存吃紧的通信模组，不建议客户使用wav格式。
另外，音频播放录音的实际效果与硬件设计、终端设备结构设计、扬声器和麦克风选型、外接PA或降噪芯片等均有关，通常客户在完成软硬件开发和结构定版后需对音频参数进行校准（使用的音频CODEC芯片支持音频参数调试的情况下）。

### **PWM模拟音频数据**
对于内部未集成音频芯片的模组，其自身不具备音频能力，但可以使用PWM口输出模拟音频数据信号。该方案下，通常模组内部会集成PCM数据转PWM模拟信号的算法库，将原始音频PCM数据（或AMR/MP3等数据解码得到的PCM数据）转换成PWM数据，通过PWM接口输出。
PWM模拟音频数据方案下，因为PWM驱动扬声器能力较弱，该方式下直接驱动扬声器时播放音量极小，通常需要配合音频PA使用。
PWM模拟音频数据方案，可用于没有录音需求且对音质需求不高的场景。

### **PCM数字音频数据**
对于内部未集成音频芯片的模组，其自身不具备音频能力，但可以进行PCM数字音频数据的通信。该方案下，用户可通过模组PCM硬件接口与外置的音频芯片进行PCM数据的双向通信。
播放音频方案：模组将应用写入的原始音频PCM数据（或AMR/MP3等数据解码得到的PCM数据）通过PCM硬件接口发送给外置的音频芯片，由外置的音频芯片进行播放。
录制音频方案：外置的音频芯片将录音所得的原始音频PCM数据通过PCM硬件接口发送给模组，模组按照应用设置的格式，将原始音频PCM数据（或经过编码后的AMR格式数据）回调给应用。

## **APP实现功能**
实现播放PWM模拟音频应用功能，包括以下子功能：写入待播放的音频文件、播放AMR文件、播放MP3文件、播放WAV文件、流播放原始PCM音频数据。
1. 写入待播放的音频文件至文件系统
2. 播放文件系统中的AMR文件
3. 播放文件系统中的MP3文件
4. 读取文件系统中的WAV文件
5. 流播放PCM数据

## **APP执行流程**
1. 设备上电，执行主任务`audio_pwm_demo_appimg_enter`
2. 写入待播放的音频文件至文件系统
3. 播放文件系统中的AMR文件
4. 播放文件系统中的MP3文件
5. 读取文件系统中的WAV文件，并通过WAV文件头获取WAV文件中打包的PCM数据的参数，流播放其中的PCM数据
6. 文件系统播放完成后，流播放另一份PCM数据
7. 应用写入的PCM数据即将播完时，继续向播放器写入待播放数据
8. 持续进行步骤7的操作

## **使用说明**
- 支持的模组（子）型号：ML307R-DC/ML307C-DC-CN
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.版本及其后续版本
- 是否需要外设支撑：须外接扬声器，建议外接音频PA芯片（PWM驱动扬声器能力较弱，该方式下直接驱动扬声器时播放音量极小，通常需要配合音频PA使用）
- 使用注意事项：若未外接音频PA芯片，无法驱动大功率的扬声器，驱动小功率的扬声器时播放的音量极小

## **FAQ**
- 问题：为什么有的模组只支持播放功能，不支持录音功能？
  答复：模组对音频功能的支持可能和内存空间大小、是否内置有音频CODEC芯片有关，因产品和功能设计而有差异。对于内存空间较大的产品，通常支持的音频功能较丰富，反之则较少（存在只支持播放不支持录音的可能）。对于模组内置有音频CODEC芯片的产品，在内存空间足够的情况下，理论上是能够支持播放和录音功能的。对于模组内部无音频CODEC芯片，外部也未外挂音频CODEC芯片的产品，录音功能是不可实现的，但可以通过PWM功能输出模拟音频数据的方式驱动扬声器实现音频播放的功能。对于模组内部无音频CODEC芯片，外部外挂音频DAC芯片的产品，因外挂的音频DAC芯片具备音频数字信号转模组信号的功能，能够驱动扬声器，可以支持音频播放功能但无法支持录音功能。对于模组内部无音频CODEC芯片，外部外挂音频CODEC芯片的产品，可以支持音频播放和录音功能。

- 问题：使用音频功能过程中，为什么会遇到爆破音的情况？
  答复：除播放的音频数据中本身就是爆破音的原因外，爆破音的产品通常由硬件电路产生。出现爆破音问题时，需抓取播放定位爆破音的产生原因并进行优化。例如，打开PA或音频芯片或其他外置设备的控制等操作引入的电路上电压的跳变，此电压跳变经PA放大后被扬声器播放导致的爆破音。在硬件电路无法进一步优化时，也可采用软件规避的方式处理，比如在可能出现爆破音的操作时不开启PA，使得爆破音不被PA放大，这样就不会传入到人耳中。


## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 10:26
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/11/11 11:13
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------