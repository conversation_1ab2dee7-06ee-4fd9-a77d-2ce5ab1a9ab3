/*
 * @FilePath: \ml307_dcln\custom\custom_main\inc\topic_custom.h
 * @Description: 
 * @Author: ji<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-06-19 08:55:40
 * @LastEditors: jiawenjie <EMAIL>
 * @LastEditTime: 2023-07-20 10:28:28
 * 
 * Copyright (c) 2023 by Zhejiang Tengen Electric Co., Ltd., All Rights Reserved. 
 */

#ifndef _TOPIC_CUSTOM_H_
#define _TOPIC_CUSTOM_H_

/*----------------------------------------------------------------------------*
 **                             Dependencies                                   *
 **----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------*
**                             Mcaro Definitions                              *
**----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------*
**                             Data Structures                                *
**----------------------------------------------------------------------------*/
/**
 *   
*/


typedef enum
{
  TOPIC_CUSTOM_MQTTTYPE_PUB = 0,
  TOPIC_CUSTOM_MQTTTYPE_SUB,
} topic_custom_mqtttype_e;


typedef enum
{
  TOPIC_CUSTOM_PRODUCT_NONE = 0,
  TOPIC_CUSTOM_PRODUCT_RANGNENG,
  TOPIC_CUSTOM_PRODUCT_COGIOT,
  TOPIC_CUSTOM_PRODUCT_SIFANG,
  TOPIC_CUSTOM_PRODUCT_PHILIPS,
  TOPIC_CUSTOM_PRODUCT_EJLCHINA,
} topic_custom_product_e;

typedef enum
{
  TOPIC_CUSTOM_SUB_OTA_TOPIC_SUBDEV = 0,
  TOPIC_CUSTOM_SUB_OTA_TOPIC_GATEWAY,
  TOPIC_CUSTOM_SUB_SET_PARA,
  TOPIC_CUSTOM_SUB_CTRL,
  TOPIC_CUSTOM_SUB_LEAK_CHECK,
  TOPIC_CUSTOM_SUB_LOCAL_TIMER_CTRL,
  TOPIC_CUSTOM_SUB_LOCAL_TIMER_LC,
  TOPIC_CUSTOM_SUB_GATEWAY_CTRL,
  TOPIC_CUSTOM_SUB_PERIOD_CTRL,
  TOPIC_CUSTOM_SUB_GATEWAY_PRI_CTRL,
  /////////////////////////////////////// SIFANG
  TOPIC_CUSTOM_SUB_CHECK,
  TOPIC_CUSTOM_SUB_TIMETAB,
  TOPIC_CUSTOM_SUB_TIMETABNO,
  TOPIC_CUSTOM_SUB_GETHISTORY,
  TOPIC_CUSTOM_SUB_SETSERVER,
  TOPIC_CUSTOM_SUB_HEART,
  TOPIC_CUSTOM_SUB_POWERDMP,
  TOPIC_CUSTOM_SUB_LAST
} topic_custom_subtable_e;


typedef enum
{
  TOPIC_CUSTOM_PUB_USER_GATEWAY = 0,  //网关信号上报
  TOPIC_CUSTOM_PUB_USER_PERIOD,/// 向云端反馈一次微断的周期控制开关状态
  TOPIC_CUSTOM_PUB_USER_GATEWAY_PERIOD,//网关控制时段上报
  TOPIC_CUSTOM_PUB_USER_PARA,///子设备参数上报
  TOPIC_CUSTOM_PUB_USER_DATA,    //子设备数据及变化上报
  TOPIC_CUSTOM_PUB_USER_CHANGDATA,//子设备参数变化上报
  TOPIC_CUSTOM_PUB_USER_CHANGPARA,//子设备参数变化上报
  TOPIC_CUSTOM_PUB_USER_ONLINE, //子设备上线下线上报
  TOPIC_CUSTOM_PUB_USER_ALARM,//子设备告警
  TOPIC_CUSTOM_PUB_USER_GATEWAY_LOG, //网关日志上报
  TOPIC_CUSTOM_PUB_USER_DEVICE,//子微断版本上报
  TOPIC_CUSTOM_PUB_USER_LOCALTIMER_TASK,//子设备定时任务上报
  TOPIC_CUSTOM_PUB_USER_GATEWAYDEVICE,// 网关版本上报
  TOPIC_CUSTOM_PUB_USER_GATEWAYCHECK,//网关设备自检结果上报
  TOPIC_CUSTOM_PUB_USER_TIMETAB,//单个微断时间表上传
  TOPIC_CUSTOM_PUB_USER_TIMENO,//单个微断屏蔽时间表上传
  TOPIC_CUSTOM_PUB_USER_ONEKEYCTRL,//现场一键分合闸动作上报
  TOPIC_CUSTOM_PUB_USER_HISTORY,//分合闸历史纪录上报
  TOPIC_CUSTOM_PUB_USER_GATEWAYOTA,//网关设备ota升级进度
  TOPIC_CUSTOM_PUB_USER_SUBOTA,//子设备ota升级进度
  TOPIC_CUSTOM_PUB_USER_MCCB_EVENT,//MCCB EVENT
  TOPIC_CUSTOM_PUB_USER_MCCB_RECORD,//MCCB RECORD
  TOPIC_CUSTOM_PUB_USER_IM20,//IM20
  TOPIC_CUSTOM_PUB_USER_HEART,
  TOPIC_CUSTOM_PUB_LAST
} topic_custom_pubtable_e;


typedef struct
{
 char use;
 topic_custom_subtable_e subtype;
 char* topic;
 aiot_mqtt_recv_handler_t recv_function;
}mqtt_custom_subtopic_t;



typedef struct
{
 char use;
 char qos;
 topic_custom_pubtable_e pubtype;
 char* topic;
}mqtt_custom_pubtopic_t;

typedef struct
{
    char* name;
    char* topicdetail;
    topic_custom_subtable_e subtype;
    topic_custom_product_e product;
}mqtt_subtopic;


typedef struct
{
    char* name;
    char* topicdetail;
    int Qos;
    topic_custom_pubtable_e pubtype;
    topic_custom_product_e product;
}mqtt_pubtopic;


typedef struct 
{
  char *host;
  uint16_t port;
  char *product_key;
  char *device_name;
  char *device_secret;
  char *username;
  char *password;
  char *clientid;
  char *extend_clientid;
  char *security_mode;
}mqtt_con_info;


/*----------------------------------------------------------------------------*
**                             Function Define                                *
**----------------------------------------------------------------------------*/

void InitTopicCustom();
void ExitTopicCustom();

int GetSubTopicNum();
int GetPubTopicNum();

topic_custom_product_e GetCurrentProduct(void);
mqtt_custom_subtopic_t * GetSubTopics();

int GetSubInfo(char * subtopic,topic_custom_subtable_e subtype);
int GetPubInfo(char * pubtopic,int *qos,topic_custom_pubtable_e pubtype);

void GetDevicesMqttInfo(mqtt_con_info * mqttinfo);
#endif // !_TOPIC_CUSTOM_H_
