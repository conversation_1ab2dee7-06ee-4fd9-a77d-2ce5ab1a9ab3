# 简易录音机APP 简介 

## **音频和TTS功能介绍**

### **音频功能**
通信模组中使用的音频功能通常包括播放和录音。其中，播放和录音的音频格式通常为数字音频信号流PCM数据。
通信模组通常均支持PCM数据的播放和录音，同时，不同平台会集成一些常见的音频编解码功能，例如amr、mp3等。当模组内部集成这些编解码功能后，相应的音频格式也能够支持。
以ML307A系列模组为例，平台底层集成了amr编解码库和mp3解码库，支持pcm与amr两种编码格式的互转和mp3向pcm编码格式的解码，故ML307A系列模组能够支持amr音频的播放、录音功能和mp3音频的播放功能（不支持mp3的录音功能）。
常见的wav格式的音频文件，即为以wav文件格式封装的PCM数据（包括wav文件头和PCM数据），本质仍为PCM数据。因其未应用编解码技术进行压缩，使得wav文件相对于其他格式的音频文件要大很多。对于内存吃紧的通信模组，不建议客户使用wav格式。
另外，音频播放录音的实际效果与硬件设计、终端设备结构设计、扬声器和麦克风选型、外接PA或降噪芯片等均有关，通常客户在完成软硬件开发和结构定版后需对音频参数进行校准（使用的音频CODEC芯片支持音频参数调试的情况下）。

### **TTS功能**
TTS（Text To Speech），即文本转语音，用于实现某些编码格式下的文本数据向音频PCM数据的转换。
通常情况下，TTS功能需要内置转码算法库文件，该算法库通常较大（且支持的文本编码格式或文字库越多，其占用的代码空间越大）。以ML307A系列模组为例，TTS转码库的大小超过700kb。

## **APP实现功能**
实现简易录音机功能，包括以下子功能：TTS转码、TTS播放、录音并保存为WAV文件、录音并保存为AMR文件、WAV文件头解析、流播放、文件系统播放。
1. TTS转码，实现中文文本转音频PCM数据的功能
2. TTS播放，实现播放由中文文本转换而成的音频PCM数据的功能
3. 录音为wav文件，实现录音PCM原始音频数据并将其以wav格式封装为wav文件保存在文件系统的功能
4. 录音为AMR文件，实现录音AMR音频数据并将其以amr文件的封装保存在文件系统的功能
5. wav文件头解析，实现从wav文件头信息中获取PCM原始音频数据的参数信息的功能
6. 流播放，采用流播放的方式播放音频数据
7. 文件系统播放，采样文件系统播放的方式播放音频文件

## **APP执行流程**
1. 设备上电，执行主任务`recorder_demo_appimg_enter`
2. 设置播放器音量为100
3. 初始化离线TTS
4. 常用中文文本进行TTS转码，转码音频数据本地保存
5. 播放录音WAV文件语音提示
6. 等待录音完成
7. 播放录制的WAV文件
8. 播放录音AMR文件语音提示
9. 等待录音完成
10. 播放录制的AMR文件
11. 循环执行步骤5~步骤10

## **使用说明**
- 支持的模组（子）型号：ML307A-DSLN TTS版本模组
- 支持的SDK版本：ML307A OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：需要将模组SPK引脚与外部扬声器连接，需要将模组MIC引脚与外部麦克风连接
- 使用注意事项：仅适用于TTS版本模组，非TTS版本模组上执行时TTS初始化会返回错误

## **FAQ**
- 问题：为什么有的模组只支持播放功能，不支持录音功能？
  答复：模组对音频功能的支持可能和内存空间大小、是否内置有音频CODEC芯片有关，因产品和功能设计而有差异。对于内存空间较大的产品，通常支持的音频功能较丰富，反之则较少（存在只支持播放不支持录音的可能）。对于模组内置有音频CODEC芯片的产品，在内存空间足够的情况下，理论上是能够支持播放和录音功能的。对于模组内部无音频CODEC芯片，外部也未外挂音频CODEC芯片的产品，录音功能是不可实现的，但可以通过PWM功能输出模拟音频数据的方式驱动扬声器实现音频播放的功能。对于模组内部无音频CODEC芯片，外部外挂音频DAC芯片的产品，因外挂的音频DAC芯片具备音频数字信号转模组信号的功能，能够驱动扬声器，可以支持音频播放功能但无法支持录音功能。对于模组内部无音频CODEC芯片，外部外挂音频CODEC芯片的产品，可以支持音频播放和录音功能。

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/10/18 09:43
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------