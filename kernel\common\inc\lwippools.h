#include "lwipopts.h"

#if (LWIP_POOL_SIZE_TYPE == 3) //for falcon and kestrel
LWIP_MALLOC_MEMPOOL_START
LWIP_MALLOC_MEMPOOL(512,  32)
LWIP_MALLOC_MEMPOOL(512,  64)
LWIP_MALLOC_MEMPOOL(1024,  128)

LWIP_MALLOC_MEMPOOL(512,  256)
LWIP_MALLOC_MEMPOOL(512,  512)
LWIP_MALLOC_MEMPOOL(256,  1024)

LWIP_MALLOC_MEMPOOL(128, 1800)
LWIP_MALLOC_MEMPOOL_END

#elif (LWIP_POOL_SIZE_TYPE == 2) //for nbiot
LWIP_MALLOC_MEMPOOL_START
LWIP_MALLOC_MEMPOOL(32,  32)
LWIP_MALLOC_MEMPOOL(64,  64)
LWIP_MALLOC_MEMPOOL(64,  128)

LWIP_MALLOC_MEMPOOL(32,  256)
LWIP_MALLOC_MEMPOOL(8,  512)
LWIP_MALLOC_MEMPOOL(8,  1024)

LWIP_MALLOC_MEMPOOL(0, 1800)
LWIP_MALLOC_MEMPOOL_END

#else //default
LWIP_MALLOC_MEMPOOL_START
LWIP_MALLOC_MEMPOOL(32,  32)
LWIP_MALLOC_MEMPOOL(64,  64)
LWIP_MALLOC_MEMPOOL(512,  128)

LWIP_MALLOC_MEMPOOL(128,  256)
LWIP_MALLOC_MEMPOOL(64,  512)
LWIP_MALLOC_MEMPOOL(16,  1024)

LWIP_MALLOC_MEMPOOL(16, 1800)
LWIP_MALLOC_MEMPOOL_END

#endif

