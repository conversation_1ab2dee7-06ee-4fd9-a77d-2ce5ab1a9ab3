/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

#include "acm_calibration.h"
#include "audio_def.h"

#define ASR_AUDIO_MAIN_VERSION   (0x3)   
#define ASR_AUDIO_SUB_VERSION    (0x0)    

//#define ASR_AUDIO_NVM_STRIP

/********************************************************************************************************
	ACMCodec_GainT.CodecControl defines board specific configuration:
	bit 0 : mic selection
	bit 1 : sidetone selection


	On ASR reference board:
	RCV use mic2
	HS use mic1
	Disable codec sidetone

	Customers may change this usage on their board design, so they could change this to match their board.
*********************************************************************************************************/
#define ASR_BOARD_CODEC_SETTING_MIC1 					\
	(  (ACMCODEC_MIC1 << ACMCODEC_CONTROL_BITS_MIC) 	\
	 | (0 << ACMCODEC_CONTROL_BITS_SIDETONE)			\
	)

#define ASR_BOARD_CODEC_SETTING_MIC2 					\
	(  (ACMCODEC_MIC2 << ACMCODEC_CONTROL_BITS_MIC) 	\
	 | (0 << ACMCODEC_CONTROL_BITS_SIDETONE)			\
	)


/********************************************************************************************************
	ACMDSP_VEParametersT.VoiceControl defines board specific configuration:
	bit 0 :  Tx EC
	bit 1 :  Tx Dual-Mic
	bit 2 :  Tx NS
	bit 3 :  TX EQ
	bit 4 :  TX AVC
	bit 5 :  TX Volume
	bit 6 :  reserved
	bit 7 :  reserved
	bit 8 :  Rx NS
	bit 9 :  Rx EQ
	bit 10: Rx Boost
	bit 11: Rx BiQuadIIR
	bit 12: Rx Rx Volume
	bit 13: Rx sidetone
	bit 14: reserved
	bit 15: reserved


	On ASR reference board:
	Disable dual-mic
	Disable sidetone
	Disable slowvoice
	Enable others

	Customers may change this to match their calibration result.
*********************************************************************************************************/	
#define ASR_BOARD_DSP_SETTING 							\
	( (1 << ACMDSP_CONTROL_BITS_TX_EC)					\
	| (0 << ACMDSP_CONTROL_BITS_TX_DUALMIC)				\
	| (1 << ACMDSP_CONTROL_BITS_TX_NS)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_EQ)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_AVC)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_VOLUME)				\
	| (1 << ACMDSP_CONTROL_BITS_RESERVED6)				\
	| (1 << ACMDSP_CONTROL_BITS_RESERVED7)				\
	| (1 << ACMDSP_CONTROL_BITS_RX_NS)					\
	| (1 << ACMDSP_CONTROL_BITS_RX_EQ)					\
	| (1 << ACMDSP_CONTROL_BITS_RX_BOOST)				\
	| (1 << ACMDSP_CONTROL_BITS_RX_BIQUADIIR)			\
	| (1 << ACMDSP_CONTROL_BITS_RX_VOLUME)				\
	| (0 << ACMDSP_CONTROL_BITS_RX_SIDETONE)			\
	| (0 << ACMDSP_CONTROL_BITS_RX_SLOWVOICE)			\
	| (0 << ACMDSP_CONTROL_BITS_MP3_MODE)				\
	)


// 48k 2 channel mp3 play buffer is mixed  after VE, so it cannot use VE
#define ASR_BOARD_DSP_AUDIO_SETTING 							\
	( (0 << ACMDSP_CONTROL_BITS_TX_EC)					\
	| (0 << ACMDSP_CONTROL_BITS_TX_DUALMIC)				\
	| (1 << ACMDSP_CONTROL_BITS_TX_NS)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_EQ)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_AVC)					\
	| (1 << ACMDSP_CONTROL_BITS_TX_VOLUME)				\
	| (1 << ACMDSP_CONTROL_BITS_RESERVED6)				\
	| (1 << ACMDSP_CONTROL_BITS_RESERVED7)				\
	| (0 << ACMDSP_CONTROL_BITS_RX_NS)					\
	| (0 << ACMDSP_CONTROL_BITS_RX_EQ)					\
	| (0 << ACMDSP_CONTROL_BITS_RX_BOOST)				\
	| (0 << ACMDSP_CONTROL_BITS_RX_BIQUADIIR)			\
	| (1 << ACMDSP_CONTROL_BITS_RX_VOLUME)				\
	| (0 << ACMDSP_CONTROL_BITS_RX_SIDETONE)			\
	| (0 << ACMDSP_CONTROL_BITS_RX_SLOWVOICE)			\
	| (0 << ACMDSP_CONTROL_BITS_MP3_MODE)				\
	)
    
#define ASR_BOARD_DSP_BT_SETTING 							\
        ( (0 << ACMDSP_CONTROL_BITS_TX_EC)                  \
        | (0 << ACMDSP_CONTROL_BITS_TX_DUALMIC)             \
        | (0 << ACMDSP_CONTROL_BITS_TX_NS)                  \
        | (0 << ACMDSP_CONTROL_BITS_TX_EQ)                  \
        | (0 << ACMDSP_CONTROL_BITS_TX_AVC)                 \
        | (1 << ACMDSP_CONTROL_BITS_TX_VOLUME)              \
        | (0 << ACMDSP_CONTROL_BITS_RESERVED6)              \
        | (0 << ACMDSP_CONTROL_BITS_RESERVED7)              \
        | (0 << ACMDSP_CONTROL_BITS_RX_NS)                  \
        | (0 << ACMDSP_CONTROL_BITS_RX_EQ)                  \
        | (0 << ACMDSP_CONTROL_BITS_RX_BOOST)               \
        | (0 << ACMDSP_CONTROL_BITS_RX_BIQUADIIR)           \
        | (1 << ACMDSP_CONTROL_BITS_RX_VOLUME)              \
        | (0 << ACMDSP_CONTROL_BITS_RX_SIDETONE)            \
        | (0 << ACMDSP_CONTROL_BITS_RX_SLOWVOICE)           \
        | (0 << ACMDSP_CONTROL_BITS_MP3_MODE)             \
        )



#if CONFIG_AUDIO_DSP_OFF == 0
const ACMCodec_GainT g_AudioDefaultGainTable[] = 
{
	/*************************************************************************************
			Voice scenario:Use Codec sidetone and no DSP sidetone
	**************************************************************************************/
	{
		VC_HANDSET,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M24dB	, ACMCODEC_SIDETONE_GAIN_M8dB, 0, 0},		//ACMCodec_GainInT  Tx_CodecGain
		0,							//signed short      Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_3dB	, ACMCODEC_CLASSG_MODE_0_9V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_3dB	, ACMCODEC_CLASSG_MODE_0_9V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_3dB	, ACMCODEC_CLASSG_MODE_0_9V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_3dB	, ACMCODEC_CLASSG_MODE_0_9V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_3dB	, ACMCODEC_CLASSG_MODE_0_9V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB		, ACMCODEC_RCV_GAIN_3dB		, ACMCODEC_CLASSG_MODE_1_8V	, ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 5
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -36, -32, -28, -24, -20, -16, -12, -8, -4, 0, 0},
		0xFFFF						//signed short      Rx_DSPSideToneGain
	}, //VC_HANDSET
	
#ifndef ASR_AUDIO_NVM_STRIP	
	{
		VC_HANDSFREE,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M8dB	, ACMCODEC_SIDETONE_GAIN_M8dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -36, -32, -28, -24, -20, -16, -12, -8, -4, 0, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //VC_HANDSFREE
	
	{
		VC_HEADSET,
		ASR_BOARD_CODEC_SETTING_MIC1,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M24dB	, ACMCODEC_SIDETONE_GAIN_M8dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_M6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -36, -32, -28, -24, -20, -16, -12, -8, -4, 0, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //VC_HEADSET
	
	{
		VC_HEADPHONE,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M18dB	, ACMCODEC_SIDETONE_GAIN_M8dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //VC_HEADPHONE

	{
		VC_BT,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_0dB	, ACMCODEC_PGA_STAGE2_0dB	, ACMCODEC_ADC_DIGGAIN_0dB	, ACMCODEC_SIDETONE_GAIN_M8dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_M24dB, ACMCODEC_DAC_GAIN_M6dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_M18dB, ACMCODEC_DAC_GAIN_M6dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_M12dB, ACMCODEC_DAC_GAIN_M3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_M6dB , ACMCODEC_DAC_GAIN_M3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_M2dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_0dB 	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_2dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_0dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_6dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_12dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_18dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -30, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //VC_BT	

	/*************************************************************************************
			Audio scenario: No Codec sidetone and no DSP sidetone
	**************************************************************************************/
	{
		AC_HANDSET,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M24dB	, ACMCODEC_SIDETONE_GAIN_BYPASS, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
        //ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
        {{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_M3dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
         {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_M3dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
         {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M3dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M3dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_M6dB     , ACMCODEC_RCV_GAIN_0dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
        },

		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //AC_HANDSET
	
	{
		AC_HANDSFREE,
		ASR_BOARD_CODEC_SETTING_MIC2,
        {ACMCODEC_PGA_STAGE1_18dB   , ACMCODEC_PGA_STAGE2_6dB   , ACMCODEC_ADC_DIGGAIN_M24dB    , ACMCODEC_SIDETONE_GAIN_BYPASS, 0, 0},     //ACMCodec_GainInT  Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //AC_HANDSFREE
	
	{
		AC_HEADSET,
		ASR_BOARD_CODEC_SETTING_MIC1,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M24dB	, ACMCODEC_SIDETONE_GAIN_M10dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_22dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //AC_HEADSET
	
	{
		AC_HEADPHONE,
		ASR_BOARD_CODEC_SETTING_MIC2,
        {ACMCODEC_PGA_STAGE1_18dB   , ACMCODEC_PGA_STAGE2_6dB   , ACMCODEC_ADC_DIGGAIN_M24dB    , ACMCODEC_SIDETONE_GAIN_BYPASS, 0, 0},      //ACMCodec_GainInT  Tx_CodecGain
        0,                      //signed short      Tx_DSPGain
        //ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
        {{ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_M3dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
         {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_M6dB    , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
         {ACMCODEC_DAC_DIGGAIN_22dB, ACMCODEC_DAC_GAIN_0dB  , ACMCODEC_RCV_GAIN_0dB , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB , ACMCODEC_RCV_GAIN_0dB , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_3dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_3dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
         {ACMCODEC_DAC_DIGGAIN_22dB , ACMCODEC_DAC_GAIN_0dB     , ACMCODEC_RCV_GAIN_6dB     , ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
        },
        //signed short    Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
        {-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},

		0xFFFF						//signed short		Rx_DSPSideToneGain
	}, //AC_HEADPHONE
	
	{
		AC_BT,
		ASR_BOARD_CODEC_SETTING_MIC2,
		{ACMCODEC_PGA_STAGE1_0dB	, ACMCODEC_PGA_STAGE2_0dB	, ACMCODEC_ADC_DIGGAIN_0dB	, ACMCODEC_SIDETONE_GAIN_BYPASS, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_M24dB, ACMCODEC_DAC_GAIN_M6dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_M18dB, ACMCODEC_DAC_GAIN_M6dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_M12dB, ACMCODEC_DAC_GAIN_M3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_M6dB , ACMCODEC_DAC_GAIN_M3dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_M2dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_0dB 	, ACMCODEC_CLASSG_MODE_0_9V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_2dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_0dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_6dB	, ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_12dB , ACMCODEC_DAC_GAIN_3dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_18dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_24dB , ACMCODEC_DAC_GAIN_6dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-24, -20, -16, -12, -8, -4, 0, 4, 8, 12, 12, 12},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	} //AC_BT
	
#ifdef CRANEGT_DDR  
	,{
		AC_FM,
		ASR_BOARD_CODEC_SETTING_MIC1,
		{ACMCODEC_PGA_STAGE1_18dB	, ACMCODEC_PGA_STAGE2_6dB	, ACMCODEC_ADC_DIGGAIN_M24dB	, ACMCODEC_SIDETONE_GAIN_M10dB, 0, 0},		//ACMCodec_GainInT	Tx_CodecGain
		0, 						//signed short		Tx_DSPGain
		//ACMCodec_GainOutT Rx_CodecGain[ACM_VOLUME_CNT]
		{{ACMCODEC_DAC_DIGGAIN_M12dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_M3dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 0
		 {ACMCODEC_DAC_DIGGAIN_M8dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_M6dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 1
		 {ACMCODEC_DAC_DIGGAIN_M6dB, ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_1_5VV, 0},// 2
		 {ACMCODEC_DAC_DIGGAIN_M3dB , ACMCODEC_DAC_GAIN_0dB	, ACMCODEC_RCV_GAIN_0dB	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 3
		 {ACMCODEC_DAC_DIGGAIN_0dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 4
		 {ACMCODEC_DAC_DIGGAIN_3dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_3dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_0VV, 0},// 5
		 {ACMCODEC_DAC_DIGGAIN_6dB	, ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 6
		 {ACMCODEC_DAC_DIGGAIN_9dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 7
		 {ACMCODEC_DAC_DIGGAIN_12dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 8
		 {ACMCODEC_DAC_DIGGAIN_15dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 9
		 {ACMCODEC_DAC_DIGGAIN_18dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0},// 10
		 {ACMCODEC_DAC_DIGGAIN_21dB , ACMCODEC_DAC_GAIN_0dB 	, ACMCODEC_RCV_GAIN_6dB 	, ACMCODEC_CLASSG_MODE_1_8V , ACMCODEC_CLASSD_GAIN_2_3VV, 0} // 11
		},
		//signed short	  Rx_DSPGain[ACM_VOLUME_CNT](-36dB ~ 12dB)
		{-36, -31, -27, -24, -21, -18, -15, -12, -9, -6, -3, 0},
		0xFFFF						//signed short		Rx_DSPSideToneGain
	} //AC_FM	
#endif //CRANEGT_DDR
#endif   //ASR_AUDIO_NVM_STRIP
};


const ACMDSP_VEParametersT g_AudioDefaultVETable[] = 
{
	{
		VC_HANDSET,
      	ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_SETTING,	    //VC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//VC_HANDSET

        
#ifndef ASR_AUDIO_NVM_STRIP	

	{
		VC_HANDSFREE,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_SETTING,	    //VC Control

     	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//VC_HANDSFREE


	{
		VC_HEADSET,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_SETTING,	    //VC Control

     	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//VC_HEADSET


	{
		VC_HEADPHONE,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_SETTING,	    //VC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//VC_HEADPHONE	


	{
		VC_BT,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_BT_SETTING,	    //VC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//VC_BT


	/*************************************************************************************
			                            Audio scenario
	**************************************************************************************/


    {
		AC_HANDSET,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_AUDIO_SETTING,	    //AC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//AC_HANDSET


	{
		AC_HANDSFREE,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_AUDIO_SETTING,	    //AC Control

     	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//AC_HANDSFREE


	{
		AC_HEADSET,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_AUDIO_SETTING,	    //AC Control

     	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//AC_HEADSET


	{
		AC_HEADPHONE,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_AUDIO_SETTING,	    //AC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	},	//AC_HEADPHONE	


	{
		AC_BT,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
      	ASR_BOARD_DSP_BT_SETTING,	    //AC Control

    	/*************************************************************************************
    			TX path
		**************************************************************************************/
   		// ExpertParam_1	
       	{0,0},
   		//EC parameters	
		{
			{0, 	40,    7,	0,	16000 , 0},	  	//8k
			{0, 	80,    7,   4,  16131 , 0}		//16k
		},
   		//TX NS&RES parameters.	
		{
			{{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},	 {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
			{{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},	 {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}}	
		},
   		//TxFreq_eqCoeffArray	
        {	
			{0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			 0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},	//8K
			{0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			 0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			 0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},	//16K
		},
   		//TX AgcParams
		{	
			{1, 3, -3,	 -3,	-70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
			{1, 6, -2,	 -2,	-60, 4, 35 ,  0,  -2, 2, -15,  20, 68}	//16K
		},
	
    	/*************************************************************************************
    			RX path
		**************************************************************************************/
		// ExpertParam_2	
  	  	{0,0},
      	// RX ns  	        
		{
		      {-15, -45, 0xC33, 18022, 0},   //8k
		      {-15, -45, 0xA53, 18022, 0}    //16k
         },
      	//RxFreq_eqCoeffArray	
		{	
			 {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
			  0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
			 {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
			  0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
			  0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
		},	
     	//Rx AgcParams
		{	
			{1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},	// 8K
			{1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}	//16K
		},		
    	// BiQuadIIR
		{
			{ //8k
				{4096,       -7247,        3270},	     // a[0], a[1], a[2]
				{3570,       -7140,        3570}		 // b[0], b[1], b[2]
			},
			{ //16k
				{4096,		  6236, 	   2633},	     // a[0], a[1], a[2]
				{3060,		  6120, 	   3060}		 // b[0], b[1], b[2]
			}
		},	
		//SlowVoiceParam
		{9,  32767*9/10,  0x5000,  0},
		
		// Reserved[6]
		{0,0,0,0,0,0},
		//Tag
		ASR_BOARD_VE_TAG
	}	//AC_BT
        
#ifdef AUDIO_VAD_ENABLE

    ,{
        AC_KWS,
        ASR_AUDIO_MAIN_VERSION | (ASR_AUDIO_SUB_VERSION << 8),//version
        ASR_BOARD_DSP_BT_SETTING,       //AC Control
    
        /*************************************************************************************
                TX path
        **************************************************************************************/
        // ExpertParam_1    
        {0,0},
        //EC parameters 
        {
            {0,     40,    7,   0,  16000 , 0},     //8k
            {0,     80,    7,   4,  16131 , 0}      //16k
        },
        //TX NS&RES parameters. 
        {
            {{-18, -39, 0xA53, 18022, 0}, {2, -2,-4,-88,   1, 40, 0x1500, 0x1200,  0x3266, 100, 0x467, 2, 8,  0},    {28, 12, 0x0800,  64, 30, 16, 16, 12, -18}},
            {{-16, -39, 0xA53, 18022, 0}, {2, -1,-1,-87,  -1, 33, 0x2000, 0x1000,  0x8754, 100, 0x467, 2, 8,  0},    {56, 14, 0x1100,  64, 30, 20, 20,  9, 10}} 
        },
        //TxFreq_eqCoeffArray   
        {   
            {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
             0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
             0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
            {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
             0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
             0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
        },
        //TX AgcParams
        {   
            {1, 3, -3,   -3,    -70, 3, 60,   0,  -2, 2, -15,  20, 68}, // 8K
            {1, 6, -2,   -2,    -60, 4, 35 ,  0,  -2, 2, -15,  20, 68}  //16K
        },
    
        /*************************************************************************************
                RX path
        **************************************************************************************/
        // ExpertParam_2    
        {0,0},
        // RX ns            
        {
              {-15, -45, 0xC33, 18022, 0},   //8k
              {-15, -45, 0xA53, 18022, 0}    //16k
         },
        //RxFreq_eqCoeffArray   
        {   
             {0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
              0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000, 
              0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000,0x4000},  //8K
             {0x2000,0x4000,0x4000,0x4000,0x4000,0x4000,0x5000,0x5800, 
              0x5800,0x5000,0x3000,0x5000,0x5800,0x5000,0x4800,0x4400, 
              0x4000,0x4000,0x4000,0x3800,0x3000,0x3000,0x3000,0x3000},  //16K
        },  
        //Rx AgcParams
        {   
            {1, 4, -2, 1, -75, 3, 65, 0, -2, 2, -10,  20, 68},  // 8K
            {1, 3, -2, 2, -75, 4, 33, 0, -2, 2, -10,  20, 68}   //16K
        },      
        // BiQuadIIR
        {
            { //8k
                {4096,       -7247,        3270},        // a[0], a[1], a[2]
                {3570,       -7140,        3570}         // b[0], b[1], b[2]
            },
            { //16k
                {4096,        6236,        2633},        // a[0], a[1], a[2]
                {3060,        6120,        3060}         // b[0], b[1], b[2]
            }
        },  
        //SlowVoiceParam
        {9,  32767*9/10,  0x5000,  0},
        
        // Reserved[6]
        {0,0,0,0,0,0},
        //Tag
        ASR_BOARD_VE_TAG
    }   //AC_KWS
#endif  //AUDIO_VAD_ENABLE
#endif //ASR_AUDIO_NVM_STRIP
};
#endif

const ACMCP_MediaVEarametersT g_AudioDefaultMediaVETable[] ={
    {   .index = 0,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },

            
#ifndef ASR_AUDIO_NVM_STRIP	

    {   .index = 1,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 2,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 3,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 4,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 5,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 6,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 7,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 8,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 9,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
    {   .index = 10,
        .onoffCtl = ASR_BOARD_MEDIA_VE_SETTING, // bit0:eq,  bit1: drc and HFP, bit2: spkPro
        .eqParam = {
                // unsigned short BiquadEnControl; 
            //bit 0 ~bit 4 is on/off of each filter.
            0x1,
                //unsigned short reserved0;
                //short     reserve1;
            // BiQuadCoeffsStruct Tuningmem[5]; 
            //get from user input or read workspace log
            {   //for example a[0] =8192  norm_s(a[0])-1 = 2;
                {2, -7989, 3106, 4425, -3994, 1224},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
                {1,  6367, 255 , 5590, 3184,  2729},
            }
        },
        .drcParam = {
            .drcGain = 4,
            .cutOffFreqIdx = 1,
        },
        .spkProParam = {
            .maxRms = 0,
            .Alpha = 0,
            .N2cos = 0,
        },
        .Tag = ASR_BOARD_VE_TAG,
        
    },
#endif
};

/***************************************************************************************************
    Expose a NVM for customer to configure the behavior of CP audio
****************************************************************************************************/
const ACM_Configuration AudioConfigure[] = 
{    { 0x08,                1,               0,            {"Audio Configuration"}}};
//Configure   Always_Print_PCM    Disable_All_Modules

