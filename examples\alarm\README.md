# RTC-ALARM闹钟接口简介 

## **RTC-ALARM介绍**
RTC是实时时钟（Real-Time Clock）的缩写，它是一种能够持续跟踪时间的计时器。
RTC是一个独立的定时器，它拥有一个连续计数的计数器。
在相应的软件配置下，RTC可以提供时钟日历的功能，包括年、月、日、时、分、秒等时间信息。
通过修改计数器的值，可以重新设置当前的时间和日期。
即使在设备断电的情况下，只要芯片的备用电源一直供电，RTC上的时间也会一直走，这是其显著特点。

## **实现功能**
1. 实现闹钟定时功能，定时时间根据CM_ALARM_DEMO_WAKEUP_TIME_S确定
2. 在不断电情况下，模组内部RTC仍然运行，到达闹钟时间后，自动触发回调。

## **APP执行流程**
1. 创建闹钟RTC测试主线程
2. 等待网络注册成功，自动将网络时间同步至RTC中
3. 根据CM_ALARM_DEMO_WAKEUP_TIME_S（相对时间），计算得到闹钟唤醒预定时间（绝对时间），单位秒，并通过换算成年月日时分秒格式
4. 设置闹钟到达回调，设置闹钟时间，打开闹钟
5. 等待闹钟定时到达


## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 问题：为什么无法实现闹钟定时开机效果？
  答复：目前闹钟开机功能与powerkey开关机键存在互斥，不支持闹钟定时开机。

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/5 17:30
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------