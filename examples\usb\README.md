# 简易USB接口测试 简介 

## USB介绍**
USB（通用串行总线）是一种外部总线标准，用于规范计算机与外部设备的连接和通信。它是一种新型数据通信方式，逐渐取代其他接口标准。USB具有传输速度快、使用方便、支持热插拔、连接灵活、独立供电等优点，可连接多种外设。计算机等智能设备与外界数据的交互主要以网络和USB接口为主。
本示例中USB接口可例化成串口使用，可用于数据交互。


## **实现功能**
实现将USB接口例化成串口使用，连接电脑设备管理器会出现2个ASR Modem Device 其中一个为ASR Modem Device 2为USB例化串口，可进行本demo中的回显测试
1. 实现USB数据的收发回显，即USB收到数据，并将数据发回至USB
2. 实现USB的插拔检测

## **APP执行流程**
1. 初始化USB数据接收回调及插拔事件回调
2. 创建线程用于处理USB的事件：1、收到USB数据APP_DEMO_USB_EVENT_RECV、2、USB插入APP_DEMO_USB_EVENT_INSERT、3、USB拔出APP_DEMO_USB_EVENT_REMOVE
3. 回显USB收到的数据

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：回显函数使用了cm_log_printf,由于该函数限制至多打印108字节，因此当产生回显数据大于108字节时，无法打印数据至USB。但实际不影响USB接收数据，该示例设置的最大长度为1K，可根据实际内存情况调整大小
- APP使用前提：无

## **FAQ**
- 问题：为什么直接使用USB输出数据，无数据打印？
  答复：注意该USB存在一特性：需先以某波特率发任意字节给模组，进行波特率适配，模组才能通过USB往外输出数据

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/5 17:30
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------