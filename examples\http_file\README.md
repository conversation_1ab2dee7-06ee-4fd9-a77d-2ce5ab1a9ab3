# http 文件

## **http介绍**

‌HTTP协议‌，全称为Hypertext Transfer Protocol，即超文本传输协议，是互联网上应用最为广泛的一种网络传输协议。它定义了客户端（如Web浏览器）与服务器之间的通信规范，主要用于传输超文本（如HTML文档）以及其他类型的数据（如图片、视频、文件等）。
HTTP上传文件有多种方式，其中Multipart/form-data是常用的POST方式之一。在这种方式下，数据会被分隔为多个部分，每个部分都有自己的头信息和内容。文件数据会以二进制的形式发送到服务器。这种方式适用于上传文件、图片等资源。

HTTP分片下载是一种下载文件的技术，HTTP允许将一个大文件分成多个小块（分片），然后分别下载这些分片，从而实现更快速、稳定的下载过程。

## **实现功能**
本APP实现http文件的上传和下载
1. 使用http POST上传一份amr音频文件到http服务器上；
2. 使用http GET从http服务器分包下载文件。


## **APP执行流程**

1. 进入http 文件测试线程，然后等待模组pdp激活；
2. 进入cm_http_file_post函数，实现代码中amr文件上传：申请文件上传时http body数据的缓冲区大小；创建http实例；配置http参数；设置http header，以Multipart/form-data方式上传，还设置文件名等；封装http body，文件内容封装在里面；发起http同步请求；最后打印请求结果。
3. 进入cm_http_file_get函数，实现http分包下载：创建http实例；配置http参数；设置header，并发起http同步请求，尝试先下载1字节数据，获取文件的大小；设置header，开始分包下载数据，并打印下载累计长度；最后下载完成，打印下载长度。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：无
- 使用注意事项：  
    1. 测试本app请确认服务器支持文件上传和分包下载；
    2. 测试本app请填写HTTP_FILE_TEST_SERVER（http文件测试的服务器地址和端口）、HTTP_FILE_TEST_POST_URL（上传文件的url）、HTTP_FILE_TEST_GET_URL（下载文件的url）；
    3. 文件上传的示例在我司的http测试服务器测试正常，若用户调试异常，可以尝试使用电脑上传时，使用wireshark抓包，看http上传的参数，根据参数修改代码；
    4. 分包下载分包下载一定要先确认文件的大小，可能尝试下载一小段数据（本示例是下载1字节），然后从http响应的header中找到“Content-Range”关键字，后面的数值就是文件的大小（具体可以看http相关协议）。

## **FAQ（非必要，视客户/FAE咨询情况增列）**

- 无

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/18 18:04
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------