# 简易spi操作flash简介 

## **模组spi功能介绍**
本功能模块通spi接口驱动flash，主要目的是展示模组作为主设备spi功能模块接口的使用方法;
本模组的只能作为主设备，不能作为从设备，四线制（CLK+CS+MISO+MOSI),支持特定波特率、SPI的NSS管理仅支持硬件控制、特定的SPI传输数据位宽度位数，详见cm_flash.h；
本模组的spi为标准协议，这里不再赘述。

## **实现功能**
实现模组通过spi与flash的通讯，包括以下子功能：
1. 调用IOMUX设置相关引脚功能为spi；
2. 打开spi,设置相关参数；
3. 读取flash ID，确保通讯正常；
4. 写入测试数据；
5. 读取写入的数据；
6. 关闭spi。

## **APP执行流程**
1. 设备上电，创建spi操作flash任务，该任务间隔特定时间操作flash 1次；
2. 设置相关引脚功能为spi；
3. 打开spi,设置相关参数；
4. 读取flash ID，确保通讯正常；
5. 写入测试数据；
6. 读取写入的数据；
7. 关闭spi。

## **使用说明**
- 支持的模组（子）型号：ML307R各子型号/ML307C-DC-CN；
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0/ML307C OpenCPU SDK 1.0.0版本及其后续版本；
- 是否需要外设支撑：必须接flash，如果是其它外设，请阅读相关外设芯片手册调整相关配置参数；
- 使用注意事项：1.确保硬件连接无误；2.确保外设芯片与功能模块中的一致；
- APP使用前提：模组开发板及其外设flash芯片；

## **FAQ**
- 问题：除了关注cm_spi.h中的接口函数，还需要注意什么？
  答复：1.需调用cm_iomux.h文件引脚配置接口设置选择引脚为spi功能；
        2.需确保模组和芯片供电稳定和引脚连接正确，可用示波器抓取波形进行相关参数分析；

## **版本更新说明**

### **1.0.1版本**
- 发布时间：2024/12/24 11:32
- 修改记录：
  1. 新增支持的模组（子）型号以及支持的SDK版本

### **1.0.0版本**
- 发布时间：2024/11/05 10:30
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------