/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*********************************************************************
* Filename:     AUX_TABLE_TxApcParameters_CalBody.h
* Created By:   <PERSON><PERSON> meltser on 6/18/2008
* Programmers:  Elad Goldman
* Module:       Generic RF Module - Data Base
* RF Type:      SKY_Hel3
* DB Ver.:      0.9
* Doc Ver.:     2.94
* GUI Ver.:     7.26
*
* Description:  Contains the "AUX_TABLE_TxApcParameters" CalBody
*
* Notes:        This file is automatically generated by WIZARD.
*********************************************************************/

/*
 * each line in the file contain the data of 5*Int16:
*/
//|-BandZoneSize-----------|-TempZoneSize-----------|-Min_PCL----------------|-Min_Temperature--------|-Max_Temperature--------|-fixedIndex1------------|-fixedIndex0------------|
   11,                      25,                      5,                       243,                     343,                     // EGSM_BAND,                GMSK_MODULATION,         
   11,                      25,                      5,                       243,                     343,                     // EGSM_BAND,                PSK8_MODULATION,         

   24,                      25,                      0,                       243,                     343,                     // DCS_BAND,                 GMSK_MODULATION,         
   24,                      25,                      0,                       243,                     343,                     // DCS_BAND,                 PSK8_MODULATION,         

   19,                      25,                      0,                       243,                     343,                     // PCS_BAND,                 GMSK_MODULATION,         
   19,                      25,                      0,                       243,                     343,                     // PCS_BAND,                 PSK8_MODULATION,         

   8,                       25,                      5,                       243,                     343,                     // GSM_850_BAND,             GMSK_MODULATION,         
   8,                       25,                      5,                       243,                     343,                     // GSM_850_BAND,             PSK8_MODULATION,         

