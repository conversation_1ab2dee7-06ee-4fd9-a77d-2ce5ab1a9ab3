/*
 * @Author: <PERSON><PERSON><PERSON> l<PERSON>@tengen.com.cn
 * @Date: 2023-06-20 00:56:38
 * @LastEditors: ji<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-17 14:01:22
 * @FilePath: \ml307_dcln\custom\custom_main\inc\modbus_485_poll.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef MODBUS_485_POLL_H
#define MODBUS_485_POLL_H

void modbus_loop_485_poll(void);
#endif // __MODBUS_485_POLL_H