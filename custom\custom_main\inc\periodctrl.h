/*********************************************************
*  @file    periodctrl.h
*  @brief   ML302 OpenCPU aliyun topic period ctrl file
*  Copyright (c) 2021 TENGEN.
*  All rights reserved.
*  created by Delei 2021/03/25
********************************************************/
#ifndef _PERIODCTRL_H_
#define _PERIODCTRL_H_


#include "core_list.h"



#define MAX_FAULT_COUNT                    2
#define TIME_MAX_NUM                       48 //本地定时最多数量
#define SECOND_OF_DAY                      (24*60*60)

//周期控制中，进入/退出屏蔽时段表发生的动作
#define INFO_TIME_NO_CTRL       1
//周期控制中，到达指定的时间点，执行的动作
#define INFO_TIME_PERIOD        2
//用户通过云端下发的分闸动作
#define INFO_USER_CTRL_0        3
//用户通过云端下发的合闸动作
#define INFO_USER_CTRL_1        4
//一键分合闸中，触发了分闸
#define INFO_ONEKEY_CTRL_0      5
//一键分合闸中，触发了合闸
#define INFO_ONEKEY_CTRL_1      6


typedef struct 
{
    /* data */
    int breakid; /* breakid 因为表地址会变化，这里需要对应到BreakId*/
    int year;/* 年 */
    int mon; /* 月 */
    int day; /* 日 */
    int hour;/* 时 */
    int min;/* 分 */
    int val;/* 分合闸  0:分闸  1：合闸 */
    uint16_t status;/* 定时任务状态 0:Inactive 1:active 2:idle*/
    uint16_t type;/* 定时类型  0:单次  1:重复每天  */
}__packed GW_TIMER;

typedef struct cm_tm {
    int tm_sec; /* 秒 – 取值区间为[0,59] */   
    int tm_min; /* 分 - 取值区间为[0,59] */
    int tm_hour; /* 时 - 取值区间为[0,23] */
    int tm_mday; /* 一个月中的日期 - 取值区间为[1,31] */
    int tm_mon; /* 月份（从一月开始，0代表一月） - 取值区间为[0,11] */
    int tm_year; /* 年份，其值等于实际年份减去1900 */
    int	tm_wday; /*星期*/
} cm_tm_t;

typedef struct
{
    GW_TIMER TaskList[TIME_MAX_NUM]; /* 定义任务条数 */
    int TaskNum;/* 有效定时任务的数量 */
    int TaskSN[TIME_MAX_NUM];/* 存贮定时任务的序号 */
}__packed GW_TIME_TASK;


//Flash中的记忆体(时间表和时间屏蔽表在一个区中，最多32个区，用于保存最多32个微断的时间表)[靠左对齐]
//512*32 = 16384Bytes = 16KBytes
typedef struct
{
    uint32_t loopMod;//循环模式:1代表日重复，2代表周重复(第8~15位，为1的位，分别表示从周日到周六，哪些天与当前时间表相同，第8位表示周日)
    uint32_t breakId;
    uint32_t enClass[2];//使能位，1代表使能(第几位就代表第几个时间点的使能情况, enClass[0]是高32位)
    uint32_t actClass[2];//某个时间点需要执行的动作0代表分闸，1代表合闸(第几位就代表第几个时间点的动作, actClass[0]是高32位)
    uint32_t timePoint[56];//时间点表(相对时间),没有内容的地方，其值为0xffffffff(即-1),最小单位s，精度min。[靠左对齐]

    uint32_t timeNo[64];//时间点屏蔽表(绝对时间),最小单位s，精度min，默认值为0，无效值为0。[靠左对齐]
    uint32_t timeNoBegin;//时间点屏蔽有效时，开始的动作，0代表分闸，1代表合闸，最多32个位
    uint32_t timeNoEnd;//时间点屏蔽有效时，结束的动作，0代表分闸，1代表合闸，最多32个位
}TIME_TAB_FLASH;//size:512U

//RAM中的临时时间表(保存了每个微断，在某一天中的全部有效时间点)
typedef struct
{
    uint32_t breakId;
    uint32_t timePoint[56];//时间执行表(表内所有相对时间点都是有效相对时间点),默认值为-1，无效值为0xffffffff(即-1),最小单位s，精度min
    uint32_t actClass[2];//某个时间点需要执行的动作0代表分闸，1代表合闸
    uint32_t oveClass[2];//该时间点的动作已经被成功执行，则对应位值1  
    uint32_t failCnt;//临时记录某个时间点被执行的失败次数，超过3次，将触发告警

    uint32_t timeNo[64];//时间点屏蔽表(绝对时间)，默认值为0，无效值为0。[靠左对齐]
    uint32_t timeNoBegin;//时间点屏蔽有效时，开始的动作，0代表分闸，1代表合闸，最多32个位，低位表示第0个时段
    uint32_t timeNoEnd;//时间点屏蔽有效时，结束的动作，0代表分闸，1代表合闸，最多32个位，低位表示第0个时段
    int timeNoFlg;//用于记录当前的微断是否进入了屏蔽时段
    int timeNoFlgOld;//用于记录上次微断是否进入了屏蔽时段

    struct core_list_head timeListNode;
}TIME_TAB_DAY;


void check_bk_ctrl(void *arug);
void clearParametersForPer(void);
void cm_sec_to_date(long lSec, cm_tm_t *tTime);

void check_bk_ctrl1(void *arug);


void sendAllTimeTablesForBreaks(void);

void InitPeriodCtrl();
#endif
