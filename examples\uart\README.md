# uart  

## **uart功能介绍**

串口通信是一种设备间常用的串行通信方式，串口按位（bit）发送和接收字节，可以在使用一根线发送数据的同时用另一根线接收数据。同时串口通信协议是指规定了数据包的内容，内容包含了起始位、主体数据、校验位及停止位，双方需要约定一致的数据包格式才能正常收发数据的有关规范。

## **实现功能**
本APP实现uart发送和接收操作示例
1. 实现串口数据按时间分包接收，即多久没有新的串口数据到来后或者超过自行规定的缓冲区大小就认为是一包数据；
2. 实现串口发给模组什么数据模组就回复什么数据。


## **APP执行流程**

1. 进入uart测试线程，根据测试的串口ID设置对应串口引脚的GPIO复用；
2. 创建串口通知信号量和串口事件接收线程，串口通知信号量用于通知线程有新的串口数据需要获取；串口事件用于通知线程有底层串口队列溢出或者流控事件；
3. 注册串口事件参数：如事件触发类型、参数和串口事件回调函数；
4. 初始化串口配置参数，其中初始化波特率为115200；
5. 串口输出初始化完成的日志；
6. 申请用户层串口接收数据缓冲区的大小，然后进入循环，持续等待串口通知信号量，该信号量在串口回调函数cm_serial_uart_callback提示有新的串口数据到来后触发，线程中接收到信号量，退出阻塞，开始读取串口数据，直到第一次读取的数据为0或者异常后，再延时UART_PAKCET_TIME时间后，进行第二次读取，若还是为0或者异常，则认为是一包数据，输出接收到的数据；若读取的数据超过自定义的缓冲区大小，则也认为是一包数据，输出接收到的数据并重新继续接收。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：无
- 使用注意事项：  
    1. 如要使用UART2（即debug口），需要使用虚拟AT指令“AT+MCFG=log2cat,1”先将log打印从debug切换到usb，注意debug口开机会打印开机log，无法过滤；
    2. 串口回调函数中不可输出LOG、串口打印、执行复杂任务或消耗过多资源，建议以信号量或消息队列形式控制其他线程执行任务。

## **FAQ（非必要，视客户/FAE咨询情况增列）**

- 无

## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/18 19:07
- 修改记录：
  1. 初版


--------------------------------------------------------------------------------