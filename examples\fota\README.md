# FOTA接口测试 简介 

## FOTA介绍**
FOTA（Firmware Over-The-Air）升级，即固件空中升级，是一种通过云端升级技术为具有联网功能的设备提供固件升级服务的方式。
本模组支持最小系统升级、全系统差分升级、APP整包升级三种方案。

最小系统升级方案下，系统分为可连网的最小系统和非最小系统（用户开发的应用APP代码均属于非最小
系统），固件升级时两个系统均需要升级。升级时首先下载最小系统差分文件，下载完成后重启进入升级。完
成最小系统的升级后重启运行的是升级后的可连网的最小系统，然后再下载非最小系统升级文件，该文件是全
包非差分文件，升级时直接覆盖非最小系统区域，下载完成后校验，校验通过后整个升级完成。

升级包生成方式请参考手册《ML307R_固件升级开发指导手册》

## **实现功能**
1. 提供三种最小系统升级、全系统差分升级、APP整包升级三种FOTA示例代码
2. 提供HTTP的下载示例，功能包含获取固件包大小，部分下载，全量下载。
3. 注意在本示例中，采用最小系统升级和APP整包升级时，模组升级成功后，会再次下载升级包，并反复升级，为正常现象。而全系统差分升级，升级一次成功后，会校验到升级包为旧升级包，而停止升级。

## **APP执行流程**
1. 进入fota测试线程，等待模组pdp激活
2. 根据APP_DEMO_FOTA_MODE设置，选择FOTA方式，0-最小系统 1-全系统差分升级/APP整包升级
3. 在最小系统升级下，会校验URL是否合法，并优先通过下载10个字节确认差分包是可被下载的。
4. 注册FOTA回调，并触发升级，若固件包无误，模组会自动重启进行升级，升级完成后自动开机。
5. 在全系统差分升级/APP整包升级下，先初始化FOTA并擦除过往的固件区。
6. 获取固件包大小，用于设置cm_ota_set_otasize大小。在获取固件包大小中会使用HTTP的Range请求，获取报头中的整个固件包大小
7. 根据APP_FOTA_PER_PACKET_LEN大小分包下载固件包，下载一包写入一包，一旦有下载失败则擦除原有的固件区，并退出。
8. 当写入字节数与设定的固件大小一致时，判断为固件包已全部下载完成。
9. 触发升级，若固件包无误，模组会自动重启进行升级，升级完成后自动开机。

## **使用说明**
- 支持的模组（子）型号：ML307R-DC
- 支持的SDK版本：ML307R OpenCPU SDK 2.0.0版本及其后续版本
- 是否需要外设支撑：不需要
- 使用注意事项：
- APP使用前提：无

## **FAQ**
- 问题：最小系统升级方案和全系统差分升级/APP整包升级，是否能共用一个接口？
  答复：不可以，最小系统升级方案中的API接口请参考cm_fota.h。全系统差分升级/APP整包升级请参考cm_ota.h。 两者不能混用，并严格按照《ML307R_固件升级开发指导手册》中生成固件升级包。

- 问题：最小系统升级方案报855错误？
  答复：常见错误原因是传入的URL不支持Range下载，即断点续传功能，同时模组也不支持重定向的URL。
        也有可能是因为升级包生成错误导致，请严格按照手册进行生成。


## **版本更新说明**

### **1.0.0版本**
- 发布时间：2024/11/11 18:08
- 修改记录：
  1. 初版

--------------------------------------------------------------------------------